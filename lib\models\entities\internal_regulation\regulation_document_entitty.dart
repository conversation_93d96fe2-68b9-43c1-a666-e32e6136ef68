import 'package:json_annotation/json_annotation.dart';
import 's3_object_entity.dart';

part 'regulation_document_entitty.g.dart';

@JsonSerializable()
class RegulationDocumentEntity {

  @Json<PERSON>ey()
  List<S3ObjectEntity>? attachmentFile;

  @<PERSON><PERSON><PERSON>ey()
  String? id;

  @J<PERSON><PERSON>ey()
  String? title;

  @Json<PERSON>ey()
  String? content;

  @Json<PERSON>ey()
  String? status;

  @Json<PERSON>ey()
  String? projectId;

  @J<PERSON><PERSON><PERSON>()
  String? documentFolderId;

  @JsonKey()
  S3ObjectEntity? thumbnailImage;

  @J<PERSON><PERSON>ey()
  S3ObjectEntity? contentMedia;

  @J<PERSON><PERSON><PERSON>()
  String? createdDate;

  RegulationDocumentEntity({
    this.attachmentFile,
    this.id,
    this.title,
    this.content,
    this.status,
    this.projectId,
    this.documentFolderId,
    this.thumbnailImage,
    this.contentMedia,
    this.createdDate,
  });

  factory RegulationDocumentEntity.fromJson(Map<String, dynamic> json) => _$RegulationDocumentEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$RegulationDocumentEntityToJson(this);
}
