import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/main.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/repositories/file_repository.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/my_app_bar.dart';
import 'package:real_care/ui/dialogs/app_dialog.dart';
import 'package:real_care/ui/dialogs/file_picker_dialog.dart';
import 'package:real_care/ui/pages/profile/profile_info/profile_info_cubit.dart';
import 'package:real_care/ui/pages/profile/widgets/loading_profile_header_widget.dart';
import 'package:real_care/ui/pages/profile/widgets/loading_row_profile_info_widget.dart';
import 'package:real_care/ui/pages/profile/widgets/profile_header_widget.dart';
import 'package:real_care/ui/pages/profile/widgets/row_profile_info_widget.dart';
import 'package:real_care/ui/widgets/app_snackbar.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:real_care/utils/dialog_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

class ProfileInfoPage extends StatefulWidget {
  @override
  _ProfileInfoPageState createState() => _ProfileInfoPageState();
}

class _ProfileInfoPageState extends State<ProfileInfoPage> {
  AppCubit? _appCubit;
  late ProfileInfoCubit _profileInfoCubit;
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // _cubit = BlocProvider.of<ProfileInfoCubit>(context);
    _appCubit = BlocProvider.of<AppCubit>(context);

    final uploadRepository = RepositoryProvider.of<UploadRepository>(context);
    _profileInfoCubit = ProfileInfoCubit(
      uploadRepo: uploadRepository,
    );
    _profileInfoCubit.stream.listen((state) {
      if (state.uploadAvatarStatus == LoadStatus.SUCCESS) {
        _appCubit!.uploadAvatarSuccess(state.avatar);
      } else {
        if (state.uploadAvatarStatus == LoadStatus.FAILURE) {
          _appCubit!.uploadAvatarFailure();
          _showMessage(SnackBarMessage(
            message: S.of(context).text_avatar_upload_failed,
            type: SnackBarType.ERROR,
          ));
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: Stack(
        children: [
          _buildBackgroundWidget(),
          SafeArea(
            child: _buildBodyWidget(),
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: double.infinity,
          child: Image.asset(
            AppImages.bgHomeHeader,
            fit: BoxFit.fitWidth,
          ),
        ),
      ],
    );
  }

  Widget _buildBodyWidget() {
    return Column(
      children: [
        MyAppBar(
          title: S.of(context).profile_infomation,
          onBackPressed: () {
            Navigator.of(context).pop();
          },
        ),
        Expanded(
          child: BlocBuilder<AppCubit, AppState>(
            bloc: _appCubit,
            buildWhen: (previous, current) =>
                previous.user != current.user ||
                previous.uploadAvatarStatus != current.uploadAvatarStatus,
            builder: (context, state) {
              return Column(
                children: [
                  ProfileHeaderWidget(
                    onCameraPressed: _handleCameraPressed,
                    onVerifyPressed: _handleVerifyPressed,
                    userEntity: state.user,
                    isLoad: state.uploadAvatarStatus == LoadStatus.LOADING
                        ? true
                        : false,
                  ),
                  SizedBox(height: 5),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _onRefreshData,
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // [Fix]: Fix later
                            // state.user?.verifyImages != null
                            //     ? SizedBox(height: 0)
                            //     : SizedBox(height: 20),
                            Container(
                              child: _buildListInformation(state.user),
                              color: Colors.white,
                            ),
                            SizedBox(height: 25),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 15),
                              child: AppTintButton(
                                title: S.of(context).update_profile_info,
                                onPressed: _handlerUpdateInfo,
                              ),
                            ),
                            SizedBox(height: 10),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 15),
                              child: AppWhiteCustomButton(
                                title: S.of(context).change_password,
                                onPressed: _handlerChangePassword,
                              ),
                            ),
                            SizedBox(height: 25),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
              // if (state.getUserInfoStatus == LoadStatus.LOADING) {
              //   return _buildLoadingWidget();
              // } else if (state.getUserInfoStatus == LoadStatus.FAILURE) {
              //   return ErrorListWidget(
              //     onRefresh: _onRefreshData,
              //   );
              // } else {
              //   return
              // }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    List<Widget> listInfoWidget = [];
    listInfoWidget.add(SizedBox(height: 25));
    listInfoWidget
        .add(LoadingRowProfileInfoWidget(title: S.of(context).request_name));
    listInfoWidget.add(
        LoadingRowProfileInfoWidget(title: S.of(context).profile_date_birth));
    listInfoWidget
        .add(LoadingRowProfileInfoWidget(title: S.of(context).profile_gender));
    listInfoWidget
        .add(LoadingRowProfileInfoWidget(title: S.of(context).profile_phone));
    listInfoWidget
        .add(LoadingRowProfileInfoWidget(title: S.of(context).profile_email));
    listInfoWidget.add(
        LoadingRowProfileInfoWidget(title: S.of(context).profile_identities));
    listInfoWidget.add(LoadingRowProfileInfoWidget(
        title: S.of(context).profile_identities_date));
    listInfoWidget
        .add(LoadingRowProfileInfoWidget(title: S.of(context).profile_place));
    listInfoWidget
        .add(LoadingRowProfileInfoWidget(title: S.of(context).profile_address));
    listInfoWidget.add(
        LoadingRowProfileInfoWidget(title: S.of(context).permanent_address));
    listInfoWidget.add(SizedBox(height: 25));
    return Column(
      children: [
        LoadingProfileHeaderWidget(),
        Expanded(
          child: SingleChildScrollView(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                children: listInfoWidget,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildListInformation(UserEntity? userEntity) {
    List<Widget> listInfoWidget = [];
    listInfoWidget.add(RowProfileInfoWidget(
        title: S.of(context).request_name,
        information: userEntity?.name ?? ""));
    listInfoWidget.add(RowProfileInfoWidget(
        title: S.of(context).profile_date_birth,
        information: userEntity?.dob?.toDateString() ?? ""));
    listInfoWidget.add(RowProfileInfoWidget(
        title: S.of(context).profile_gender,
        information: userEntity?.genderType?.displayText ?? ""));
    listInfoWidget.add(RowProfileInfoWidget(
        title: S.of(context).profile_phone,
        information: userEntity?.phone ?? ""));
    listInfoWidget.add(RowProfileInfoWidget(
        title: S.of(context).profile_email,
        information: userEntity?.email ?? ""));
    // [Fix]: Fix later
    // listInfoWidget.add(RowProfileInfoWidget(
    //     title: S.of(context).profile_identities,
    //     information: (userEntity?.identities?.length ?? 0) != 0
    //         ? (userEntity?.identities!.last.value ?? "")
    //         : ""));
    // listInfoWidget.add(
    //   RowProfileInfoWidget(
    //       title: S.of(context).profile_identities_date,
    //       information: DateTimeExtension.fromString(
    //                   (userEntity?.identities?.length ?? 0) != 0
    //                       ? (userEntity?.identities![0].date ?? "")
    //                       : "",
    //                   format: AppConfig.dateAPIFormat)
    //               ?.toDateString() ??
    //           ""),
    // );
    // listInfoWidget.add(RowProfileInfoWidget(
    //     title: S.of(context).profile_place,
    //     information: (userEntity?.identities?.length ?? 0) != 0
    //         ? (userEntity?.identities![0].place ?? "")
    //         : ""));
    // listInfoWidget.add(RowProfileInfoWidget(
    //     title: S.of(context).profile_address,
    //     information: userEntity?.address ?? ""));
    // listInfoWidget.add(RowProfileInfoWidget(
    //     title: S.of(context).permanent_address,
    //     information: userEntity?.rootAddress ?? ""));
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 15),
        child: Column(children: listInfoWidget));
  }

  void _handlerChangePassword() async {
    var result =
        await Application.router!.navigateTo(context, Routes.resetPassword);
    if (result == null) {
      _appCubit!.getNotificationCountUnread();
    }
  }

  void _handlerUpdateInfo() async {
    UserEntity? _user = _appCubit!.state.user;

    // [Fix]: Fix later
    // if ((_user?.verifyAccountStatus == VerifyAccountStatus.APPROVED ||
    //     _user?.verifyAccountStatus == VerifyAccountStatus.WAITING)) {
    //   var result = await Application.router!
    //       .navigateTo(appNavigatorKey.currentContext!, Routes.updateProfile);
    //   if (result == null) {
    //     _appCubit!.getNotificationCountUnread();
    //   }
    // } else {
      AppDialog(
        context: context,
        title: "Vui lòng xác thực tài khoản\nđể chỉnh sửa thông tin.",
        titleStyle: TextStyle(
          fontSize: 15,
        ),
        onDismissed: () {
          Get.back();
        },
        type: DialogType.ERROR,
        onCancelPressed: () {
          Get.back();
        },
        onOkPressed: () {
          _handleVerifyPressed();
        },
        cancelText: "Hủy",
        okText: "Xác thực tài khoản",
        showCloseButton: true,
      ).show();
    // }
  }

  void _handleCameraPressed() async {
    FileInfo? file = await DialogUtils.pickFile(
      context,
      sourceTypes: [
        FileSourceType.camera,
        FileSourceType.library,
      ],
    );

    if (file?.file != null) {
      _appCubit!.uploadAvatar();
      _profileInfoCubit.uploadAvatar(file?.file);
    }
  }

  void _handleVerifyPressed() {
    Application.router!.navigateTo(context, Routes.verifyProfile);
  }

  Future<void> _onRefreshData() async {
    _appCubit!.getProfile();
  }

  void _showMessage(SnackBarMessage message) {
    final context = _scaffoldKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(AppSnackBar(message: message));
    }
  }
}
