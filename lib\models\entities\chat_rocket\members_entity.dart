import 'package:json_annotation/json_annotation.dart';

part 'members_entity.g.dart';

@JsonSerializable()
class MembersEntity {
  @Json<PERSON>ey(name: "_id")
  String? id;
  @Json<PERSON>ey()
  String? username;
  @Json<PERSON>ey()
  String? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: '_updatedAt')
  String? updatedAt;
  @Json<PERSON>ey()
  String? name;

  bool? isSelect;

  MembersEntity({
    this.id,
    this.username,
    this.status,
    this.updatedAt,
    this.name,
    this.isSelect,
  });

  factory MembersEntity.fromJson(Map<String, dynamic> json) => _$MembersEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MembersEntityToJson(this);
}
