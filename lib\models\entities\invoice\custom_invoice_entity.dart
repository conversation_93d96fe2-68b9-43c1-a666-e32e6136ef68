import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'invoice_entity.dart';

part 'custom_invoice_entity.g.dart';

@JsonSerializable()
class CustomInvoiceEntity extends Equatable {
  @Json<PERSON>ey()
  String? date;
  @JsonKey()
  List<InvoiceEntity>? invoiceList;

  CustomInvoiceEntity({
    this.date,
    this.invoiceList,
  });

  factory CustomInvoiceEntity.fromJson(Map<String, dynamic> json) => _$CustomInvoiceEntityFromJson(json);
  Map<String, dynamic> toJson() => _$CustomInvoiceEntityToJson(this);

  CustomInvoiceEntity copyWith({
    String? date,
    List<InvoiceEntity>? invoiceList,
  }) {
    return CustomInvoiceEntity(
      date: date ?? this.date,
      invoiceList: invoiceList ?? this.invoiceList,
    );
  }

  @override
  List<Object?> get props => [
    date,
    invoiceList,
  ];
}