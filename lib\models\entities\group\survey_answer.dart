import 'package:real_care/models/entities/group/group_user_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'survey_answer.g.dart';

@JsonSerializable()
class SurveyAnswer {
  @Json<PERSON>ey()
  bool? isDefault;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? createdDate;
  @Json<PERSON>ey()
  List<GroupUserEntity?>? respondents;
  @JsonKey()
  GroupUserEntity? createdBy;
  @Json<PERSON>ey()
  String? id;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? name;
  @Json<PERSON><PERSON>()
  bool? isVoted;
  @Json<PERSON>ey()
  bool? isVotedLocal;
  @JsonKey()
  int? totalVoted;

  SurveyAnswer({
    this.name,
    this.isVoted,
    this.isDefault,
    this.id,
    this.createdDate,
    this.respondents,
    this.createdBy,
    this.totalVoted,
    this.isVotedLocal,
  });

  SurveyAnswer.fromJson(Map<String, dynamic> json) {
    isDefault = json['isDefault'];
    id = json['id'];
    createdDate = json['createdDate'];
    name = json['name'];
    isVoted = json['isVoted'] ?? false;
    isVotedLocal = json['isVoted'] ?? false;
    totalVoted = json['totalVoted'];
    if (json['respondents'] != null) {
      respondents = [];
      json['respondents'].forEach((v) {
        respondents!.add(GroupUserEntity.fromJson(v));
      });
    }
    createdBy = json['createdBy'] != null ? GroupUserEntity.fromJson(json['createdBy']) : null;
  }

  SurveyAnswer.fromWebSocketJson(Map<String, dynamic> json) {
    isDefault = json['isDefault'];
    id = json['id'];
    createdDate = json['createdDate'];
    name = json['name'];
    totalVoted = json['totalVoted'];

    /// Vì bắn socket cho all user subscribe channel nên isVoted phải tự check.
    if (json['respondents'] != null) {
      respondents = [];
      bool hasVote = false;
      json['respondents'].forEach((e) {
        respondents!.add(GroupUserEntity.fromJson(e));
        if (e["id"] == "1") {
        // if (e["id"] == (GlobalData.instance.myProfile?.id ?? "")) {
          hasVote = true;
        }
      });
      isVoted = hasVote;
      isVotedLocal = hasVote;
    }
    createdBy = json['createdBy'] != null ? GroupUserEntity.fromJson(json['createdBy']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}