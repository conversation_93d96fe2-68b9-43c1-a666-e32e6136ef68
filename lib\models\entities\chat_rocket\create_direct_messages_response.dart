import 'package:real_care/models/entities/chat_rocket/room_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'create_direct_messages_response.g.dart';

@JsonSerializable()
class CreateDirectMessagesResponse {
  @JsonKey()
  RoomEntity? room;
  @JsonKey()
  bool? success;

  CreateDirectMessagesResponse({
    this.room,
    this.success,
  });

  factory CreateDirectMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateDirectMessagesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CreateDirectMessagesResponseToJson(this);
}
