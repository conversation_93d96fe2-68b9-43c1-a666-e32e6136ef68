import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/invoice/degree_calculator_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'service_invoice_detail_entity.g.dart';

@JsonSerializable()
class ServiceInvoiceDetailEntity extends Equatable {
  @Json<PERSON>ey()
  List<DegreeCalculatorEntity>? degreeCalculator;
  @Json<PERSON>ey()
  String? code;
  @JsonKey()
  int? amount;
  @Json<PERSON>ey()
  int? paymentTotal;
  @Json<PERSON>ey()
  int? unitPrice;
  @JsonKey()
  String? name;
  @JsonKey()
  String? id;
  @JsonKey()
  int? firstNumber;
  @JsonKey()
  int? lastNumber;

  ServiceInvoiceDetailEntity({
    this.degreeCalculator,
    this.code,
    this.amount,
    this.paymentTotal,
    this.unitPrice,
    this.name,
    this.id,
    this.firstNumber,
    this.lastNumber,
  });

  factory ServiceInvoiceDetailEntity.fromJson(Map<String, dynamic> json) => _$ServiceInvoiceDetailEntityFromJson(json);
  Map<String, dynamic> toJson() => _$ServiceInvoiceDetailEntityToJson(this);

  ServiceInvoiceDetailEntity copyWith({
    List<DegreeCalculatorEntity>? degreeCalculator,
    String? code,
    int? amount,
    int? paymentTotal,
    int? unitPrice,
    String? name,
    String? id,
    int? firstNumber,
    int? lastNumber,
  }) {
    return ServiceInvoiceDetailEntity(
      degreeCalculator: degreeCalculator ?? this.degreeCalculator,
      code: code ?? this.code,
      amount: amount ?? this.amount,
      paymentTotal: paymentTotal ?? this.paymentTotal,
      unitPrice: unitPrice ?? this.unitPrice,
      name: name ?? this.name,
      id: id ?? this.id,
      firstNumber: firstNumber ?? this.firstNumber,
      lastNumber: lastNumber ?? this.lastNumber,
    );
  }

  @override
  List<Object?> get props => [
    degreeCalculator,
    code,
    amount,
    paymentTotal,
    unitPrice,
    name,
    id,
    firstNumber,
    lastNumber,
  ];
}