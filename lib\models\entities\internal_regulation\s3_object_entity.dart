import 'package:json_annotation/json_annotation.dart';

part 's3_object_entity.g.dart';

@JsonSerializable()
class S3ObjectEntity {

  @Json<PERSON>ey()
  String? fileType;

  @Json<PERSON>ey()
  String? originalName;

  @Json<PERSON>ey()
  String? uploadName;

  @J<PERSON><PERSON>ey()
  String? url;

  @J<PERSON><PERSON>ey()
  String? absoluteUrl;

  S3ObjectEntity({
    this.fileType,
    this.originalName,
    this.uploadName,
    this.url,
    this.absoluteUrl
  });

  factory S3ObjectEntity.fromJson(Map<String, dynamic> json) => _$S3ObjectEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$S3ObjectEntityToJson(this);
}
