import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:real_care/models/entities/service_request/common_sr_entity.dart';

part 'service_requests_entity.g.dart';

@JsonSerializable()
class ServiceRequestsEntity {
  @Json<PERSON>ey(name: "new")
  List<CommonSREntity>? newList;
  @<PERSON><PERSON><PERSON><PERSON>(name: "processing")
  List<CommonSREntity>? processing;
  @Json<PERSON>ey(name: "cancel")
  List<CommonSREntity>? cancel;
  @J<PERSON><PERSON><PERSON>(name: "done")
  List<CommonSREntity>? done;
  @J<PERSON><PERSON><PERSON>(name: "renew")
  List<CommonSREntity>? renew;
  @J<PERSON><PERSON>ey(name: "reassign")
  List<CommonSREntity>? reassign;
  @J<PERSON><PERSON><PERSON>(name: "assign")
  List<CommonSREntity>? assign;

  ServiceRequestsEntity(
      {this.newList,
      this.processing,
      this.cancel,
      this.done,
      this.renew,
      this.reassign,
      this.assign});

  factory ServiceRequestsEntity.fromJson(Map<String, dynamic> json) =>
      _$ServiceRequestsEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$ServiceRequestsEntityToJson(this);
}

class ServiceManagementObject extends Equatable {
  String? title;
  String? status;
  bool? isCollapse;
  List<CommonSREntity>? listManagement;

  ServiceManagementObject({
    this.title,
    this.status,
    this.isCollapse,
    this.listManagement,
  });

  @override
  List<Object?> get props => [
        this.title,
        this.status,
        this.isCollapse,
        this.listManagement,
      ];
}
