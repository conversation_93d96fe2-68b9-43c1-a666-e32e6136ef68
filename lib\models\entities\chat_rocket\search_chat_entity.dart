import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'search_chat_entity.g.dart';

@JsonSerializable()
class SearchChatEntity extends Equatable {
  @<PERSON><PERSON><PERSON><PERSON>(name: "_id")
  final String? id;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String? username;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String? status;
  @Json<PERSON><PERSON>()
  final String? name;
  @<PERSON>son<PERSON><PERSON>()
  final String? fname;
  @Json<PERSON><PERSON>()
  final String? description;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String? teamMain;
  @Json<PERSON>ey()
  final int? usersCount;
  @<PERSON><PERSON><PERSON>ey()
  final String? t;
  @Json<PERSON>ey()
  final String? ts;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String? teamId;
  @J<PERSON><PERSON>ey()
  final int? roomsCount;

  SearchChatEntity({
    this.id,
    this.username,
    this.status,
    this.name,
    this.fname,
    this.description,
    this.teamMain,
    this.usersCount,
    this.t,
    this.ts,
    this.teamId,
    this.roomsCount,
  });

  factory SearchChatEntity.fromJson(Map<String, dynamic> json) => _$SearchChatEntityFromJson(json);

  Map<String, dynamic> toJson() => _$SearchChatEntityToJson(this);

  @override
  List<Object> get props => [
        this.id!,
        this.username!,
        this.status!,
        this.name!,
        this.fname!,
        this.description!,
        this.teamMain!,
        this.usersCount!,
        this.t!,
        this.ts!,
        this.teamId!,
        this.roomsCount!,
      ];
}
