import 'package:real_care/models/entities/service_param_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/survey_entity.dart';
import 'package:real_care/models/entities/survey_ver2_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'service_request_entity.g.dart';

@JsonSerializable()
class ServiceRequestEntity {
  @JsonKey()
  String? title;
  @JsonKey()
  String? description;
  @JsonKey()
  List<SurveyEntity>? surveys;
  @JsonKey()
  List<SurveyVer2Entity>? surveyAnswers;
  @JsonKey()
  String? target;
  @Json<PERSON>ey()
  bool? canSurvey;
  @JsonKey()
  bool? submitSurvey;
  @JsonKey()
  String? exploitStatus;
  @JsonKey()
  String? id;
  @JsonKey()
  ProjectEntity? project;
  @JsonKey()
  String? code;
  @JsonKey()
  String? createdDate;
  @J<PERSON><PERSON><PERSON>()
  String? updatedDate;
  @Json<PERSON>ey()
  String? doneDate;
  @J<PERSON>Key()
  String? processingDate;
  @JsonKey()
  String? cancelDate;
  @JsonKey()
  List<String>? causeReject;
  @JsonKey()
  String? name;
  @JsonKey()
  String? email;
  @JsonKey()
  String? phone;
  @JsonKey()
  String? repoType;
  @JsonKey()
  ServiceParamEntity? customData;
  @JsonKey()
  String? reason;

  ServiceRequestEntity({
    this.title,
    this.description,
    this.target,
    this.surveys,
    this.submitSurvey,
    this.canSurvey,
    this.exploitStatus,
    this.id,
    this.project,
    this.code,
    this.createdDate,
    this.updatedDate,
    this.doneDate,
    this.processingDate,
    this.cancelDate,
    this.causeReject,
    this.name,
    this.email,
    this.phone,
    this.repoType,
    this.customData,
    this.reason,
  });

  factory ServiceRequestEntity.fromJson(Map<String, dynamic> json) => _$ServiceRequestEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$ServiceRequestEntityToJson(this);
}
