import 'package:json_annotation/json_annotation.dart';

part 'attachments_entity.g.dart';

@JsonSerializable()
class AttachmentsEntity {
  @Json<PERSON>ey()
  String? ts;
  @Json<PERSON>ey()
  String? title;
  @J<PERSON><PERSON>ey()
  String? type;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? description;
  @<PERSON><PERSON><PERSON><PERSON>(name: "title_link")
  String? titleLink;
  @<PERSON><PERSON><PERSON><PERSON>(name: "title_link_download")
  bool? titleLinkDownload;
  @<PERSON>son<PERSON><PERSON>(name: "image_url")
  String? imageUrl;
  @Json<PERSON><PERSON>(name: "image_type")
  String? imageType;
  @<PERSON><PERSON><PERSON><PERSON>(name: "image_size")
  double? imageSize;
  @<PERSON><PERSON><PERSON><PERSON>(name: "image_dimensions")
  dynamic imageDimensions;
  @<PERSON><PERSON><PERSON><PERSON>(name: "image_preview")
  String? imagePreview;

  AttachmentsEntity({
    this.ts,
    this.title,
    this.type,
    this.description,
    this.titleLink,
    this.titleLinkDownload,
    this.imageUrl,
    this.imageType,
    this.imageSize,
    this.imageDimensions,
    this.imagePreview,
  });

  factory AttachmentsEntity.fromJson(Map<String, dynamic> json) => _$AttachmentsEntityFromJson(json);
  Map<String, dynamic> toJson() => _$AttachmentsEntityToJson(this);
}