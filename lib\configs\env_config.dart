enum Environment {
  local,
  staging,
  th,
  prod,
}

extension EnvironmentExt on Environment {
  String get envName {
    switch (this) {
      case Environment.local:
        return 'LOCAL';
      case Environment.staging:
        return 'STAGING';
      case Environment.th:
        return 'TH';
      case Environment.prod:
        return '';
    }
  }

  String get webUrl {
    switch (this) {
      case Environment.local:
        return 'https://fpt.realagent.vn';
      case Environment.staging:
        return 'https://fpt.realagent.vn';
      case Environment.th:
        return 'https://fpt.realagent.vn';
      case Environment.prod:
        return 'https://fpt.realagent.vn';
    }
  }
}