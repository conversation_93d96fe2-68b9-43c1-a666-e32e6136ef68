import 'package:real_care/utils/logger.dart';

class PushNotificationEntity {
  String? title = "";
  String? body = "";
  PushNotificationDataEntity? data;

  PushNotificationEntity({this.title, this.body, this.data});

  factory PushNotificationEntity.fromMap(Map<String, dynamic> map) {
    String title = "";
    String body = "";
    PushNotificationDataEntity? data;
    try {
      if (map['notification'] != null) {
        title = map['notification']['title'] ?? "";
        body = map['notification']['body'] ?? "";
      }
      if (map['data'] != null) {
        final mapData = Map<String, dynamic>.from(map['data']);
        data = PushNotificationDataEntity.fromMap(mapData);
      } else {
        data = PushNotificationDataEntity.fromMap(map);
      }
    } catch (e) {
      logger.e(e);
    }
    return PushNotificationEntity(
      title: title,
      body: body,
      data: data,
    );
  }
}

class PushNotificationDataEntity {
  String? entityId = "";
  String? entityName = "";
  String? eventName = "";
  String? title = "";
  String? content = "";

  //Other data
  String? saleProgramId = "";
  // String saleProgramName = "";
  String? projectId = "";
  // String projectName = "";

  PushNotificationType? get notificationType {
    return PushNotificationTypeExtension.fromString(eventName);
  }

  PushNotificationDataEntity({
    this.entityId,
    this.entityName,
    this.eventName,
    this.title,
    this.content,
    this.saleProgramId,
    // this.saleProgramName,
    this.projectId,
    // this.projectName,
  });

  factory PushNotificationDataEntity.fromMap(Map<String, dynamic> map) {
    return PushNotificationDataEntity(
      entityId: map['entityId'] as String?,
      entityName: map['entityName'] as String?,
      eventName: map['eventName'] as String?,
      title: map['title'] as String?,
      content: map['content'] as String?,
      saleProgramId: map['saleProgramId'] as String?,
      // saleProgramName: map['saleProgramName'] as String,
      projectId: map['projectId'] as String?,
      // projectName: map['projectName'] as String,
    );
  }
}

enum PushNotificationType {
  userPasswordChanged, // Thay đổi mật khẩu thành công
  ticketCreated_YCDCH, // [Ticket YCDCH/YCDC] Tạo YCDCH
  ticketApproved_YCDCH_by_DVBH, // [Ticket YCDCH/YCDC] ĐVBH duyệt YCDCH
  ticketRejected_YCDCH_by_DVKH_or_DVBH, // [Ticket YCDCH/YCDC] ĐVBH/DVKH từ chối YCDCH
  ticketApproved_YCDCH_by_DVKH, // [Ticket YCDCH/YCDC] DVKH duyệt YCDCH
  ticketCreated_DNTT, // [Ticket YCDCH/YCDC] Tạo ĐNTT
  ticketApproved_DNTT, // [Ticket YCDCH/YCDC] Duyệt ĐNTT
  ticketProcessed, // [Ticket YCDCH/YCDC] Ráp ưu tiên
  propertyUnitExpired, // [Sản phẩm] Server trả sp đã đăng ký: [Mã SP] đã bị trả về do hết thời gian
  propertyUnitWithdrawed, // [Sản phẩm] Thu hồi SP: [Mã SP] đã bị thu hồi bởi [người thu thồi]
  propertyUnitTransfered, // [Sản phẩm] Chuyển SP: [Mã SP] đã bị chuyển từ [pos1.name] qua [pos2.nam] bởi [người chuyển]
  transactionSuccessed, // [Giao dịch] Giao dịch thành công (chuyển đỏ sản phẩm)
  transactionWaitingForDocument, // [Giao dịch] Chờ bổ sung hồ sơ (chuyển cam sản phẩm)
}

extension PushNotificationTypeExtension on PushNotificationType {
  static PushNotificationType? fromString(String? name) {
    if (name == "userPasswordChanged") {
      return PushNotificationType.userPasswordChanged;
    } else if (name == "ticketCreated_YCDCH") {
      return PushNotificationType.ticketCreated_YCDCH;
    } else if (name == "ticketApproved_YCDCH_by_DVBH") {
      return PushNotificationType.ticketApproved_YCDCH_by_DVBH;
    } else if (name == "ticketRejected_YCDCH_by_DVKH_or_DVBH") {
      return PushNotificationType.ticketApproved_YCDCH_by_DVKH;
    } else if (name == "ticketApproved_YCDCH_by_DVKH") {
      return PushNotificationType.ticketApproved_YCDCH_by_DVKH;
    } else if (name == "ticketCreated_DNTT") {
      return PushNotificationType.ticketCreated_DNTT;
    } else if (name == "ticketApproved_DNTT") {
      return PushNotificationType.ticketApproved_DNTT;
    } else if (name == "ticketProcessed") {
      return PushNotificationType.ticketProcessed;
    } else if (name == "propertyUnitTransfered") {
      return PushNotificationType.propertyUnitTransfered;
    } else if (name == "transactionSuccessed") {
      return PushNotificationType.transactionSuccessed;
    } else if (name == "transactionWaitingForDocument") {
      return PushNotificationType.transactionWaitingForDocument;
    } else {
      return null;
    }
  }
}