import 'package:real_care/utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesHelper {
  static const _authKey = '_authKey';
  static const _introKey = '_introKey';

  static const _lastSelectedProject = '_lastSelectedProject';
  static const _lastSelectedProjectName = '_lastSelectedProjectName';
  static const _lastSelectedApartment = '_lastSelectedApartment';
  static const _apartmentCode = '_apartmentCode';
  static const _propertyApartmentCode = '_propertyApartmentCode';

  static const _longitude = '_longitude';
  static const _latitude = '_latitude';

  //Get authKey
  static Future<String> getApiTokenKey() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_authKey) ?? "";
    } catch (e) {
      logger.e(e);
      return "";
    }
  }

  //Set authKey
  static void setApiTokenKey(String apiTokenKey) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(_authKey, apiTokenKey);
  }

  //Get intro
  static Future<bool> isSeenIntro() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_introKey) ?? false;
    } catch (e) {
      logger.e(e);
      return false;
    }
  }

  //Set intro
  static void setSeenIntro({isSeen = true}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_introKey, isSeen ?? true);
  }

  //Get last project
  static Future<String> getLastSelectedProject() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastSelectedProject) ?? ""; // [Fix this]
    } catch (e) {
      return "";
    }
  }

  //Set  project id
  static void setLastSelectedProject(String projectId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSelectedProject, projectId);
    } catch (e) {
      logger.e(e);
    }
  }

  //Get last project
  static Future<String> getLastSelectedProjectName() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastSelectedProjectName) ?? "";
    } catch (e) {
      return "";
    }
  }

  //Set  project name
  static void setLastSelectedProjectName(String projectId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSelectedProjectName, projectId);
    } catch (e) {
      logger.e(e);
    }
  }

  //Get last apartment
  static Future<String> getLastSelectedApartment() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastSelectedApartment) ?? "";
    } catch (e) {
      return "";
    }
  }

  //Set  apartment id
  static void setLastSelectedApartment(String apartmentId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSelectedApartment, apartmentId);
    } catch (e) {
      logger.e(e);
    }
  }

  //Get apartment code
  static Future<String> getLastSelectedApartmentCode() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_apartmentCode) ?? "";
    } catch (e) {
      return "";
    }
  }

  //Set  apartment code
  static void setLastSelectedApartmentCode(String apartmentCode) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_apartmentCode, apartmentCode);
    } catch (e) {
      logger.e(e);
    }
  }

  //Set latitude
  static void setLatitude(String latitude) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_latitude, latitude);
    } catch (e) {
      logger.e(e);
    }
  }

  //Get latitude
  static Future<String?> getLatitude() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_latitude);
    } catch (e) {
      return "";
    }
  }

  //Get apartment code
  static Future<String> getLastSelectedPropertyApartmentCode() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_propertyApartmentCode) ?? "";
    } catch (e) {
      return "";
    }
  }

  //Set  apartment code
  static void setLastSelectedPropertyApartmentCode(String propertyApartmentCode) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_propertyApartmentCode, propertyApartmentCode);
    } catch (e) {
      logger.e(e);
    }
  }

  //Set longitude
  static void setLongitude(String longitude) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_longitude, longitude);
    } catch (e) {
      logger.e(e);
    }
  }

  //Get latitude
  static Future<String?> getLongitude() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_longitude);
    } catch (e) {
      return "";
    }
  }

  //Set to list projectID
  static void setListProjectId({
    required String email,
    required List<String> listProjectId,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(email, listProjectId);
    } catch (e) {
      logger.e(e);
    }
  }

  //Get list projectID
  static Future<List<String>?> getListProjectId({required String email}) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(email);
    } catch (e) {
      return null;
    }
  }
}