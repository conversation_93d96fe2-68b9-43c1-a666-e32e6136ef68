import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/attachments_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message_attachments_entity.g.dart';

@JsonSerializable()
class MessageAttachmentsEntity extends Equatable {
  @Json<PERSON>ey(name: 'text')
  final String? text;

  @Json<PERSON><PERSON>(name: 'authorName')
  final String? authorName;

  @Json<PERSON>ey(name: 'authorIcon')
  final String? authorIcon;

  @<PERSON><PERSON><PERSON><PERSON>(name: "messageLink")
  final String? messageLink;

  @<PERSON><PERSON><PERSON><PERSON>(name: "attachments")
  final List<AttachmentsEntity>? attachments;

  @<PERSON><PERSON><PERSON><PERSON>(name: "ts")
  final String? ts;

  @<PERSON><PERSON><PERSON><PERSON>(name: "title")
  final String? title;

  @J<PERSON><PERSON><PERSON>(name: "type")
  final String? type;

  @J<PERSON><PERSON><PERSON>(name: "description")
  final String? description;

  @<PERSON><PERSON><PERSON><PERSON>(name: "titleLink")
  final String? titleLink;

  @<PERSON><PERSON><PERSON><PERSON>(name: "titleLinkDownload")
  final bool? titleLinkDownload;

  @<PERSON><PERSON><PERSON><PERSON>(name: "imageUrl")
  final  String? imageUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: "imageType")
  final String? imageType;

  @<PERSON><PERSON><PERSON><PERSON>(name: "imageSize")
  final int? imageSize;

  @JsonKey(name: "imageDimensions")
  final ImageDimensionEntity? imageDimensions;

  @JsonKey(name: "imagePreview")
  final String? imagePreview;

  MessageAttachmentsEntity({
    this.text,
    this.authorName,
    this.authorIcon,
    this.messageLink,
    this.attachments,
    this.ts,
    this.title,
    this.type,
    this.description,
    this.titleLink,
    this.titleLinkDownload,
    this.imageUrl,
    this.imageType,
    this.imageSize,
    this.imageDimensions,
    this.imagePreview,
  });

  factory MessageAttachmentsEntity.fromJson(Map<String, dynamic> json) => _$MessageAttachmentsEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MessageAttachmentsEntityToJson(this);

  @override
  List<Object?> get props => [
    text,
    authorName,
    authorIcon,
    messageLink,
    attachments,
    ts,
    title,
    type,
    description,
    titleLink,
    titleLinkDownload,
    imageUrl,
    imageType,
    imageSize,
    imageDimensions,
    imagePreview,
  ];
}

@JsonSerializable()
class ImageDimensionEntity extends Equatable {
  @JsonKey(name: 'width')
  final int? width;

  @JsonKey(name: 'height')
  final int? height;

  ImageDimensionEntity({
    this.width,
    this.height,
  });

  factory ImageDimensionEntity.fromJson(Map<String, dynamic> json) => _$ImageDimensionEntityFromJson(json);

  Map<String, dynamic> toJson() => _$ImageDimensionEntityToJson(this);

  @override
  List<Object?> get props => [
    width,
    height,
  ];
}

