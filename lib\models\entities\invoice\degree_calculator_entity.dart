import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';
import 'invoice_entity.dart';

part 'degree_calculator_entity.g.dart';

@JsonSerializable()
class DegreeCalculatorEntity extends Equatable {
  @Json<PERSON>ey()
  String? id;
  @JsonKey()
  int? to;
  @JsonKey()
  int? from;
  @JsonKey()
  int? price;

  DegreeCalculatorEntity({
    this.id,
    this.to,
    this.from,
    this.price,
  });

  factory DegreeCalculatorEntity.fromJson(Map<String, dynamic> json) =>
      _$DegreeCalculatorEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$DegreeCalculatorEntityToJson(this);

  DegreeCalculatorEntity copyWith({
    String? id,
    int? to,
    int? from,
    int? price,
  }) {
    return new DegreeCalculatorEntity(
      id: id ?? this.id,
      to: to ?? this.to,
      from: from ?? this.from,
      price: price ?? this.price,
    );
  }

  @override
  List<Object?> get props => [
        this.id,
        this.to,
        this.from,
        this.price,
      ];
}
