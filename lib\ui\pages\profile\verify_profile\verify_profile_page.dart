import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_shadow.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/file_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/file_repository.dart';
import 'package:real_care/repositories/index.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/my_app_bar.dart';
import 'package:real_care/ui/dialogs/file_picker_dialog.dart';
import 'package:real_care/ui/pages/profile/verify_profile/verify_profile_cubit.dart';
import 'package:real_care/ui/pages/profile/verify_profile/verify_profile_state.dart';
import 'package:real_care/ui/widgets/app_snackbar.dart';
import 'package:real_care/utils/dialog_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:shimmer/shimmer.dart';
import 'package:real_care/helper/attach_file_helper.dart';

enum PersonalPhotoType {
  PERSONAL_PHOTO,
  FRONT_IDENTITY_CARD,
  BACK_IDENTITY_CARD,
}

class VerifyProfilePage extends StatefulWidget {
  @override
  _VerifyProfilePageState createState() => _VerifyProfilePageState();
}

class _VerifyProfilePageState extends State<VerifyProfilePage> {
  late AppCubit _appCubit;
  PersonalPhotoType? _pickingPhotoType;
  VerifyProfileCubit? _cubit;

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    final uploadRepository = RepositoryProvider.of<UploadRepository>(context);
    final projectRepository = RepositoryProvider.of<ProjectRepository>(context);
    _cubit = VerifyProfileCubit(
      uploadRepo: uploadRepository,
      projectRepository: projectRepository,
    );
    _appCubit = BlocProvider.of<AppCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: Stack(
        children: [
          _buildBackgroundWidget(),
          SafeArea(
            child: _buildBodyWidget(),
          ),
        ],
      ),
    );
  }

  Widget _buildBodyWidget() {
    return Column(
      children: [
        MyAppBar(
          title: S.of(context).title_verify_profile,
          onBackPressed: () {
            Navigator.of(context).pop();
          },
        ),
        Expanded(
          child: Column(
            children: [
              _buildVerifyProfileHeader(),
              SizedBox(height: 10),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 20),
                      _buildIdentityImages(),
                      SizedBox(height: 5),
                      _buildPortraitImages(),
                      SizedBox(height: 25),
                      _btnVerifyProfile(),
                      SizedBox(height: 10),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        child: AppWhiteCustomButton(
                          title: S.of(context).text_back,
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ),
                      SizedBox(height: 25),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _btnVerifyProfile() {
    return BlocConsumer<VerifyProfileCubit, VerifyProfileState>(
      bloc: _cubit,
      listenWhen: (previous, current) =>
          previous.verifyProfile != current.verifyProfile,
      listener: (prev, current) async {
        if (current.verifyProfile == LoadStatus.SUCCESS) {
          _showMessage(SnackBarMessage(
            message:
                'Yêu cầu thay đổi thông tin tài khoản của bạn đã được gửi đến quản trị viên thành công, chúng tôi sẽ cố gắng phản hồi sớm nhất',
            type: SnackBarType.SUCCESS,
          ));
          Future.delayed(Duration(seconds: 3), () {
            // _appCubit.verifyProfileSuccess(current.verifyImages); // [Fix]: Fix later
            Navigator.of(context).pop(true);
          });
        } else {
          if (current.verifyProfile == LoadStatus.FAILURE) {
            _appCubit.verifyProfileFailure();
            _showMessage(SnackBarMessage(
              message: S.of(context).verify_profile_error,
              type: SnackBarType.ERROR,
            ));
          }
        }
      },
      buildWhen: (prev, current) {
        return prev.portrait != current.portrait ||
            prev.identityBackImage != current.identityBackImage ||
            prev.identityFrontImage != current.identityFrontImage;
      },
      builder: (context, state) {
        bool isCheckFile = state.checkFile;
        bool isLoading = state.verifyProfile == LoadStatus.LOADING;
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 15),
          child: isCheckFile || state.verifyProfile == LoadStatus.SUCCESS
              ? AppGreyButton(
                  title: 'Gửi thông tin',
                )
              : AppTintButton(
                  title: 'Gửi thông tin',
                  isLoading: isLoading,
                  onPressed: _handlerVerifyProfile,
                ),
        );
      },
    );
  }

  Widget _buildVerifyProfileHeader() {
    return Container(
      height: 110,
      margin: EdgeInsets.only(left: 12, right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: AppShadow.boxShadow,
        color: Colors.white,
      ),
      child: Stack(
        children: [
          Positioned(
              right: -110 / 2,
              child: Container(
                height: 110,
                width: 110,
                child: Image.asset(AppImages.icPersonLargeOrange),
              )),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Container(
                  height: 74,
                  width: 68,
                  child: Image.asset(AppImages.icCheckWithShield),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 80,
                        height: 18,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: AppColors.orangeFFD584,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: AppColors.orange, width: 1),
                        ),
                        child: Text(S.of(context).not_verify_profile,
                            style: AppTextStyle.blackS10),
                      ),
                      SizedBox(height: 5),
                      Text(S.of(context).text_verify_profile,
                          style: AppTextStyle.tintS14),
                    ],
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _labelText(String labelText) {
    return Container(
      child: RichText(
        text: TextSpan(children: [
          TextSpan(
            text: labelText,
            style: AppTextStyle.blackS14,
          ),
          TextSpan(
            text: "*",
            style: AppTextStyle.blackS12.copyWith(color: Colors.red),
          )
        ]),
      ),
    );
  }

  Widget _buildIdentityImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
            margin: EdgeInsets.only(left: 12),
            child: Text(S.of(context).text_identity_card,
                style: AppTextStyle.blackS14Bold)),
        SizedBox(height: 12),
        Row(
          children: [
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _labelText(S.of(context).text_front_identity),
                  SizedBox(height: 5),
                  _buildImagesFontWidget(
                    image: AppImages.icFrontIdentityWithDashLine,
                  )
                ],
              ),
            ),
            SizedBox(width: 5),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _labelText(S.of(context).text_behind_identity),
                  SizedBox(height: 5),
                  _buildImagesBackWidget(
                    image: AppImages.icBehindIdentityWithDashLine,
                  )
                ],
              ),
            ),
            SizedBox(width: 5),
          ],
        )
      ],
    );
  }

  Widget _buildPortraitImages() {
    return Container(
      margin: EdgeInsets.only(left: 12, right: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(S.of(context).text_portrait, style: AppTextStyle.blackS14Bold),
          SizedBox(height: 12),
          _labelText(S.of(context).text_image_portrait),
          SizedBox(height: 5),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildImagesPersonalWidget(
                      image: AppImages.icPortraitWithDashLine,
                    )
                  ],
                ),
              ),
              SizedBox(width: 5),
              Expanded(
                child: AspectRatio(
                  aspectRatio: 175 / 116,
                  child: Container(
                    margin: EdgeInsets.only(bottom: 18),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(S.of(context).text_note,
                            style: AppTextStyle.greyS10),
                        Text(S.of(context).text_describe_verify,
                            style: AppTextStyle.greyS10),
                        Container(
                          height: 60,
                          width: 85,
                          child: Image.asset(AppImages.icDemoIdentityPicture),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildImagesFontWidget({required String image}) {
    return BlocBuilder<VerifyProfileCubit, VerifyProfileState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.uploadFrontIdentityCardStatus !=
              current.uploadFrontIdentityCardStatus;
        },
        builder: (context, state) {
          if (state.uploadFrontIdentityCardStatus == LoadStatus.LOADING) {
            return _buildLoadingFile();
          } else if (state.uploadFrontIdentityCardStatus ==
              LoadStatus.LOADING) {
            return Container();
          } else {
            return Container(
                child: Stack(
              children: [
                state.frontIdentityCard == null
                    ? Container(
                        child: AspectRatio(
                          aspectRatio: 162 / 97,
                          child: Image.asset(
                            image,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                        padding: EdgeInsets.only(right: 5, bottom: 18),
                      )
                    : Container(
                        padding: EdgeInsets.only(right: 5, bottom: 18),
                        child: AspectRatio(
                          aspectRatio: 162 / 97,
                          child: ClipRRect(
                            child: Container(
                                child: Image.file(state.frontIdentityCard!,
                                    fit: BoxFit.cover),
                                color: Colors.grey),
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                          ),
                        ),
                      ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () {
                      _pickingPhotoType = PersonalPhotoType.FRONT_IDENTITY_CARD;
                      _onPickImage(PersonalPhotoType.FRONT_IDENTITY_CARD);
                    },
                    child: Container(
                      height: 36,
                      width: 36,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: AppShadow.boxShadow,
                          color: Colors.white),
                      child: Image.asset(AppImages.icCameraOrange),
                    ),
                  ),
                ),
              ],
            ));
          }
        });
  }

  Widget _buildImagesBackWidget({required String image}) {
    return BlocBuilder<VerifyProfileCubit, VerifyProfileState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.uploadBackIdentityCardStatus !=
              current.uploadBackIdentityCardStatus;
        },
        builder: (context, state) {
          if (state.uploadBackIdentityCardStatus == LoadStatus.LOADING) {
            return _buildLoadingFile();
          } else if (state.uploadBackIdentityCardStatus == LoadStatus.FAILURE) {
            return Container();
          } else {
            return Container(
                child: Stack(
              children: [
                state.backIdentityCard == null
                    ? Container(
                        child: AspectRatio(
                          aspectRatio: 162 / 97,
                          child: Image.asset(
                            image,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                        padding: EdgeInsets.only(right: 5, bottom: 18),
                      )
                    : Container(
                        padding: EdgeInsets.only(right: 5, bottom: 18),
                        child: AspectRatio(
                          aspectRatio: 162 / 97,
                          child: ClipRRect(
                            child: Container(
                                child: Image.file(state.backIdentityCard!,
                                    fit: BoxFit.cover),
                                color: Colors.grey),
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                          ),
                        ),
                      ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () {
                      _pickingPhotoType = PersonalPhotoType.BACK_IDENTITY_CARD;
                      _onPickImage(PersonalPhotoType.BACK_IDENTITY_CARD);
                    },
                    child: Container(
                      height: 36,
                      width: 36,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: AppShadow.boxShadow,
                          color: Colors.white),
                      child: Image.asset(AppImages.icCameraOrange),
                    ),
                  ),
                ),
              ],
            ));
          }
        });
  }

  Widget _buildLoadingFile() {
    return AspectRatio(
      aspectRatio: 175 / 116,
      child: Container(
        padding: EdgeInsets.only(right: 5, bottom: 18),
        child: Shimmer.fromColors(
          baseColor: Colors.grey[350]!,
          highlightColor: Colors.grey[100]!,
          child: Center(
            child: Container(
                height: double.infinity,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                )),
          ),
        ),
      ),
    );
  }

  Widget _buildImagesPersonalWidget({required String image}) {
    return BlocBuilder<VerifyProfileCubit, VerifyProfileState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.uploadPersonalPhotoStatus !=
              current.uploadPersonalPhotoStatus;
        },
        builder: (context, state) {
          if (state.uploadPersonalPhotoStatus == LoadStatus.LOADING) {
            return _buildLoadingFile();
          } else if (state.uploadPersonalPhotoStatus == LoadStatus.FAILURE) {
            return Container();
          } else {
            return Container(
                child: Stack(
              children: [
                state.personalPhoto == null
                    ? Container(
                        child: AspectRatio(
                          aspectRatio: 162 / 97,
                          child: Image.asset(
                            image,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                        padding: EdgeInsets.only(right: 5, bottom: 18),
                      )
                    : Container(
                        padding: EdgeInsets.only(right: 5, bottom: 18),
                        child: AspectRatio(
                          aspectRatio: 162 / 97,
                          child: ClipRRect(
                            child: Container(
                                child: Image.file(state.personalPhoto!,
                                    fit: BoxFit.cover),
                                color: Colors.grey),
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                          ),
                        ),
                      ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () {
                      _pickingPhotoType = PersonalPhotoType.PERSONAL_PHOTO;
                      _onPickImage(PersonalPhotoType.PERSONAL_PHOTO);
                    },
                    child: Container(
                      height: 36,
                      width: 36,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: AppShadow.boxShadow,
                          color: Colors.white),
                      child: Image.asset(AppImages.icCameraOrange),
                    ),
                  ),
                ),
              ],
            ));
          }
        });
  }

  Future<void> _onPickImage(PersonalPhotoType type) async {
    setState(() {
      _pickingPhotoType = type;
    });

    final assets = await AttachFileHelper.shared.getListImageFromLibrary(enableCamera: true);
    if (assets.isNotEmpty) {
      final asset = assets[0]; // Get first selected image
      final file = await asset.file; // Get the actual file
      if (file != null) {
        switch (type) {
          case PersonalPhotoType.PERSONAL_PHOTO:
            _cubit?.updatePersonalPhoto(file);
            break;
          case PersonalPhotoType.FRONT_IDENTITY_CARD:
            _cubit?.updateFrontIdentityCard(file);
            break;
          case PersonalPhotoType.BACK_IDENTITY_CARD:
            _cubit?.updateBackIdentityCard(file);
            break;
        }
      }
    }

    setState(() {
      _pickingPhotoType = null;
    });
  }

  Widget _buildBackgroundWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: double.infinity,
          child: Image.asset(
            AppImages.bgHomeHeader,
            fit: BoxFit.fitWidth,
          ),
        ),
      ],
    );
  }

  void _handlerVerifyProfile() {
    _appCubit.verifyProfile();
    _cubit!.verifyProfile();
  }

  void _showMessage(SnackBarMessage message) {
    final context = _scaffoldKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(AppSnackBar(message: message));
    }
  }
}

class AttachFile extends Equatable {
  String id;
  String? path;
  String? name;
  AssetEntity? asset;
  File? file;
  String? urlPath;
  String? mime;
  FileEntity? fileUpload;

  AttachFile({
    required this.id,
    this.path,
    this.name,
    this.asset,
    this.file,
    this.mime,
    this.urlPath,
    this.fileUpload,
  });

  @override
  List<Object?> get props => [
        this.id,
        this.path,
        this.name,
        this.asset,
        this.file,
        this.mime,
        this.urlPath,
        this.fileUpload,
      ];
}
