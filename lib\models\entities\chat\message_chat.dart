import 'package:flutter/material.dart';

class Message {
  String? id;
  String? rid;
  String? t;
  String? msg;
  String? ts;
  User? user;
  FileAttachments? file;
  bool? pinned;
  List<ChildAttachments>? attachments;
  String? sUpdatedAt;
  GlobalKey? key;
  dynamic reactions;

  Message({
    this.id,
    this.t,
    this.rid,
    this.msg,
    this.ts,
    this.user,
    this.attachments,
    this.sUpdatedAt,
    this.file,
    this.pinned,
    this.reactions,
    this.key,
  });


  String get directMessageId {
    List<String> temp = msg?.split(' ') ?? [];
    final String messageLink = temp[1];
    final directMessageId = messageLink.split('msg=').last.replaceAll(')', '');
    return directMessageId;
  }

  Message.fromJson(Map<String, dynamic> json) {
    reactions = json['reactions'];
    pinned = json['pinned'];
    id = json['_id'];
    t = json['t'];
    rid = json['rid'];
    msg = json['msg'];
    if (json['ts'] != null) {
      if (json['ts'] is String) {
        ts = json['ts'];
      } else if (json['ts'] is Map<String, dynamic>) {
        final time = DateTime.fromMillisecondsSinceEpoch(json['ts']["\$date"]).toString();
        ts = time;
      }
    }
    user = json['u'] != null ? User.fromJson(json['u']) : null;
    if (json['attachments'] != null) {
      attachments = <ChildAttachments>[];
      json['attachments'].forEach((v) {
        attachments!.add(ChildAttachments.fromJson(v));
      });
    }
    if (json['_updatedAt'] != null) {
      if (json['_updatedAt'] is String) {
        sUpdatedAt = json['_updatedAt'];
      } else if (json['_updatedAt'] is Map<String, dynamic>) {
        final time = DateTime.fromMillisecondsSinceEpoch(json['_updatedAt']["\$date"]).toString();
        sUpdatedAt = time;
      }
    }

    if (json['file'] != null) {
      file = FileAttachments.fromJson(json['file']);
    }

    key = GlobalKey();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reactions'] = reactions;
    data['_id'] = id;
    data['rid'] = rid;
    data['msg'] = msg;
    data['ts'] = ts;
    data['pinned'] = pinned;
    if (user != null) {
      data['u'] = user?.toJson();
    }
    if (attachments != null) {
      data['attachments'] = attachments?.map((v) => v.toJson()).toList();
    }
    data['_updatedAt'] = sUpdatedAt;
    return data;
  }
}

class User {
  String? id;
  String? username;
  String? name;

  User({this.id, this.username, this.name});

  User.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    username = json['username'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['_id'] = id;
    data['username'] = username;
    data['name'] = name;
    return data;
  }
}

class ParentAttachments {
  String? text;
  String? authorName;
  String? authorIcon;
  String? messageLink;
  List<ParentAttachments>? attachments;
  String? ts;

  ParentAttachments({this.text, this.authorName, this.authorIcon, this.messageLink, this.attachments, this.ts});

  ParentAttachments.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    authorName = json['author_name'];
    authorIcon = json['author_icon'];
    messageLink = json['message_link'];
    if (json['attachments'] != null) {
      attachments = <ParentAttachments>[];
      json['attachments'].forEach((v) {
        attachments!.add(ParentAttachments.fromJson(v));
      });
    }
    if (json['ts'] != null) {
      if (json['ts'] is String) {
        ts = json['ts'];
      } else if (json['ts'] is Map<String, dynamic>) {
        final time = DateTime.fromMillisecondsSinceEpoch(json['ts']["\$date"]).toString();
        ts = time;
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['text'] = text;
    data['author_name'] = authorName;
    data['author_icon'] = authorIcon;
    data['message_link'] = messageLink;
    if (attachments != null) {
      data['attachments'] = attachments?.map((v) => v.toJson()).toList();
    }
    data['ts'] = ts;
    return data;
  }
}

class ChildAttachments {
  String? title;
  String? type;
  String? description;
  String? titleLink;
  bool? titleLinkDownload;

  String? imageUrl;
  String? imageType;
  int? imageSize;

  String? audioUrl;
  String? audioType;
  int? audioSize;

  String? videoUrl;
  String? videoType;
  int? videoSize;

  ImageDimensions? imageDimensions;
  String? imagePreview;

  ChildAttachments({
    this.title,
    this.type,
    this.description,
    this.titleLink,
    this.titleLinkDownload,
    this.imageUrl,
    this.imageType,
    this.imageSize,
    this.audioUrl,
    this.audioType,
    this.audioSize,
    this.videoUrl,
    this.videoType,
    this.videoSize,
    this.imageDimensions,
    this.imagePreview,
  });

  ChildAttachments.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    type = json['type'];
    description = json['description'];
    titleLink = json['title_link'];
    titleLinkDownload = json['title_link_download'];
    imageUrl = json['image_url'];
    imageType = json['image_type'];
    imageSize = json['image_size'];
    audioUrl = json['audio_url'];
    audioType = json['audio_type'];
    audioSize = json['audio_size'];
    videoUrl = json['video_url'];
    videoType = json['video_type'];
    videoSize = json['video_size'];
    imageDimensions = json['image_dimensions'] != null ? ImageDimensions.fromJson(json['image_dimensions']) : null;
    imagePreview = json['image_preview'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['type'] = type;
    data['description'] = description;
    data['title_link'] = titleLink;
    data['title_link_download'] = titleLinkDownload;
    data['image_url'] = imageUrl;
    data['image_type'] = imageType;
    data['image_size'] = imageSize;
    data['audio_url'] = audioUrl;
    data['audio_type'] = audioType;
    data['audio_size'] = audioSize;
    data['video_size'] = videoUrl;
    data['video_type'] = videoType;
    data['video_size'] = videoSize;
    if (imageDimensions != null) {
      data['image_dimensions'] = imageDimensions?.toJson();
    }
    data['image_preview'] = imagePreview;
    return data;
  }
}

class FileAttachments {
  String? id;
  String? name;
  String? type;

  FileAttachments({
    this.id,
    this.name,
    this.type,
  });

  FileAttachments.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    name = json['name'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['_id'] = id;
    data['name'] = name;
    data['type'] = type;
    return data;
  }
}

class ImageDimensions {
  int? width;
  int? height;

  ImageDimensions({this.width, this.height});

  ImageDimensions.fromJson(Map<String, dynamic> json) {
    width = json['width'];
    height = json['height'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['width'] = width;
    data['height'] = height;
    return data;
  }
}