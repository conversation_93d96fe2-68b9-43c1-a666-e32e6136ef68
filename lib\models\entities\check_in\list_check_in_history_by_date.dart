import 'package:real_care/models/entities/check_in/check_in_of_building.dart';
import 'package:json_annotation/json_annotation.dart';

part 'list_check_in_history_by_date.g.dart';

@JsonSerializable()
class CheckInHistoryByDate {
  @J<PERSON><PERSON>ey(name: 'key')
  String? date;
  @J<PERSON><PERSON>ey(name: 'mapData')
  List<CheckInOfBuilding>? listCheckInOfBuilding;

  CheckInHistoryByDate({this.date, this.listCheckInOfBuilding});

  factory CheckInHistoryByDate.fromJson(Map<String, dynamic> json) => _$CheckInHistoryByDateFromJson(json);
  
  Map<String, dynamic> toJson() => _$CheckInHistoryByDateToJson(this);
}
