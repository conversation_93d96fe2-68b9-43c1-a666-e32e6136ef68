import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'family_entity.g.dart';

@JsonSerializable()
class FamilyEntity extends Equatable {
  @Json<PERSON>ey(name: '_id')
  String? id_;
  @J<PERSON><PERSON><PERSON>()
  String? id;
  @Json<PERSON>ey()
  String? relationship;
  @Json<PERSON>ey()
  String? status;
  @JsonKey()
  String? name;
  @<PERSON>son<PERSON>ey()
  String? code;
  @Json<PERSON>ey()
  String? gender;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? birthdayYear;
  @Json<PERSON>ey()
  Object? contactAddress;
  @<PERSON><PERSON><PERSON>ey()
  String? bornAddress;
  @Json<PERSON>ey()
  String? rootAddress;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? email;
  @J<PERSON><PERSON>ey()
  String? phone;
  @J<PERSON><PERSON><PERSON>()
  String? identityId;
  @Json<PERSON>ey()
  String? identityValue;
  @Json<PERSON>ey()
  String? identityIssuedDate;
  @JsonKey()
  String? identityIssuedPlace;
  @JsonKey()
  String? job;
  @J<PERSON><PERSON><PERSON>()
  String? nation;
  @J<PERSON><PERSON><PERSON>()
  String? religion;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? reason;
  @J<PERSON><PERSON>ey()
  String? nationality;

  FamilyEntity({
    this.id_,
    this.id,
    this.relationship,
    this.status,
    this.name,
    this.code,
    this.gender,
    this.birthdayYear,
    this.contactAddress,
    this.bornAddress,
    this.rootAddress,
    this.email,
    this.phone,
    this.identityId,
    this.identityValue,
    this.identityIssuedDate,
    this.identityIssuedPlace,
    this.job,
    this.nation,
    this.religion,
    this.reason,
    this.nationality,
  });

  factory FamilyEntity.fromJson(Map<String, dynamic> json) => _$FamilyEntityFromJson(json);
  Map<String, dynamic> toJson() => _$FamilyEntityToJson(this);

  FamilyEntity copyWith({
    String? id_,
    String? id,
    String? relationship,
    String? status,
    String? name,
    String? code,
    String? gender,
    String? birthdayYear,
    Object? contactAddress,
    String? bornAddress,
    String? rootAddress,
    String? email,
    String? phone,
    String? identityId,
    String? identityValue,
    String? identityIssuedDate,
    String? identityIssuedPlace,
    String? job,
    String? nation,
    String? religion,
    String? reason,
    String? nationality,
  }) {
    return FamilyEntity(
      id_: id_ ?? this.id_,
      id: id ?? this.id,
      relationship: relationship ?? this.relationship,
      status: status ?? this.status,
      name: name ?? this.name,
      code: code ?? this.code,
      gender: gender ?? this.gender,
      birthdayYear: birthdayYear ?? this.birthdayYear,
      contactAddress: contactAddress ?? this.contactAddress,
      bornAddress: bornAddress ?? this.bornAddress,
      rootAddress: rootAddress ?? this.rootAddress,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      identityId: identityId ?? this.identityId,
      identityValue: identityValue ?? this.identityValue,
      identityIssuedDate: identityIssuedDate ?? this.identityIssuedDate,
      identityIssuedPlace: identityIssuedPlace ?? this.identityIssuedPlace,
      job: job ?? this.job,
      nation: nation ?? this.nation,
      religion: religion ?? this.religion,
      reason: reason ?? this.reason,
      nationality: nationality ?? this.nationality,

    );
  }

  @override
  List<Object?> get props => [
    id_,
    id,
    relationship,
    status,
    name,
    code,
    gender,
    birthdayYear,
    contactAddress,
    bornAddress,
    rootAddress,
    email,
    phone,
    identityId,
    identityValue,
    identityIssuedDate,
    identityIssuedPlace,
    job,
    nation,
    religion,
    reason,
    nationality
  ];
}


@JsonSerializable()
class ContactAddress extends Equatable {
  @JsonKey()
  String? province;
  @JsonKey()
  String? district;
  @JsonKey()
  String? ward;
  @JsonKey()
  String? address;

  ContactAddress({
    this.ward,
    this.district,
    this.address,
    this.province,
  });

  factory ContactAddress.fromJson(Map<String, dynamic> json) => _$ContactAddressFromJson(json);
  Map<String, dynamic> toJson() => _$ContactAddressToJson(this);

  @override
  List<Object?> get props => [
    province,
    district,
    address,
  ];
}