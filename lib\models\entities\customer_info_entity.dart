import 'package:real_care/models/entities/address_entity.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:json_annotation/json_annotation.dart';

import 'count_type_entity.dart';
import 'index.dart';

part 'customer_info_entity.g.dart';

@JsonSerializable()
class CustomerInfoEntity {
  CustomerInfoEntity({
    this.onlyYear,
    this.gender,
    this.birthday,
    this.birthdayYear,
    this.address,
    this.rootAddress,
    this.taxCode,
    this.age,
    this.id,
    this.code,
    this.name,
    this.phone,
    this.email,
    this.identityNumber,
    this.identityIssueDate,
    this.identityIssueLocation,
    this.bankInfo,
    this.info,
    this.personalInfo,
    this.identities,
    this.employee,
    this.description,
    this.active,
    this.modifiedBy,
    this.createdBy,
    this.status,
    this.createdDate,
    this.updatedDate,
    this.v,
    this.stt,
    this.employeeName,
    this.employeeCode,
    this.employeeEmail,
    this.posName,
    this.createdDateString,
    this.totalCall,
    this.totalCallOut,
    this.totalCallIn,
    this.totalCallMissing,
    this.takeNote,
    this.inviter,
    this.pos,
    this.rowId,
    this.modifiedDate,
    this.data,
    this.countType,
    this.isPotential,
    this.apartmentCode,
  });

  @JsonKey()
  String? code;
  @JsonKey()
  String? id;
  @JsonKey()
  String? name;
  @JsonKey()
  String? phone;
  @JsonKey()
  String? email;
  @JsonKey()
  String? identityNumber;
  @JsonKey()
  DateTime? identityIssueDate;
  @JsonKey()
  String? identityIssueLocation;
  @JsonKey()
  BankEntity? bankInfo;
  @JsonKey()
  bool? onlyYear;
  @JsonKey()
  String? gender;
  @JsonKey()
  String? birthday;
  @JsonKey()
  String? birthdayYear;
  @JsonKey()
  AddressEntity? address;
  @JsonKey()
  AddressEntity? rootAddress;
  @JsonKey()
  String? taxCode;
  @JsonKey()
  dynamic age;
  @JsonKey()
  CustomerInfoEntity? info;
  @JsonKey()
  PersonalInfoEntity? personalInfo;
  @JsonKey()
  List<PersonalIdentityEntity>? identities;
  @JsonKey()
  UserEntity? employee;
  @JsonKey()
  String? description;
  @JsonKey()
  bool? active;
  @JsonKey()
  String? modifiedBy;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  int? status;
  @JsonKey()
  DateTime? createdDate;
  @JsonKey()
  DateTime? updatedDate;
  @JsonKey()
  int? v;
  @JsonKey()
  int? stt;
  @JsonKey()
  String? employeeName;
  @JsonKey()
  String? employeeCode;
  @JsonKey()
  String? employeeEmail;
  @JsonKey()
  String? posName;
  @JsonKey()
  String? createdDateString;
  @JsonKey()
  int? totalCall;
  @JsonKey()
  int? totalCallOut;
  @JsonKey()
  int? totalCallIn;
  @JsonKey()
  int? totalCallMissing;
  @JsonKey()
  String? takeNote;
  @JsonKey()
  dynamic inviter;
  @JsonKey()
  PosEntity? pos;
  @JsonKey()
  String? rowId;
  @JsonKey()
  DateTime? modifiedDate;
  @JsonKey()
  DataEntity? data;
  @JsonKey()
  CountTypeEntity? countType;
  @JsonKey()
  bool? isPotential;
  @JsonKey()
  String? apartmentCode;

  // [Fix]: Fix later
  // GenderType? get getGenderType {
  //   return GenderTypeExtension.fromAPICode(gender);
  // }

  CustomerInfoEntity copyWith({
    String? code,
    String? id,
    String? name,
    String? phone,
    String? email,
    String? identityNumber,
    DateTime? identityIssueDate,
    String? identityIssueLocation,
    BankEntity? bankInfo,
    bool? onlyYear,
    String? gender,
    String? birthday,
    String? birthdayYear,
    AddressEntity? address,
    AddressEntity? rootAddress,
    String? taxCode,
    int? age,
    CustomerInfoEntity? info,
    PersonalInfoEntity? personalInfo,
    List<PersonalIdentityEntity>? identities,
    UserEntity? employee,
    String? description,
    bool? active,
    String? modifiedBy,
    String? createdBy,
    int? status,
    DateTime? createdDate,
    DateTime? updatedDate,
    int? v,
    int? stt,
    String? employeeName,
    String? employeeCode,
    String? employeeEmail,
    String? posName,
    String? createdDateString,
    int? totalCall,
    int? totalCallOut,
    int? totalCallIn,
    int? totalCallMissing,
    String? takeNote,
    dynamic inviter,
    PosEntity? pos,
    String? rowId,
    DateTime? modifiedDate,
    DataEntity? data,
    CountTypeEntity? countType,
    bool? isPotential,
    String? apartmentCode,
  }) {
    return CustomerInfoEntity(
      code: code ?? this.code,
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      identityNumber: identityNumber ?? this.identityNumber,
      identityIssueDate: identityIssueDate ?? this.identityIssueDate,
      identityIssueLocation:
          identityIssueLocation ?? this.identityIssueLocation,
      bankInfo: bankInfo ?? this.bankInfo,
      onlyYear: onlyYear ?? this.onlyYear,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      birthdayYear: birthdayYear ?? this.birthdayYear,
      address: address ?? this.address,
      rootAddress: rootAddress ?? this.rootAddress,
      taxCode: taxCode ?? this.taxCode,
      age: age ?? this.age,
      info: info ?? this.info,
      personalInfo: personalInfo ?? this.personalInfo,
      identities: identities ?? this.identities,
      employee: employee ?? this.employee,
      description: description ?? this.description,
      active: active ?? this.active,
      modifiedBy: modifiedBy ?? this.modifiedBy,
      createdBy: createdBy ?? this.createdBy,
      status: status ?? this.status,
      createdDate: createdDate ?? this.createdDate,
      updatedDate: updatedDate ?? this.updatedDate,
      v: v ?? this.v,
      stt: stt ?? this.stt,
      employeeName: employeeName ?? this.employeeName,
      employeeCode: employeeCode ?? this.employeeCode,
      employeeEmail: employeeEmail ?? this.employeeEmail,
      posName: posName ?? this.posName,
      createdDateString: createdDateString ?? this.createdDateString,
      totalCall: totalCall ?? this.totalCall,
      totalCallOut: totalCallOut ?? this.totalCallOut,
      totalCallIn: totalCallIn ?? this.totalCallIn,
      totalCallMissing: totalCallMissing ?? this.totalCallMissing,
      takeNote: takeNote ?? this.takeNote,
      inviter: inviter ?? this.inviter,
      pos: pos ?? this.pos,
      rowId: rowId ?? this.rowId,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      data: data ?? this.data,
      countType: countType ?? this.countType,
      isPotential: isPotential ?? this.isPotential,
      apartmentCode: apartmentCode ?? this.apartmentCode,
    );
  }

  factory CustomerInfoEntity.fromJson(Map<String, dynamic> json) => _$CustomerInfoEntityFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerInfoEntityToJson(this);
}