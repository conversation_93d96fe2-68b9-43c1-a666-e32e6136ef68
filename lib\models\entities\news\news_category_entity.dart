import 'package:json_annotation/json_annotation.dart';

part 'news_category_entity.g.dart';

@JsonSerializable()
class NewsCategoryEntity {
  @Json<PERSON>ey()
  String? status;

  @Json<PERSON>ey()
  String? id;

  @J<PERSON><PERSON>ey()
  String? code;

  @<PERSON><PERSON><PERSON><PERSON>()
  String? name;

  @J<PERSON><PERSON>ey()
  String? modifiedBy;

  @Json<PERSON>ey()
  String? createdDate;

  @Json<PERSON>ey()
  String? modifiedDate;

  @JsonKey(name: '__v')
  int? v;

  NewsCategoryEntity(
      {this.status,
      this.id,
      this.code,
      this.name,
      this.modifiedBy,
      this.createdDate,
      this.modifiedDate,
      this.v});

  factory NewsCategoryEntity.fromJson(Map<String, dynamic> json) => _$NewsCategoryEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$NewsCategoryEntityToJson(this);
}
