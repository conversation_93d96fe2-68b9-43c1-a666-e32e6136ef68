import 'members_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'search_member_create_message_entity.g.dart';

@JsonSerializable()
class SearchMemberCreateMessageEntity {
  @Json<PERSON>ey()
  List<MembersEntity>? items;
  @J<PERSON><PERSON>ey()
  bool? success;

  SearchMemberCreateMessageEntity({
    this.success,
    this.items,
  });

  factory SearchMemberCreateMessageEntity.fromJson(Map<String, dynamic> json) =>
      _$SearchMemberCreateMessageEntityFromJson(json);

  Map<String, dynamic> toJson() => _$SearchMemberCreateMessageEntityToJson(this);
}
