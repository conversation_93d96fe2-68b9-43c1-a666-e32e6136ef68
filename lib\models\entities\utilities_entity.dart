import 'package:json_annotation/json_annotation.dart';

part 'utilities_entity.g.dart';

@JsonSerializable()
class UtilitiesEntity {
  @JsonKey()
  String name;
  @JsonKey()
  String type;

  UtilitiesEntity({required this.name, required this.type});

  factory UtilitiesEntity.fromJson(Map<String, dynamic> json) =>
      _$UtilitiesEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$UtilitiesEntityToJson(this);
}
