import 'package:real_care/models/entities/group/comment_entity.dart';
import 'package:real_care/models/entities/group/group_entity.dart';
import 'package:real_care/models/entities/group/group_user_entity.dart';
import 'package:real_care/models/entities/group/survey_question.dart';
import 'package:real_care/models/entities/media_entity.dart';
import 'package:real_care/models/enums/comment_status.dart';
import 'package:real_care/models/enums/group/group_post_status.dart';
import 'package:real_care/models/enums/setting_comment_type.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_post_entity.g.dart';

@JsonSerializable()
class GroupPostEntity {
  @JsonKey()
  String? id;
  @JsonKey()
  GroupUserEntity? user;
  @JsonKey()
  GroupEntity? group;
  @JsonKey()
  List<MediaEntity>? medias;
  @Json<PERSON>ey()
  bool? isPinned;
  @JsonKey()
  bool? isFavorite;
  @JsonKey()
  bool? isComment;
  @JsonKey()
  bool? isShared;
  @JsonKey()
  bool? isAdmin;
  @JsonKey()
  int? totalFavorites;
  @JsonKey()
  int? totalComments;
  @JsonKey()
  int? totalShares;
  @JsonKey()
  List<CommentEntity?>? comments;
  @JsonKey()
  List<GroupUserEntity?>? favorites;
  @JsonKey()
  String? allowComment;
  @JsonKey()
  String? status;
  @JsonKey()
  String? requestStatusPost;
  @JsonKey()
  List<GroupPostEntity?>? histories;
  @JsonKey()
  String? description;
  @JsonKey()
  String? modifiedBy;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  DateTime? createdDate;
  @JsonKey()
  DateTime? modifiedDate;
  @JsonKey()
  String? eventName;
  @JsonKey()
  String? actionName;
  @JsonKey()
  int? v;
  @JsonKey()
  String? shareUrl;
  @JsonKey()
  bool? isFollow;
  @JsonKey()
  List<SurveyQuestion>? poll;
  @JsonKey()
  String? title;
  @JsonKey()
  Object? category;
  @JsonKey()
  int? amount;
  @JsonKey()
  String? sellStatus;
  @JsonKey()
  String? detailedDescription;
  @JsonKey()
  bool? isMarketPost;
  @JsonKey()
  bool? isSave;

  GroupPostStatus? get postStatus {
    return GroupPostStatusExtension.fromStatus(status);
  }

  SettingCommentType get settingCommentType {
    return SettingCommentTypeExtension.fromType(allowComment);
  }

  String get getDateUpdatePost {
    return DateUtils.toDateAPIString(modifiedDate);
  }

  String get getDateCreatePost {
    return DateUtils.toDateAPIString(createdDate);
  }

  CommentStatus get getNotiCommentPost {
    return CommentStatusExtension.getCommentStatusExtension(allowComment);
  }

  GroupPostEntity({
    this.id,
    this.user,
    this.group,
    this.medias,
    this.isPinned,
    this.isFavorite,
    this.isComment,
    this.isShared,
    this.isAdmin,
    this.totalFavorites,
    this.totalComments,
    this.totalShares,
    this.comments,
    this.favorites,
    this.allowComment,
    this.status,
    this.requestStatusPost,
    this.histories,
    this.description,
    this.modifiedBy,
    this.createdBy,
    this.createdDate,
    this.modifiedDate,
    this.eventName,
    this.actionName,
    this.v,
    this.shareUrl,
    this.isFollow,
    this.poll,
    this.category,
    this.sellStatus,
    this.isMarketPost,
    this.detailedDescription,
    this.amount,
    this.title,
    this.isSave,
  }) {
    if (this.isPinned == null) this.isPinned = false;
    if (this.isFollow == null) this.isFollow = false;
    if (this.isFavorite == null) this.isFavorite = false;
    if (this.isComment == null) this.isComment = false;
    if (this.isShared == null) this.isShared = false;
    if (this.isAdmin == null) this.isAdmin = false;
    if (this.totalFavorites == null) this.totalFavorites = 0;
    if (this.totalComments == null) this.totalComments = 0;
    if (this.totalShares == null) this.totalShares = 0;
  }

  factory GroupPostEntity.fromJson(Map<String, dynamic> json) =>
      _$GroupPostEntityFromJson(json);

  Map<String, dynamic> toJson() => _$GroupPostEntityToJson(this);
}
