import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/customer_info_entity.dart';
import 'package:real_care/models/enums/history_extension/history_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'util_history_entity.g.dart';

@JsonSerializable()
class UtilHistoryEntity extends Equatable {
  @JsonKey(name: '_id')
  String? id;
  @JsonKey()
  String? areaId;
  @Json<PERSON><PERSON>()
  String? logo;
  @Json<PERSON>ey()
  String? projectId;
  @Json<PERSON>ey()
  String? name;
  @Json<PERSON>ey()
  String? code;
  @JsonKey()
  String? status;
  @Json<PERSON>ey()
  DateTime? bookingDate;
  @JsonKey()
  String? reason;
  @Json<PERSON>ey()
  CustomerInfoEntity? customer;
  @JsonKey()
  int? depositAmount;
  @JsonKey()
  AreaEntity? area;

  UtilHistoryEntity({
    this.id,
    this.logo,
    this.areaId,
    this.projectId,
    this.name,
    this.code,
    this.status,
    this.bookingDate,
    this.reason,
    this.customer,
    this.area,
    this.depositAmount,
  });

  HistoryStatus? get getHistoryStatus {
    return HistoryStatusExtension.fromText(status);
  }

  @override
  List<Object?> get props => [
        this.id,
        this.name,
        this.logo,
        this.areaId,
        this.code,
        this.status,
        this.customer,
        this.projectId,
        this.bookingDate,
        this.reason,
        this.area,
        this.depositAmount,
      ];

  factory UtilHistoryEntity.fromJson(Map<String, dynamic> json) =>
      _$UtilHistoryEntityFromJson(json);

  Map<String, dynamic> toJson() => _$UtilHistoryEntityToJson(this);
}

@JsonSerializable()
class AreaEntity extends Equatable {
  @JsonKey()
  String? name;
  @JsonKey()
  String? seat;
  @JsonKey()
  String? seatId;
  @JsonKey()
  String? time;

  AreaEntity({
    this.seatId,
    this.name,
    this.seat,
    this.time,
  });

  @override
  List<Object?> get props => [
        this.name,
        this.time,
        this.seat,
        this.seatId,
      ];

  factory AreaEntity.fromJson(Map<String, dynamic> json) =>
      _$AreaEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AreaEntityToJson(this);
}
