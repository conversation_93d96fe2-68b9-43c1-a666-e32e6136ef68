import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/apartment_info/family_entity.dart';
import 'package:real_care/models/enums/apartment_info/family_relationship.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/my_app_bar.dart';
import 'package:real_care/ui/pages/apartment_info/family_management/family_register/family_register_cubit.dart';
import 'package:real_care/ui/pages/apartment_info/widgets/picked_item_widget.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'dialog/cancel_dialog.dart';
import 'dialog/cancel_family_page.dart';
import 'family_register/family_register_step1.dart';

class FamilyDetailPage extends StatefulWidget {
  final FamilyEntity? familyDetail;
  final String? apartmentId;
  const FamilyDetailPage({super.key, this.familyDetail, this.apartmentId});

  @override
  State<FamilyDetailPage> createState() => _FamilyDetailPageState();
}

class _FamilyDetailPageState extends State<FamilyDetailPage> {
  late FamilyRegisterCubit _registerCubit;
  @override
  void initState() {
    super.initState();
    _registerCubit = BlocProvider.of<FamilyRegisterCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          _buildBackgroundWidget(),
          SafeArea(
            child: Column(
              children: [
                _buildAppbar(context, S.current.apartment_family_info),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 20, right: 23, left: 22),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topRight: Radius.circular(20.0),
                          topLeft: Radius.circular(20.0)),
                    ),
                    child: _buildBodyWidget(),
                  ),
                ),
                _buildBottomButton()
              ],
            ),
          ),
        ],
      ),
    );
  }

  _buildBottomButton() {
    return Container(
      margin: EdgeInsets.only(left: 22, right: 23, bottom: 25),
      child: Row(
        children: [
          Expanded(
            child: AppTintBorderButton(
              title: S.current.common_cancel_register,
              onPressed: () {
                CancelFamilyDialog.showCustomDialog(
                  context,
                  CancelFamilyPage(
                    id: widget.apartmentId,
                    familyId: widget.familyDetail?.id,
                    name: widget.familyDetail?.name,
                    onRefreshData: () {
                      Navigator.pop(context, true);
                    },
                  ),
                  true,
                );
              },
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: AppTintButton(
              title: S.current.common_update_info,
              onPressed: () {
                _registerCubit.updateMemberInfo(
                  widget.apartmentId,
                  widget.familyDetail ?? FamilyEntity(),
                );

                Application.router!.navigateTo(
                    context, Routes.familyRegisterStep2,
                    transitionDuration: Duration(milliseconds: 400),
                    transition: TransitionType.fadeIn,
                    routeSettings: RouteSettings(
                        arguments: FamilyRegisterArgument(
                            headerTitle: FamilyRelationshipExtension
                                    .getFamilyRelationshipExtension(
                                        widget.familyDetail?.relationship)
                                .title,
                            headerIcon: FamilyRelationshipExtension
                                    .getFamilyRelationshipExtension(
                                        widget.familyDetail?.relationship)
                                .image,
                            cubit: _registerCubit,
                            editInfo: true)));
              },
              isEnable: true,
            ),
          ),
        ],
      ),
    );
  }

  _buildBodyWidget() {
    String addressContact = getDataContactAddress(widget.familyDetail?.contactAddress ?? "");
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PickedItemWidget(
            iconUrl: widget.familyDetail!.relationship == 'owner' &&
                    widget.familyDetail!.gender == 'female'
                ? AppImages.icWifeApartment
                : FamilyRelationshipExtension.getFamilyRelationshipExtension(
                        widget.familyDetail?.relationship)
                    .image,
            title: FamilyRelationshipExtension.getFamilyRelationshipExtension(
                    widget.familyDetail?.relationship)
                .title,
            marginHorizontal: 0,
            subTitleTop: S.current.apartment_family_relationship_withOwner,
            distanceIconText: 20,
          ),
          SizedBox(height: 14),
          _buildText(
              S.current.common_fullName, widget.familyDetail?.name ?? ""),
          SizedBox(height: 18),
          _buildText(
              S.current.common_dateOfBirth,
              widget.familyDetail?.birthdayYear == null
                  ? ''
                  : DateTimeExtension.fromString(
                          widget.familyDetail?.birthdayYear)!
                      .toDateString()),
          SizedBox(height: 18),
          // _buildText(
          //     S.current.common_gender,
          //     GenderTypeExtension.fromAPICode(widget.familyDetail?.gender)
          //         .displayText), // [Fix]: Fix later
          SizedBox(height: 18),
          _buildText(S.current.common_bornAddress,
              widget.familyDetail?.bornAddress ?? ""),
          SizedBox(height: 18),
          _buildText(S.current.common_rootAddress,
              widget.familyDetail?.rootAddress ?? ""),
          SizedBox(height: 18),
          _buildText(
              S.current.common_nation, widget.familyDetail?.nation ?? ""),
          SizedBox(height: 18),
          _buildText(
              S.current.common_religion, widget.familyDetail?.religion ?? ""),
          SizedBox(height: 18),
          _buildText(S.current.common_nationality,
              widget.familyDetail?.nationality ?? ""),
          SizedBox(height: 18),
          _buildText(S.current.profile_identities,
              widget.familyDetail?.identityValue ?? ""),
          SizedBox(height: 18),
          _buildText(
              S.current.common_identityDate,
              widget.familyDetail?.identityIssuedDate == null
                  ? ''
                  : DateTimeExtension.fromString(
                          widget.familyDetail?.identityIssuedDate)!
                      .toDateString()),
          SizedBox(height: 18),
          _buildText(S.current.common_identityPlace,
              widget.familyDetail?.identityIssuedPlace ?? ""),
          SizedBox(height: 18),
          _buildText(S.current.common_contactAddress, addressContact),
          SizedBox(height: 18),
          _buildText(S.current.common_job, widget.familyDetail?.job ?? ""),
          SizedBox(height: 18),
        ],
      ),
    );
  }

  String getDataContactAddress(Object addressContact){
    if(addressContact is String){
      return addressContact;
    } else {
      if(addressContact is Map<String, dynamic>){
        ContactAddress.fromJson(addressContact);
        return "${addressContact['address']}, ${addressContact["ward"]}, ${addressContact["district"]}, ${addressContact["province"]}";
      }
      return "";
    }
  }

  _buildText(String title, String content) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 135,
          child: Text(
            title,
            style: AppTextStyle.greyS12,
          ),
        ),
        Expanded(
          flex: 5,
          child: Text(
            content,
            textAlign: TextAlign.right,
            style: AppTextStyle.blackS14,
          ),
        ),
      ],
    );
  }

  _buildAppbar(BuildContext context, String title) {
    return MyAppBar(
      title: title,
      onBackPressed: () {
        Navigator.of(context).pop();
      },
    );
  }

  _buildBackgroundWidget() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: Image.asset(
            AppImages.bgHomeHeader,
            fit: BoxFit.fitWidth,
          ),
        )
      ],
    );
  }
}