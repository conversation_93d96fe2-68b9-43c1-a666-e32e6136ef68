import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/room_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'file_of_team_entity.g.dart';

@JsonSerializable()
// ignore: must_be_immutable
class FileOfTeamEntity extends Equatable {
  @Json<PERSON>ey(name: "_id")
  String? id;
  @JsonKey()
  String? name;
  @JsonKey()
  double? size;
  @Json<PERSON>ey()
  String? type;
  @JsonKey()
  String? rid;
  @JsonKey()
  String? userId;
  @JsonKey()
  String? store;
  @JsonKey(name: "_updatedAt")
  String? updatedAt;
  @JsonKey()
  String? instanceId;
  @Json<PERSON><PERSON>()
  dynamic identify;
  @JsonKey()
  bool? complete;
  @JsonKey()
  String? etag;
  @JsonKey()
  String? path;
  @JsonKey()
  double? progress;
  @JsonKey()
  String? token;
  @JsonKey()
  String? uploadedAt;
  @JsonKey()
  bool? uploading;
  @J<PERSON><PERSON><PERSON>()
  String? url;
  @JsonKey()
  String? description;
  @Json<PERSON>ey()
  String? typeGroup;
  @JsonKey()
  UEntity? user;
  @JsonKey()
  bool? isHeader;
  @JsonKey()
  bool? isDownload;
  @JsonKey()
  double? progressDownload;
  @JsonKey()
  String? fileUrl;

  factory FileOfTeamEntity.fromJson(Map<String, dynamic> json) => _$FileOfTeamEntityFromJson(json);

  Map<String, dynamic> toJson() => _$FileOfTeamEntityToJson(this);

  FileOfTeamEntity({
    this.id,
    this.name,
    this.size,
    this.type,
    this.rid,
    this.userId,
    this.store,
    this.updatedAt,
    this.instanceId,
    this.identify,
    this.complete,
    this.etag,
    this.path,
    this.progress,
    this.token,
    this.uploadedAt,
    this.uploading,
    this.url,
    this.description,
    this.typeGroup,
    this.user,
    this.isHeader = false,
    this.isDownload = false,
    this.progressDownload = 0.0,
    this.fileUrl,
  });

  @override
  List<Object?> get props => [
        this.id,
        this.name,
        this.size,
        this.type,
        this.rid,
        this.userId,
        this.store,
        this.updatedAt,
        this.instanceId,
        this.identify,
        this.complete,
        this.etag,
        this.path,
        this.progress,
        this.token,
        this.uploadedAt,
        this.uploading,
        this.url,
        this.description,
        this.typeGroup,
        this.user,
        this.isHeader,
        this.isDownload,
        this.progressDownload,
      ];

  FileOfTeamEntity copyWith({
    String? id,
    String? name,
    double? size,
    String? type,
    String? rid,
    String? userId,
    String? store,
    String? updatedAt,
    String? instanceId,
    dynamic identify,
    bool? complete,
    String? etag,
    String? path,
    double? progress,
    String? token,
    String? uploadedAt,
    bool? uploading,
    String? url,
    String? description,
    String? typeGroup,
    UEntity? user,
    bool? isHeader,
    bool? isDownload,
    double? progressDownload,
  }) {
    return new FileOfTeamEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      size: size ?? this.size,
      type: type ?? this.type,
      rid: rid ?? this.rid,
      userId: userId ?? this.userId,
      store: store ?? this.store,
      updatedAt: updatedAt ?? this.updatedAt,
      instanceId: instanceId ?? this.instanceId,
      identify: identify ?? this.identify,
      complete: complete ?? this.complete,
      etag: etag ?? this.etag,
      path: path ?? this.path,
      progress: progress ?? this.progress,
      token: token ?? this.token,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploading: uploading ?? this.uploading,
      url: url ?? this.url,
      description: description ?? this.description,
      typeGroup: typeGroup ?? this.typeGroup,
      user: user ?? this.user,
      isHeader: isHeader ?? this.isHeader,
      isDownload: isDownload ?? this.isDownload,
      progressDownload: progressDownload ?? this.progressDownload,
    );
  }
}
