import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'community_rules_entity.g.dart';

@JsonSerializable()
class CommunityRulesEntity {
  @Json<PERSON>ey()
  String? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "_id")
  String? id_;
  @Json<PERSON>ey()
  List? customersAccepted;
  @Json<PERSON>ey()
  String? title;
  @Json<PERSON>ey()
  String? content;
  @JsonKey()
  String? projectId;
  @Json<PERSON>ey()
  bool? accepted;
  @Json<PERSON>ey()
  String? modifiedBy;
  @JsonKey()
  DateTime? modifiedDate;
  @Json<PERSON>ey()
  DateTime? createdDate;


  CommunityRulesEntity({
    this.id,
    this.id_,
    this.modifiedDate,
    this.title,
    this.projectId,
    this.modifiedBy,
    this.createdDate,
    this.accepted,
    this.customersAccepted,
    this.content,
  });

  factory CommunityRulesEntity.fromJson(Map<String, dynamic> json) => _$CommunityRulesEntityFromJson(json);

  Map<String, dynamic> toJson() => _$CommunityRulesEntityToJson(this);
}
