import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/config/dynamic_config_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/dynamic_config_repository.dart';
import 'package:real_care/network/api_util.dart';

part 'dynamic_config_state.dart';

// DataFlow: Step 5
// Create cubit

class DynamicConfigCubit extends Cubit<DynamicConfigState> {
  DynamicConfigRepository repository;

  DynamicConfigCubit()
      : repository = DynamicConfigRepositoryImpl(ApiUtil.getApiClient()),
        super(DynamicConfigState());

  Future<void> getDynamicConfig(bool isLoggedIn) async {
    emit(state.copyWith(loadDynamicConfigStatus: LoadStatus.LOADING));

    try {
      // final results = state.dynamicConfig == null
      //     ? await repository.getDynamicConfig()
      //     : state.dynamicConfig!;
      final results = await repository.getDynamicConfig();
      List<InformationDisplay> _homeMainMenu = [];

      for (var element in results) {
        if (element.appName == "FptHome" &&
            element.screenName == "Home" &&
            element.section == "Main") {
          if (element.configuration!.isActive! && element.configuration!.accessible!) {
              // (isLoggedIn || element.configuration!.accessible!)) {
            _homeMainMenu.add(InformationDisplay(
                functionName: element.functionName,
                title: element.configuration!.label,
                localAssets: element.configuration!.icon));
          }
        }
      }

      // DataFlow: Step 9
      // Let UI know when data available
      emit(state.copyWith(
          dynamicConfig: results,
          homeMainMenu: _homeMainMenu,
          loadDynamicConfigStatus: LoadStatus.SUCCESS));
    } catch (e) {
      emit(state.copyWith(loadDynamicConfigStatus: LoadStatus.FAILURE));
    }
  }
}