import 'package:json_annotation/json_annotation.dart';

part 'single_survey_entity.g.dart';

@JsonSerializable()
class SingleSurveyEntity{
  @JsonKey()
  String? value;

  @JsonKey()
  String? code;

  @JsonKey()
  String? displayPosition;


  SingleSurveyEntity({this.value, this.code, this.displayPosition});

  factory SingleSurveyEntity.fromJson(Map<String, dynamic> json) => _$SingleSurveyEntityFromJson(json);

  Map<String, dynamic> toJson() => _$SingleSurveyEntityToJson(this);
}