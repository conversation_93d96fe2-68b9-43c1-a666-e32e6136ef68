import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'room_entity.g.dart';

@JsonSerializable()
// ignore: must_be_immutable
class RoomEntity extends Equatable {
  @J<PERSON><PERSON><PERSON>(name: '_id')
  String? id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rid')
  String? rid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'u')
  UEntity? u;

  @<PERSON><PERSON><PERSON><PERSON>(name: '_updatedAt')
  String? updatedAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'alert')
  bool? alert;

  @<PERSON>son<PERSON><PERSON>(name: 'fname')
  String? fname;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'groupMentions')
  int? groupMentions;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  String? name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'open')
  bool? open;

  @<PERSON><PERSON><PERSON><PERSON>(name: 't')
  String? t;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'unread')
  int? unread;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'userMentions')
  int? userMentions;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'ls')
  String? ls;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'lr')
  String? lr;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'roles')
  List<String>? roles;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'tunread')
  List<String>? tunread;

  bool? isOnline;

  RoomEntity({
    this.id,
    this.rid,
    this.u,
    this.updatedAt,
    this.alert,
    this.fname,
    this.groupMentions,
    this.name,
    this.open,
    this.t,
    this.unread,
    this.userMentions,
    this.ls,
    this.lr,
    this.roles,
    this.tunread,
    this.isOnline = false,
  }) {
    if (this.unread == null) {
      this.unread = 0;
    }
    if (this.isOnline == null) {
      this.isOnline = false;
    }
  }

  DateTime? get updateTime {
    try {
      if (updatedAt != null) {
        DateTime date = DateTime.parse(updatedAt!).toLocal();
        return date;
      } else
        return null;
    } catch (e) {
      return null;
    }
  }

  factory RoomEntity.fromJson(Map<String, dynamic> json) => _$RoomEntityFromJson(json);

  Map<String, dynamic> toJson() => _$RoomEntityToJson(this);

  @override
  List<Object?> get props => [
        this.id,
        this.rid,
        this.u,
        this.updatedAt,
        this.alert,
        this.fname,
        this.groupMentions,
        this.name,
        this.open,
        this.t,
        this.unread,
        this.userMentions,
        this.ls,
        this.lr,
        this.roles,
        this.tunread,
        this.isOnline,
      ];
}

@JsonSerializable()
// ignore: must_be_immutable
class UEntity extends Equatable {
  @JsonKey(name: '_id')
  String? uId;

  @JsonKey(name: 'username')
  String? username;

  @JsonKey(name: 'name')
  String? name;

  UEntity({
    this.uId,
    this.username,
    this.name,
  });

  factory UEntity.fromJson(Map<String, dynamic> json) => _$UEntityFromJson(json);

  Map<String, dynamic> toJson() => _$UEntityToJson(this);

  @override
  List<Object?> get props => [
        uId,
        username,
        name,
      ];
}
