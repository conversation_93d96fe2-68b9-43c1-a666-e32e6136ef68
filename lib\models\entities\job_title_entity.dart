class JobTitleEntity {
  String? id;
  String? code;
  String? nameVN;
  String? nameEN;

  JobTitleEntity({
    this.id,
    this.code,
    this.nameVN,
    this.nameEN,
  });

  factory JobTitleEntity.fromJson(Map<String, dynamic> json) {
    return JobTitleEntity(
      id: json["id"],
      code: json["code"],
      nameVN: json["nameVN"],
      nameEN: json["nameEN"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "code": code,
    "nameVN": nameVN,
    "nameEN": nameEN,
  };
}