# Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class com.google.firebase.** { *; }

# Keep your model classes
-keep class vn.realagent.careplus.fpt.models.** { *; }

# PhotoManager
-keep class com.fluttercandies.photo_manager.** { *; }
-keep class com.fluttercandies.photo_manager.core.** { *; }
-keep class com.fluttercandies.photo_manager.permission.** { *; }

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
} 