import 'package:flutter/material.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_shadow.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/ui/components/app_cache_image.dart';

class ProfileHeaderWidget extends StatelessWidget {
  final UserEntity? userEntity;
  final VoidCallback? onCameraPressed;
  final VoidCallback? onVerifyPressed;
  final VoidCallback? onCoinPressed;
  final VoidCallback? onWalletPressed;
  final bool isLoad;

  ProfileHeaderWidget({
    required this.userEntity,
    this.onCameraPressed,
    this.onVerifyPressed,
    this.onCoinPressed,
    this.onWalletPressed,
    this.isLoad = false,
  });

  @override
  Widget build(BuildContext context) {
    final status = userEntity != null ? userEntity!.isActive ?? VerifyAccountStatus.INIT : VerifyAccountStatus.INIT;
    return Container(
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(left: 12, right: 12, bottom: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              boxShadow: AppShadow.boxShadow,
              color: Colors.white,
            ),
            child: Stack(
              children: [
                Positioned(
                    right: -110 / 2,
                    child: Container(
                      width: 110,
                      child: Image.asset(AppImages.icPersonLargeOrange),
                    )),
                Container(
                  padding: EdgeInsets.all(20),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              border:
                                  Border.all(width: 2, color: AppColors.orange),
                              shape: BoxShape.circle,
                            ),
                            height: 60,
                            width: 60,
                            child: isLoad
                                ? _loadImage()
                                : ClipRRect(
                                    borderRadius: BorderRadius.circular(30),
                                    child: AppCacheImage(
                                      url: userEntity?.images?.avatar ?? "",
                                    ),
                                  ),
                          ),
                          Positioned(
                            bottom: -5,
                            right: -5,
                            child: GestureDetector(
                              onTap: onCameraPressed,
                              child: Container(
                                height: 30,
                                width: 30,
                                color: Colors.transparent,
                                child: Image.asset(
                                  AppImages.icCameraTintWithCircle,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(userEntity?.name ?? "",
                                style: AppTextStyle.blackS18Bold),
                            SizedBox(height: 5),
                            // VerifyWidget(status: status), // [Fix]: Fix later
                            SizedBox(height: 7),
                            status == VerifyAccountStatus.APPROVED
                                ? Container()
                                : Text(S.of(context).text_verify_profile,
                                    style: AppTextStyle.tintS10),
                          ],
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: 32,
            child: Center(
              child: (status == VerifyAccountStatus.APPROVED ||
                      status == VerifyAccountStatus.WAITING)
                  ? Container()
                  : VerifyNowWidget(onPressed: onVerifyPressed),
            ),
          ),
        ],
      ),
    );
  }

  Widget _loadImage() {
    return Container(
      alignment: Alignment.center,
      child: Container(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          backgroundColor: Colors.white,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.main),
        ),
      ),
    );
  }
}

class VerifyNowWidget extends StatelessWidget {
  final VoidCallback? onPressed;
  VerifyNowWidget({this.onPressed});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
            color: Colors.orange, borderRadius: BorderRadius.circular(24)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 24,
              width: 24,
              margin: EdgeInsets.only(right: 5),
              child: Image.asset(AppImages.icVerify),
            ),
            Text(
              S.of(context).account_verifyNow,
              style: AppTextStyle.whiteS14Bold,
            )
          ],
        ),
      ),
    );
  }
}

class VerifyWidget extends StatelessWidget {
  final VerifyAccountStatus? status;
  VerifyWidget({this.status});
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 18,
          padding: EdgeInsets.symmetric(horizontal: 6),
          decoration: BoxDecoration(
            color: status.backgroundColor,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: status.borderColor, width: 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Visibility(
                visible: (status == VerifyAccountStatus.APPROVED),
                child: Container(
                  height: 12,
                  width: 12,
                  margin: EdgeInsets.only(right: 2),
                  child: Image.asset(AppImages.icCheckSmallGreen),
                ),
              ),
              Text(
                status.displayText,
                style: status.textStyle,
              ),
            ],
          ),
        ),
        Spacer(),
      ],
    );
  }
}
