import 'package:real_care/models/entities/single_survey_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'survey_ver2_entity.g.dart';

@JsonSerializable()
class SurveyVer2Entity {


  @Json<PERSON>ey()
  String? name;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? type;
  @<PERSON><PERSON><PERSON>ey()
  List<String>? displayPosition;
  @Json<PERSON>ey()
  dynamic answer;
  @Json<PERSON>ey()
  List<SingleSurveyEntity>? value;

  SurveyVer2Entity({
    this.name,
    this.answer,
    this.type,
    this.value,
    this.displayPosition,
  });



  factory SurveyVer2Entity.fromJson(Map<String, dynamic> json) =>
      _$SurveyVer2EntityFromJson(json);

  Map<String, dynamic> toJson() => _$SurveyVer2EntityToJson(this);

  SurveyVer2Entity copyWith({
    String? name,
    String? type,
    List<String>? displayPosition,
    dynamic answer,
    List<SingleSurveyEntity>? value,
  }) {
    return SurveyVer2Entity(
      name: name ?? this.name,
      type: type ?? this.type,
      displayPosition: displayPosition ?? this.displayPosition,
      answer: answer ?? this.answer,
      value: value ?? this.value,
    );
  }
}
