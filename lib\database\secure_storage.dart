import 'dart:convert';
import 'dart:async';
import 'package:real_care/database/share_preferences_helper.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/entities/token_entity.dart';
import '../models/entities/user_entity.dart';

class SecureStorage {
  static const _apiTokenKey = '_apiTokenKey';
  static const _userInfoKey = '_userInfoKey';

  final FlutterSecureStorage _storage;

  SecureStorage._internal(this._storage);

  static final SecureStorage _singleton = SecureStorage._internal(FlutterSecureStorage());

  factory SecureStorage() {
    return _singleton;
  }

  factory SecureStorage.getInstance() {
    return _singleton;
  }

  //Save token
  void saveToken(TokenEntity? token) async {
    if (token == null) {
      await _storage.delete(key: _apiTokenKey);
      SharedPreferencesHelper.setApiTokenKey("");
    } else {
      await _storage.write(key: _apiTokenKey, value: jsonEncode(token.toJson()));
      SharedPreferencesHelper.setApiTokenKey(_apiTokenKey);
    }
  }

  //Get token
  Future<TokenEntity?> getToken() async {
    try {
      final key = await SharedPreferencesHelper.getApiTokenKey();
      final tokenEncoded = await _storage.read(key: key);
      if (tokenEncoded == null) return null;
      return TokenEntity.fromJson(jsonDecode(tokenEncoded) as Map<String, dynamic>);
    } catch (e) {
      return null;
    }
  }

  void saveUserInfo(UserEntity? user) async {
    if (user == null) {
      await _storage.delete(key: _userInfoKey);
    } else {
      await _storage.write(key: _userInfoKey, value: jsonEncode(user.toJson()));
    }
  }

  Future<UserEntity?> getUserInfo() async {
    try {
      final userInfoEncoded = await (_storage.read(key: _userInfoKey) as FutureOr<String>);
      return UserEntity.fromJson(jsonDecode(userInfoEncoded) as Map<String, dynamic>);
    } catch (e) {
      return null;
    }
  }
}