import 'package:json_annotation/json_annotation.dart';

part 'survey_entity.g.dart';

@JsonSerializable()
//ignore: must_be_immutable
class SurveyEntity {
  @Json<PERSON>ey(name: "_id")
  String? id;

  @J<PERSON><PERSON>ey()
  dynamic value;
  @<PERSON><PERSON><PERSON><PERSON>()
  dynamic answer;
  @Json<PERSON>ey()
  String? name;

  @JsonKey()
  List<QuestionEntity>? surveys;

  @JsonKey()
  String? code;

  @JsonKey()
  String? type;

  @JsonKey()
  String? displayPosition;

  SurveyEntity({
    this.id,
    this.value,
    this.name,
    this.surveys,
    this.code,
    this.type,
    this.displayPosition,
    this.answer,
  });

  factory SurveyEntity.fromJson(Map<String, dynamic> json) =>
      _$SurveyEntityFromJson(json);

  //
  Map<String, dynamic> toJson() => _$SurveyEntityToJson(this);
}

@JsonSerializable()
//ignore: must_be_immutable
class QuestionEntity {
  @JsonKey()
  String? name;

  @J<PERSON><PERSON>ey()
  String? value;

  @J<PERSON><PERSON><PERSON>()
  String? code;

  QuestionEntity({this.code, this.value, this.name});

  factory QuestionEntity.fromJson(Map<String, dynamic> json) =>
      _$QuestionEntityFromJson(json);

  //
  Map<String, dynamic> toJson() => _$QuestionEntityToJson(this);
}
