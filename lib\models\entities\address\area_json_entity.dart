import 'package:json_annotation/json_annotation.dart';

part 'area_json_entity.g.dart';

@JsonSerializable()
class CountryEntity {
  @JsonKey(name: 'n')
  String name;

  CountryEntity(
    this.name,
  );

  factory CountryEntity.fromJson(Map<String, dynamic> json) => _$CountryEntityFromJson(json);
  Map<String, dynamic> toJson() => _$CountryEntityToJson(this);
}

@JsonSerializable()
class ProvinceEntity {
  @JsonKey(name: 'n')
  String name;

  @JsonKey()
  String codeErp;

  @JsonKey(name: 'd')
  List<DistrictEntity> districts;

  ProvinceEntity(
    this.name,
    this.codeErp,
    this.districts,
  );

  factory ProvinceEntity.fromJson(Map<String, dynamic> json) => _$ProvinceEntityFromJson(json);
  Map<String, dynamic> toJson() => _$ProvinceEntityToJson(this);
}

@JsonSerializable()
class DistrictEntity {
  @J<PERSON>Key(name: 'n')
  String name;

  @JsonKey()
  String codeErp;

  @JsonKey(name: 'w')
  List<String> wards;

  DistrictEntity(
    this.name,
    this.codeErp,
    this.wards,
  );

  factory DistrictEntity.fromJson(Map<String, dynamic> json) => _$DistrictEntityFromJson(json);
  Map<String, dynamic> toJson() => _$DistrictEntityToJson(this);
}

@JsonSerializable()
class IdentifyAddressEntity {
  @JsonKey()
  String name;

  IdentifyAddressEntity(
    this.name,
  );

  factory IdentifyAddressEntity.fromJson(Map<String, dynamic> json) => _$IdentifyAddressEntityFromJson(json);
  Map<String, dynamic> toJson() => _$IdentifyAddressEntityToJson(this);
}