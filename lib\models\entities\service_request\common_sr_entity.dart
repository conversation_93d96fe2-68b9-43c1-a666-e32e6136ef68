import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'common_sr_entity.g.dart';

@JsonSerializable()
class CommonSREntity {
  @Json<PERSON>ey()
  String? title;
  @JsonKey()
  String? description;
  @J<PERSON><PERSON><PERSON>()
  String? exploitStatus;
  @Json<PERSON>ey()
  String? id;
  @JsonKey()
  ProjectEntity? project;
  @Json<PERSON>ey()
  String? code;
  @JsonKey()
  String? createdDate;
  @<PERSON><PERSON><PERSON>ey()
  String? updatedDate;
  @JsonKey()
  String? doneDate;
  @Json<PERSON>ey()
  String? processingDate;
  @JsonKey()
  String? cancelDate;
  @<PERSON><PERSON><PERSON><PERSON>()
  List<String>? causeReject;
  @JsonKey()
  String? name;
  @Json<PERSON>ey()
  String? email;
  @JsonKey()
  String? phone;
  @JsonKey()
  String? repoType;
  @JsonKey()
  dynamic customData;
  @JsonKey()
  String? reason;
  @Json<PERSON>ey()
  bool? submitSurvey;

  CommonSREntity({
    this.title,
    this.description,
    this.exploitStatus,
    this.id,
    this.project,
    this.code,
    this.createdDate,
    this.updatedDate,
    this.doneDate,
    this.processingDate,
    this.cancelDate,
    this.causeReject,
    this.name,
    this.email,
    this.phone,
    this.repoType,
    this.customData,
    this.reason,
    this.submitSurvey,
  });

  static String anyToString(dynamic jsonVal) {
    return jsonVal == null ? "{}" : jsonVal?.toString() ?? "{}";
  }

  factory CommonSREntity.fromJson(Map<String, dynamic> json) => _$CommonSREntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$CommonSREntityToJson(this);
}
