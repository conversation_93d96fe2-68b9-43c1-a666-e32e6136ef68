import 'list_check_in_history_by_date.dart';
import 'package:json_annotation/json_annotation.dart';

part 'check_in_history.g.dart';

@JsonSerializable()
class CheckInHistory {
  @Json<PERSON>ey()
  int? total;
  @<PERSON><PERSON><PERSON><PERSON>()
  int? page;
  @J<PERSON><PERSON>ey()
  int? pageSize;
  @<PERSON>son<PERSON>ey()
  int? totalPages;
  @Json<PERSON>ey()
  List<CheckInHistoryByDate>? mapData;

  CheckInHistory({
    this.total,
    this.page,
    this.pageSize,
    this.totalPages,
    this.mapData,
  });

  factory CheckInHistory.fromJson(Map<String, dynamic> json) => _$CheckInHistoryFromJson(json);
  
  Map<String, dynamic> toJson() => _$CheckInHistoryToJson(this);
}
