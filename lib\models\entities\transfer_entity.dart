import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/service_param_entity.dart';
import 'package:real_care/models/enums/transfer_type.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transfer_entity.g.dart';

@JsonSerializable()
class TransferEntity extends Equatable {
  @JsonKey()
  String? title;
  @JsonKey()
  String? description;
  @Json<PERSON>ey()
  String? exploitStatus;
  @JsonKey()
  String? id;
  @JsonKey()
  ServiceParamEntity? customData;
  @JsonKey()
  String? exampleEntityId;
  @JsonKey()
  ProjectEntity? project;
  @JsonKey()
  String? code;
  @Json<PERSON>ey()
  DateTime? createdDate;
  @JsonKey()
  DateTime? updatedDate;

  TransferEntity(
    this.title,
    this.description,
    this.exploitStatus,
    this.id,
    this.customData,
    this.exampleEntityId,
    this.project,
    this.code,
    this.createdDate,
    this.updatedDate,
  );

  TransferType get transferType {
    return TransferTypeExtension.fromExploitStatus(exploitStatus ?? "");
  }

  factory TransferEntity.fromJson(Map<String, dynamic> json) => _$TransferEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TransferEntityToJson(this);

  @override
  List<Object?> get props => [
        this.title,
        this.description,
        this.exploitStatus,
        this.id,
        this.customData,
        this.exampleEntityId,
        this.project,
        this.code,
        this.createdDate,
        this.updatedDate,
      ];
}
