import 'dart:ui';

import 'package:flutter/material.dart';

class AppColors {
  ///Common
  static const Color main = Color(0xFF7F0089);
  static const Color background = Color(0xFFFFFFFF);
  static const Color pink = Color(0xFFFBE6FF);
  static const Color grayIntro = Color(0xFF707070);
  static const Color gradientManagement = Color(0xFFE8D8EB);
  static const Color yellowLight = Color(0xFFFFE8BE);
  static const Color greyRequest = Color(0xFFCCCCCC);
  static const Color greyCBCBCB = Color(0xFFCBCBCB);
  static const Color greenRequest = Color(0xFF00860A);
  static const Color orange = Color(0xFFFFA700);
  static const Color orange800 = Color(0xFFEF6C00);
  static const Color yellowFFC107 = Color(0xFFFFC107);
  static const Color orangeLight = Color(0xFFF9D68F);
  static const Color greyE6E6E6 = Color(0xFFE6E6E6);
  static const Color greyF8F8F8 = Color(0xFFF8F8F8);
  static const Color greyF4F4F4 = Color(0xFFF4F4F4);
  static const Color greyEFEFEF = Color(0xFFEFEFEF);
  static const Color grey818181 = Color(0xFF818181);
  static const Color lightTint = Color(0xFFE8D8EB);
  static const Color red = Color(0xFFEB3323);
  static const Color lightRed = Color(0xFFFFDDDD);

  static const Color lightBlue = Color(0xFFD7F2FF);
  static const Color blue = Color(0xFF42A5F5);
  static const Color blue600 = Color(0xFF039BE5);
  static const Color blue700 = Color(0xFF0288D1);
  static const Color blue900 = Color(0xFF0D47A1);
  static const Color blueBackground = Color(0xFFE7F6FF);
  static const Color blue009 = Color(0xFF009CFC);

  ///Gradient
  static const Color gradientEnd = Color(0xFFA9007C);
  static const Color gradientStart = Color(0xFF09247A);

  ///Shadow
  static const Color shadowColor = Color(0x29000000);

  ///Border
  static const Color borderColor = Color(0xFF606060);
  static const Color borderRequest = Color(0xFF741B84);

  ///Button
  static const Color buttonTint = Color(0xFF7F0089);
  static const Color buttonGrey = Color(0xFF919191);

  ///TextField
  static const Color textFieldEnabledBorder = Color(0xFF919191);
  static const Color textFieldFocusedBorder = Color(0xFF7F0089);
  static const Color textFieldDisabledBorder = Color(0xFF919191);
  static const Color textLeaseF58220 = Color(0xFFF58220);

  ///Line
  static const Color lineGray = Color(0xFFE2E2E2);
  static const Color dashLineGray = Color(0xFFCCCCCC);
  static const Color textFieldBorderColor = Color(0xFF919191);

  ///Other
  static const Color lightGray = Color(0x1A606060);
  static const Color gray = Color(0xFF919191);
  static const Color greyShadow = Color(0xFF000038);
  static const Color greyShadow29 = Color(0xFF000029);
  static const Color greyBorder = Color(0xFFE0E0E0);

  ///Text
  static const Color textBlack = Color(0xFF333333);
  static const Color textRequest = Color(0xFF810089);
  static const Color greySignUp = Color(0xFF58595B);

  static const Color yellowFFE8BE = Color(0xFFFFE8BE);
  static const Color greyDBDBDB = Color(0xFFDBDBDB);
  static const Color green3E8327 = Color(0xFF3E8327);
  static const Color green00860A = Color(0xFF00860A);
  static const Color greenC5EBB8 = Color(0xFFC5EBB8);
  static const Color orangeF3AA3C = Color(0xFFF3AA3C);
  static const Color orangeFFD584 = Color(0xFFFFD584);

  static const Color violetAA01B7 = Color(0xFFAA01B7);

  static const Color redFF0000 = Color(0xFFFF0000);
  static const Color greyF2EAFD = Color(0xFFF2EAFD);
  static const Color pinkFFD8D8 = Color(0xFFFFD8D8);
  static const Color pinkFFEECE = Color(0xFFFFEECE);
  static const Color blueE7F6FF = Color(0xFFE7F6FF);
  static const Color blue004BB2 = Color(0xFF004BB2);
  static const Color blue009CFC = Color(0xFF009CFC);

  static const Color greenE7FFD9 = Color(0xFFE7FFD9);
  static const Color roomTableShadowS2 = Color(0x00000000);
  static const Color roomTableShadowS1 = Color(0xFF4E4E4E);

  static const Color _defaultPrimaryLighter = Color(0xFFFFE4CE);
  static Color primaryLighter = _defaultPrimaryLighter;
}