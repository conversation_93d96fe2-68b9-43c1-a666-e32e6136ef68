import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'search_member_create_group_entity.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class SearchMemberCreateGroupEntity<T> extends Equatable {
  final List<T>? result;
  final bool? success;
  final int? count;
  final int? offset;
  final int? total;

  SearchMemberCreateGroupEntity({
    this.success,
    this.result,
    this.count,
    this.offset,
    this.total,
  });

  SearchMemberCreateGroupEntity<T> copyWith({
    bool? success,
    int? count,
    int? offset,
    List<T>? result,
    int? total,
  }) =>
      SearchMemberCreateGroupEntity<T>(
        success: success ?? this.success,
        count: count ?? this.count,
        offset: offset ?? this.offset,
        result: result ?? this.result,
        total: total ?? this.total,
      );

  factory SearchMemberCreateGroupEntity.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) {
    SearchMemberCreateGroupEntity<T> resultGeneric = SearchMemberCreateGroupEntity<T>(
      success: json['success'] as bool? ?? true,
      count: json['count'] as int? ?? 1,
      offset: json['offset'] as int? ?? 0,
      total: json['total'] as int? ?? 1,
    );

    if (json['result'] != null) {
      if (json['result'] is List) {
        return resultGeneric.copyWith(
          result: (json['result'] as List).map(fromJsonT).toList(),
        );
      }
    } else if (json['data'] != null) {
      if (json['data'] is List) {
        return resultGeneric.copyWith(
          result: (json['data'] as List).map(fromJsonT).toList(),
        );
      }
    }
    return resultGeneric;
  }

  @override
  List<Object?> get props => [
        success,
        result,
        count,
        offset,
        total,
      ];
}
