import 'package:flutter/material.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/check_in_repository.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/app_cache_image.dart';
import 'package:real_care/ui/pages/check_in/detail_heath_declaration/detail_heath_declacation_page.dart';
import 'package:real_care/ui/pages/check_in/information_check_in/dialog/dialog_common.dart';
import 'package:real_care/ui/pages/check_in/information_check_in/information_check_in_cubit.dart';
import 'package:real_care/ui/pages/check_in/information_check_in/information_check_in_state.dart';
import 'package:real_care/ui/pages/profile/dialog_sign_out/dialog_sign_out.dart';
import 'package:real_care/ui/widgets/app_snackbar.dart';
import 'package:real_care/ui/widgets/loading_indicator_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class InformationCheckInPage extends StatefulWidget {
  final String? userId;
  final String? buildingId;
  final String? buildingName;
  final String? address;

  const InformationCheckInPage({
    super.key,
    this.userId,
    this.buildingId,
    this.buildingName,
    this.address,
  });

  @override
  State<InformationCheckInPage> createState() => _InformationCheckInPageState();
}

class _InformationCheckInPageState extends State<InformationCheckInPage> {
  InformationCheckInCubit? _cubit;
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    final repository = RepositoryProvider.of<CheckInRepository>(context);
    _cubit = InformationCheckInCubit(
      repository: repository,
    );
    _cubit!.initData(widget.userId);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      body: SafeArea(
          child: Stack(
        children: [
          Column(
            children: [
              _buildHeader,
              Expanded(child: body),
            ],
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child:
                BlocBuilder<InformationCheckInCubit, InformationCheckInState>(
                    bloc: _cubit,
                    buildWhen: (prev, current) {
                      return prev.loadStatus != current.loadStatus;
                    },
                    builder: (context, state) {
                      if (state.loadStatus == LoadStatus.SUCCESS) {
                        return _buildBottom;
                      } else {
                        return Container();
                      }
                    }),
          )
        ],
      )),
    );
  }

  void showDialog(String title, String iconDialog) {
    DetailDialogSignOut.showCustomDialog(
      context,
      DialogCheckInPage(
          title: title,
          iconDialog: iconDialog,
          dismissOnTapOutSide: false,
          content: ' vào lúc ',
          contentLeft: _cubit!.state.user?.userInfo?.name ?? "",
          contentRight: _cubit!.state.dateHealthDeclaration(isValue: true),
          contentLeftStyle: AppTextStyle.blackS14Bold,
          contentStyle:
              AppTextStyle.blackS14.copyWith(fontWeight: FontWeight.normal),
          contentRightStyle: AppTextStyle.blackS14
              .copyWith(color: AppColors.main, fontWeight: FontWeight.normal),
          callback: () {},
          onPress: () {
            Navigator.pop(context);
          }),
      true,
    );
  }

  Widget get _buildBottom {
    return BlocConsumer<InformationCheckInCubit, InformationCheckInState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.loadStatusApprove != current.loadStatusApprove ||
              prev.loadStatusDeny != current.loadStatusDeny;
        },
        listenWhen: (prev, current) {
          return prev.loadStatusApprove != current.loadStatusApprove ||
              prev.loadStatusDeny != current.loadStatusDeny;
        },
        listener: (context, state) {
          if (state.loadStatusDeny == LoadStatus.SUCCESS) {
            showDialog('Check-in không thành công', AppImages.icCheckInWarning);
          } else {
            if (state.loadStatusApprove == LoadStatus.SUCCESS) {
              showDialog('Check-in thành công', AppImages.icCheckInSuccess);
            }
          }
          if (state.loadStatusDeny == LoadStatus.FAILURE ||
              state.loadStatusApprove == LoadStatus.FAILURE) {
            _showMessage(SnackBarMessage(
              message: state.reason,
              type: SnackBarType.ERROR,
            ));
          }
        },
        builder: (context, state) {
          return Container(
            height: 80,
            color: Colors.white,
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      _cubit!.approveAccess(
                        userId: widget.userId,
                        buildingId: widget.buildingId,
                        buildingName: widget.buildingName,
                        address: widget.address,
                        isDeny: true,
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.main),
                        color: Colors.white,
                        borderRadius: BorderRadius.all(
                          Radius.circular(16.0),
                        ),
                      ),
                      width: 110,
                      height: 32,
                      child: Center(
                        child: state.loadStatusDeny == LoadStatus.LOADING
                            ? LoadingIndicatorWidget(color: Colors.white)
                            : Text(
                                "Từ chối",
                                style: AppTextStyle.blackS14
                                    .copyWith(color: AppColors.main),
                              ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  SizedBox(
                    width: 110,
                    height: 32,
                    child: AppTintButton(
                      title: 'Đồng ý',
                      textStyle: AppTextStyle.whiteS14Bold,
                      isLoading: state.loadStatusApprove == LoadStatus.LOADING,
                      onPressed: () {
                        _cubit!.approveAccess(
                          userId: widget.userId,
                          buildingId: widget.buildingId,
                          buildingName: widget.buildingName,
                          address: widget.address,
                          isDeny: false,
                        );
                      },
                    ),
                  )
                ],
              ),
            ),
          );
        });
  }

  void _showMessage(SnackBarMessage message) {
    final context = _scaffoldKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(AppSnackBar(message: message));
    }
  }

  Widget get _buildHeader {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: SizedBox(
            width: 40,
            height: 40,
            child: Image.asset(
              AppImages.icBackBlack,
              color: AppColors.grayIntro,
            ),
          ),
        ),
        Spacer(),
        GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: SizedBox(
            width: 40,
            height: 40,
            child: Image.asset(
              AppImages.icCloseBig,
            ),
          ),
        ),
        SizedBox(
          width: 5,
        )
      ],
    );
  }

  Widget get body {
    return BlocBuilder<InformationCheckInCubit, InformationCheckInState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.loadStatus != current.loadStatus;
        },
        builder: (context, state) {
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 15,
              ),
              child: Column(
                children: [
                  Text(
                    'THÔNG TIN CHECK-IN',
                    style: AppTextStyle.blackS14Bold,
                  ),
                  SizedBox(
                    height: 27,
                  ),
                  _buildAvatar(
                      isLoadding: state.loadStatus == LoadStatus.LOADING,
                      avatarUrl: state.user?.userInfo?.images?.avatar ?? ""),
                  SizedBox(
                    height: 39,
                  ),
                  state.loadStatus == LoadStatus.LOADING
                      ? _buildShimmer(circular: 10, width: 192, height: 17)
                      : Text(
                          state.user?.userInfo?.name ?? '',
                          style: AppTextStyle.blackS16Bold,
                        ),
                  SizedBox(
                    height: 8,
                  ),
                  state.loadStatus == LoadStatus.LOADING
                      ? _buildShimmer(circular: 10, width: 104, height: 17)
                      : _statusHealthDeclaration(
                          status: state.statusHealthDeclaration(
                              state.user?.lastHealthDeclaration?.isValid ??
                                  false),
                          isValid: state.user?.lastHealthDeclaration?.isValid ??
                              false),
                  SizedBox(
                    height: 16,
                  ),
                  state.loadStatus == LoadStatus.LOADING
                      ? _buildShimmer(circular: 10, width: 42, height: 17)
                      : Visibility(
                          visible: state.numberAge() != 0,
                          child: Column(
                            children: [
                              Text(
                                '${state.numberAge()} Tuổi',
                                style: AppTextStyle.blackS14,
                              ),
                              SizedBox(
                                height: 6,
                              ),
                            ],
                          ),
                        ),
                  // state.loadStatus == LoadStatus.LOADING
                  //     ? _buildShimmer(circular: 10, width: 100, height: 17)
                  //     : Text(
                  //         state.teamDivisionCompany(
                  //             team: state.user?.userInfo?.team,
                  //             division: state.user?.userInfo?.division,
                  //             company: state.user?.userInfo?.company),
                  //         style: AppTextStyle.blackS14,
                  //         textAlign: TextAlign.center,
                  //       ), // [Fix]: Fix later
                  SizedBox(
                    height: 18,
                  ),
                  state.loadStatus == LoadStatus.LOADING
                      ? _buildShimmer(
                          circular: 0,
                          width: MediaQuery.of(context).size.width,
                          height: 200)
                      : _buildHistory(
                          isStatus:
                              state.user?.lastHealthDeclaration?.isValid ??
                                  false),
                  SizedBox(
                    height: 130,
                  ),
                ],
              ),
            ),
          );
        });
  }

  Widget _buildAvatar({required bool isLoadding, String avatarUrl = ''}) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
      ),
      height: 110,
      width: 110,
      child: isLoadding
          ? _buildShimmer(circular: 70, height: 110, width: 110)
          : ClipRRect(
              borderRadius: BorderRadius.circular(70),
              child: AppCacheImage(
                url: avatarUrl,
              ),
            ),
    );
  }

  Widget _buildShimmer({required double circular, double? height, double? width}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[400]!,
      highlightColor: Colors.grey.shade100,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(circular),
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _statusHealthDeclaration({String? status, bool? isValid}) {
    return Container(
      width: _textSize(
              status,
              AppTextStyle.whiteS12.copyWith(
                fontSize: 10,
                fontWeight: FontWeight.w500,
              )).width +
          35,
      height: 26,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: (isValid ?? false) ? AppColors.greenRequest : AppColors.red,
      ),
      child: Center(
        child: Row(
          children: [
            SizedBox(
              width: 5,
            ),
            Image.asset(
              (isValid ?? false)
                  ? AppImages.icTichGreen
                  : AppImages.icCloseRedBoderWhite,
              height: 17,
              width: 17,
              fit: BoxFit.fill,
            ),
            SizedBox(
              width: 6,
            ),
            Text(
              status ?? '',
              style: AppTextStyle.whiteS12.copyWith(
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistory({required bool isStatus}) {
    return Container(
      color: isStatus ? AppColors.greenC5EBB8 : AppColors.lightRed,
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14, vertical: 21),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                text: _cubit!.state.titleDate(),
                style: AppTextStyle.blackS12,
                children: <TextSpan>[
                  TextSpan(
                    text: _cubit!.state.dateHealthDeclaration(),
                    style:
                        AppTextStyle.blackS12.copyWith(color: AppColors.main),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 2,
            ),
            Row(
              children: [
                Text(
                  "Khai báo y tế",
                  style: AppTextStyle.blackS14
                      .copyWith(fontWeight: FontWeight.w500),
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    // showDialog(
                    //   context: context,
                    //   useRootNavigator: true,
                    //   barrierDismissible: true,
                    //   useSafeArea: false,
                    //   builder: (context) => DetailHeathDeclarationPage(),
                    // );

                    DetailDialogSignOut.showCustomDialog(
                      context,
                      DetailHeathDeclarationPage(
                          detailId:
                              _cubit!.state.user!.lastHealthDeclaration?.id ??
                                  ''),
                      true,
                    );
                  },
                  child: Row(
                    children: [
                      Text(
                        "Xem chi tiết",
                        style: AppTextStyle.blackS12.copyWith(
                            color: AppColors.main, fontStyle: FontStyle.italic),
                      ),
                      SizedBox(
                        width: 6,
                      ),
                      Icon(
                        Icons.arrow_forward_ios_sharp,
                        color: Colors.black,
                        size: 12,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 6,
            ),
            Row(
              children: [
                Image.asset(isStatus
                    ? AppImages.icTichGreenMini
                    : AppImages.icCloseRedMini),
                SizedBox(
                  width: 6,
                ),
                Text(
                  _cubit!.state.statusHealthDeclaration(isStatus),
                  style: AppTextStyle.blackS14.copyWith(
                    color: isStatus ? AppColors.greenRequest : AppColors.red,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Size _textSize(String? text, TextStyle style) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(
        minWidth: 0,
        maxWidth: double.infinity,
      );
    return textPainter.size;
  }
}