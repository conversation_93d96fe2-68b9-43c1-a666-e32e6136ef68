import 'package:json_annotation/json_annotation.dart';

part 'rootpos_entity.g.dart';

@JsonSerializable()
class RootPos {
  @JsonKey()
  List<dynamic>? staffIds;
  @Json<PERSON>ey()
  String? parentId;
  @Json<PERSON>ey()
  String? name;
  @<PERSON><PERSON><PERSON>ey()
  String? code;
  @Json<PERSON>ey()
  String? id;

  RootPos({this.staffIds, this.parentId, this.name, this.code, this.id});

  factory RootPos.fromJson(Map<String, dynamic> json) => _$RootPosFromJson(json);
  //
  Map<String, dynamic> toJson() => _$RootPosToJson(this);
}
