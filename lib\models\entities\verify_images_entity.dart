import 'package:json_annotation/json_annotation.dart';

part 'verify_images_entity.g.dart';
@JsonSerializable()
class VerifyImagesEntity {
  @JsonKey()
  String? front;
  @JsonKey()
  String? back;
  @Json<PERSON>ey()
  String? portrait;

  VerifyImagesEntity({
    this.front,
    this.back,
    this.portrait,
  });

  factory VerifyImagesEntity.fromJson(Map<String, dynamic> json) => _$VerifyImagesEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$VerifyImagesEntityToJson(this);
}
