import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:real_care/database/share_preferences_helper.dart';
import 'package:real_care/global/global_data.dart';
import 'package:real_care/helper/load_json_helper.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/verify_images_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/repositories/index.dart';
import 'package:real_care/utils/logger.dart';

part 'app_state.dart';

class AppCubit extends Cubit<AppState> {
  UserRepository userRepository;
  AuthRepository authRepository;
  UploadRepository? uploadRepo;
  ProjectRepository? projectRepository;
  NotificationRepository? notificationRepository;

  AppCubit({
    required this.userRepository,
    required this.authRepository,
    this.uploadRepo,
    this.projectRepository,
    this.notificationRepository,
  }) : super(AppState());

  void getData() async {
    await LoadJsonHelper.shared.load();
  }

  void getNotificationCountUnread() async {
    try {
      var result = await notificationRepository!.getNotificationCountUnread();
      emit(state.copyWith(
          isHasNotification: result["count"] > 0 ? true : false,
          unreadNotification: result["count"] ?? 0));
    } catch (e) {
      logger.e(e);
    }
  }

  void removeUserSection() {
    authRepository.removeToken();
    GlobalData.instance.token = null;
  }

  void updateProfile(UserEntity user) {
    emit(state.copyWith(user: user));
  }

  void getProfile() async {
    emit(state.copyWith(fetchUser: LoadStatus.LOADING));
    try {
      final userRes = await userRepository.getProfile();

      emit(state.copyWith(user: userRes));

      emit(state.copyWith(fetchUser: LoadStatus.SUCCESS));
    } catch (error) {
      logger.e(error);
      emit(state.copyWith(fetchUser: LoadStatus.FAILURE));
    }
  }

  ///Sign Out
  void signOut() async {
    emit(state.copyWith(signOutStatus: LoadStatus.LOADING));
    try {
      // [Fix]: This shit below are useless for now on!
      // final deviceToken = await FirebaseMessaging.instance.getToken();
      // final param = SignOutParam(deviceToken: deviceToken);
      // await authRepository.signOut(param); 
      await FirebaseMessaging.instance.deleteToken();
      await authRepository.removeToken();
      GlobalData.instance.token = null;
      emit(state.copyWith(
        signOutStatus: LoadStatus.SUCCESS,
        user: null,
      ));
    } catch (e) {
      logger.e(e);
      emit(state.copyWith(signOutStatus: LoadStatus.FAILURE));
    }
  }

  void uploadAvatar() {
    emit(state.copyWith(uploadAvatarStatus: LoadStatus.LOADING));
  }

  void uploadAvatarSuccess(String? avatar) {
    emit(
      state.copyWith(
        uploadAvatarStatus: LoadStatus.SUCCESS,
        user: state.user!.copyWith(
          images: ImageEntity(avatar: avatar),
        ),
      ),
    );
  }

  void uploadAvatarFailure() {
    emit(state.copyWith(
      uploadAvatarStatus: LoadStatus.FAILURE,
    ));
  }

  void verifyProfile() {
    emit(state.copyWith(verifyProfile: LoadStatus.LOADING));
  }

  // [Fix]: Request to add verifyImages to UserEntity
  // void verifyProfileSuccess(VerifyImagesEntity? verifyImages) {
  //   emit(
  //     state.copyWith(
  //       verifyProfile: LoadStatus.SUCCESS,
  //       user: state.user!.copyWith(verifyImages: verifyImages),
  //     ),
  //   );
  // }

  void verifyProfileFailure() {
    emit(
      state.copyWith(verifyProfile: LoadStatus.FAILURE),
    );
  }

  void setOpenHomeUtil(bool boolean) {
    emit(state.copyWith(openHomeUtil: boolean));
  }

  void setOpenBottomBarFromHome(bool boolean) {
    emit(state.copyWith(openBottomBarFromHome: boolean));
  }

  void setOpenBottomBarFromProfile(bool boolean) {
    emit(state.copyWith(openBottomBarFromProfile: boolean));
  }

  void setOpenProfileUtil(bool boolean) {
    emit(state.copyWith(openProfileUtil: boolean));
  }

  void setCurrentTab(int tabNumber) {
    emit(state.copyWith(currentTab: tabNumber));
  }

  void openNotificationPage(bool value) {
    emit(state.copyWith(openNotificationPage: value));
  }

  void saveProjectId({required String emailUser}) async {
    String projectId = await SharedPreferencesHelper.getLastSelectedProject();
    List<String>? listProjectId =
        await SharedPreferencesHelper.getListProjectId(
      email: emailUser,
    );
    List<String> listParam = [];
    if (listProjectId != null) {
      listParam = listProjectId;
    }
    listParam.add(projectId);
    SharedPreferencesHelper.setListProjectId(
      email: emailUser,
      listProjectId: listParam,
    );
  }

  void hasPropertyChange(bool value) {
    emit(state.copyWith(hasProperty: value));
  }
}