import 'package:json_annotation/json_annotation.dart';
import 'package:real_care/models/entities/index.dart';

part 'building_management_entity.g.dart';

@JsonSerializable()
class BuildingManagementEntity {
  @Json<PERSON>ey()
  String? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "_id")
  String? id_;
  @Json<PERSON>ey()
  String? projectId;
  @Json<PERSON>ey()
  ProjectEntity? project;
  @Json<PERSON>ey()
  String? name;
  @Json<PERSON>ey()
  String? address;
  @<PERSON>son<PERSON>ey()
  String? website;
  @Json<PERSON>ey()
  String? description;
  @Json<PERSON><PERSON>()
  HotlineEntity? primaryApartmentHotline;
  @JsonKey()
  List<HotlineEntity>? apartmentHotlines;
  @Json<PERSON>ey()
  List<HotlineEntity>? emergencyHotlines;
  @J<PERSON><PERSON><PERSON>()
  FileEntity? logo;
  @JsonKey()
  FileEntity? diagram;
  @JsonKey()
  String? modifiedBy;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  DateTime? modifiedDate;
  @J<PERSON><PERSON><PERSON>()
  DateTime? createdDate;

  BuildingManagementEntity({
    this.id,
    this.id_,
    this.description,
    this.projectId,
    this.name,
    this.address,
    this.apartmentHotlines,
    this.createdBy,
    this.createdDate,
    this.emergencyHotlines,
    this.modifiedBy,
    this.modifiedDate,
    this.primaryApartmentHotline,
    this.website,
    this.diagram,
    this.logo,
    this.project,
  });

  factory BuildingManagementEntity.fromJson(Map<String, dynamic> json) => _$BuildingManagementEntityFromJson(json);
  Map<String, dynamic> toJson() => _$BuildingManagementEntityToJson(this);

  BuildingManagementEntity copyWith({
    String? id,
    String? id_,
    String? description,
    String? projectId,
    String? name,
    String? address,
    List<HotlineEntity>? apartmentHotlines,
    String? createdBy,
    DateTime? createdDate,
    List<HotlineEntity>? emergencyHotlines,
    String? modifiedBy,
    DateTime? modifiedDate,
    HotlineEntity? primaryApartmentHotline,
    String? website,
    FileEntity? logo,
    FileEntity? diagram,
    ProjectEntity? project,
  }) {
    return BuildingManagementEntity(
      id: id ?? this.id,
      id_: id_ ?? this.id_,
      description: description ?? this.description,
      projectId: projectId ?? this.projectId,
      name: name ?? this.name,
      address: address ?? this.address,
      apartmentHotlines: apartmentHotlines ?? this.apartmentHotlines,
      createdBy: createdBy ?? this.createdBy,
      createdDate: createdDate ?? this.createdDate,
      emergencyHotlines: emergencyHotlines ?? this.emergencyHotlines,
      modifiedBy: modifiedBy ?? this.modifiedBy,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      primaryApartmentHotline:
          primaryApartmentHotline ?? this.primaryApartmentHotline,
      website: website ?? this.website,
      logo: logo ?? this.logo,
      diagram: diagram ?? this.diagram,
      project: project ?? this.project,
    );
  }

  List<Object?> get props =>
  [
    id,
    id_,
    description,
    projectId,
    name,
    address,
    apartmentHotlines,
    createdBy,
    createdDate,
    emergencyHotlines,
    modifiedBy,
    modifiedDate,
    primaryApartmentHotline,
    website,
    diagram,
    logo,
    project,
  ];
}