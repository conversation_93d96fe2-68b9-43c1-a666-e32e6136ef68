import 'package:real_care/models/entities/group/group_user_entity.dart';
import 'package:real_care/models/entities/media_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'comment_entity.g.dart';

@JsonSerializable()
class CommentEntity {
  @Json<PERSON>ey()
  String? description;
  @Json<PERSON>ey()
  GroupUserEntity? user;
  @JsonKey()
  List<MediaEntity>? medias;
  @JsonKey()
  String? id;
  @Json<PERSON><PERSON>()
  String? userId;
  @Json<PERSON>ey()
  String? createdDate;
  @Json<PERSON>ey()
  int? totalFavorites;
  @JsonKey()
  int? totalComments;
  @J<PERSON><PERSON><PERSON>()
  int? totalPostComments;
  @Json<PERSON>ey()
  bool? isFavorite;
  @Json<PERSON>ey()
  bool? isComment;
  @JsonKey()
  List<CommentEntity?>? subComments;
  @JsonKey()
  String? eventName;
  @JsonKey()
  String? actionName;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? parentId;

  CommentEntity({
    this.description,
    this.user,
    this.medias,
    this.id,
    this.createdDate,
    this.totalFavorites,
    this.isComment,
    this.totalComments,
    this.totalPostComments,
    this.isFavorite,
    this.subComments,
    this.parentId,
    this.eventName,
    this.actionName,
  }) {
    if (totalComments == null) this.totalComments = 0;
    if (totalPostComments == null) this.totalPostComments = 0;
    if (totalFavorites == null) this.totalFavorites = 0;
    if (isFavorite == null) this.isFavorite = false;
  }

  factory CommentEntity.fromJson(Map<String, dynamic> json) => _$CommentEntityFromJson(json);

  Map<String, dynamic> toJson() => _$CommentEntityToJson(this);
}
