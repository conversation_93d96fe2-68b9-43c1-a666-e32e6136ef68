import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/invoice/degree_calculator_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'service_change_entity.g.dart';

@JsonSerializable()
class ServiceChangeInvoiceEntity extends Equatable {
  @Json<PERSON>ey()
  List<DegreeCalculatorEntity>? degreeCalculator;
  @Json<PERSON>ey()
  bool? vat;
  @JsonKey()
  String? id;

  ServiceChangeInvoiceEntity({
    this.degreeCalculator,
    this.vat,
    this.id,
  });

  factory ServiceChangeInvoiceEntity.fromJson(Map<String, dynamic> json) => _$ServiceChangeInvoiceEntityFromJson(json);
  Map<String, dynamic> toJson() => _$ServiceChangeInvoiceEntityToJson(this);

  ServiceChangeInvoiceEntity copyWith({
    List<DegreeCalculatorEntity>? degreeCalculator,
    bool? vat,
    String? id,
  }) {
    return ServiceChangeInvoiceEntity(
      degreeCalculator: degreeCalculator ?? this.degreeCalculator,
      vat: vat ?? this.vat,
      id: id ?? this.id,
    );
  }

  @override
  List<Object?> get props =>
  [
    degreeCalculator,
    vat,
    id,
  ];
}