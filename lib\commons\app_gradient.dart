import 'package:flutter/material.dart';
import 'package:real_care/commons/app_colors.dart';

class AppGradient {
  static final linearGradient = LinearGradient(
    begin: Alignment.bottomCenter,
    end: Alignment.topCenter,
    colors: [AppColors.gradientStart, AppColors.gradientEnd],
  );

  static final linearGradientLeftToRight = LinearGradient(
    begin: Alignment.centerRight,
    end: Alignment.centerLeft,
    colors: [AppColors.gradientStart, AppColors.gradientEnd],
  );

  static final grayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.roomTableShadowS1,
      AppColors.roomTableShadowS2,
    ],
  );

  static final topBlackGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF000000).withAlpha(128),
      Color(0xFF000000).withAlpha(0),
    ],
  );
}