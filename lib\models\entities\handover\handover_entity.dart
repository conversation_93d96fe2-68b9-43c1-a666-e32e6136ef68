import 'package:json_annotation/json_annotation.dart';

part 'handover_entity.g.dart';

@JsonSerializable()
class HandoverTimeEntity {
  @Json<PERSON>ey()
  String? description;
  @Json<PERSON>ey(name: "_id")
  String? id;
  @Json<PERSON>ey()
  List<FrameDataEntity>? mondayFrame;
  @Json<PERSON>ey()
  List<FrameDataEntity>? fridayFrame;
  @Json<PERSON>ey()
  List<FrameDataEntity>? saturdayFrame;
  @Json<PERSON>ey()
  List<FrameDataEntity>? sundayFrame;
  @Json<PERSON>ey()
  List<FrameDataEntity>? thursdayFrame;
  @J<PERSON><PERSON><PERSON>()
  List<FrameDataEntity>? tuesdayFrame;
  @Json<PERSON>ey()
  List<FrameDataEntity>? wednesdayFrame;

  HandoverTimeEntity({
    this.id,
    this.description,
    this.mondayFrame,
    this.fridayFrame,
    this.saturdayFrame,
    this.sundayFrame,
    this.thursdayFrame,
    this.tuesdayFrame,
    this.wednesdayFrame,
  });

  factory HandoverTimeEntity.fromJson(Map<String, dynamic> json) => _$HandoverTimeEntityFromJson(json);

  Map<String, dynamic> toJson() => _$HandoverTimeEntityToJson(this);
}

@JsonSerializable()
class FrameDataEntity {
  @JsonKey()
  String? startTime;
  @JsonKey()
  String? endTime;
  @JsonKey()
  int? amount;

  FrameDataEntity({
    this.startTime,
    this.endTime,
    this.amount,
  });

  factory FrameDataEntity.fromJson(Map<String, dynamic> json) => _$FrameDataEntityFromJson(json);

  Map<String, dynamic> toJson() => _$FrameDataEntityToJson(this);
}
