part of 'dynamic_config_cubit.dart';

// DataFlow: Step 6
// Create state
class DynamicConfigState extends Equatable {
  final LoadStatus loadDynamicConfigStatus;
  final List<DynamicConfigEntity>? dynamicConfig;
  final List<InformationDisplay> homeMainMenu;
 

  const DynamicConfigState({
    this.loadDynamicConfigStatus = LoadStatus.INITIAL,
    this.dynamicConfig,
    this.homeMainMenu = const []
  });

  
  DynamicConfigState copyWith({
    LoadStatus? loadDynamicConfigStatus,
    List<DynamicConfigEntity>? dynamicConfig,
    List<InformationDisplay>? homeMainMenu
  }) {
    return DynamicConfigState(
      loadDynamicConfigStatus: loadDynamicConfigStatus ?? this.loadDynamicConfigStatus,
      dynamicConfig: dynamicConfig ?? this.dynamicConfig,
      homeMainMenu: homeMainMenu ?? this.homeMainMenu
    );
  }

  @override
  List<Object?> get props => [
    loadDynamicConfigStatus,
    dynamicConfig,
    homeMainMenu
  ];
}