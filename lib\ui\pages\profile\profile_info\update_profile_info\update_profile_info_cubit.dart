import 'package:dio/dio.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/identities_entity.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/params/user/update_user_param.dart';
import 'package:real_care/models/responses/otp_token_response.dart';
import 'package:real_care/repositories/auth_repository.dart';
import 'package:real_care/ui/widgets/app_snackbar.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:real_care/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';

import 'update_profile_info_state.dart';

class UpdateProfileInfoCubit extends Cubit<UpdateProfileInfoState> {
  AuthRepository? auth;
  AppCubit? appCubit;

  UpdateProfileInfoCubit({this.auth, this.appCubit})
      : super(UpdateProfileInfoState());

  final showMessageController = PublishSubject<SnackBarMessage>();

  @override
  Future<void> close() {
    showMessageController.close();
    return super.close();
  }

  void setData(String phone, String email) {
    emit(
      state.copyWith(phoneOld: phone, emailOld: email),
    );
  }

  void changeName(String name) {
    emit(state.copyWith(name: name));
  }

  void changePhone(String phone) {
    emit(state.copyWith(
      phone: phone,
      isChangePhone: phone != appCubit?.state.user?.phone,
    ));
  }

  void changeEmail(String email) {
    emit(state.copyWith(
      email: email,
      isChangeEmail: email != appCubit?.state.user?.email,
    ));
  }

  void changeGender(GenderType? gender) {
    emit(state.copyWith(gender: gender));
  }

  void changeIdentifyNumber(String number) {
    // [Fix]: Fix later
    // emit(state.copyWith(
    //   identifyNumber: number,
    //   isChangeIdentifyNumber: number != appCubit?.state.user?.identity?.value,
    // ));
  }

  void changeIdentifyDate(DateTime? date) {
    emit(state.copyWith(identifyDate: date));
  }

  void changeIdentifyAddress(String address) {
    emit(state.copyWith(identifyAddress: address));
  }

  void changeDateOfBirth(DateTime? date) {
    emit(state.copyWith(dateOfBirth: date));
  }

  void changeMapAddress(String mapAddress) {
    emit(state.copyWith(mapAddress: mapAddress));
  }

  void changeMapRootAddress(String mapAddress) {
    // [Fix]: Fix later
    // emit(state.copyWith(
    //   rootAddress: mapAddress,
    //   isChangeRootAddress: mapAddress != appCubit?.state.user?.rootAddress,
    // ));
  }

  void updateProfile() async {
    emit(state.copyWith(
        loadUpdateProfileStatus: LoadStatus.LOADING,
        verifyProfileStatus: LoadStatus.INITIAL));
    try {
      final param = UpdateUserParam(
        identity: IdentitiesEntity(
          value: state.identifyNumber,
          date: state.identifyDate?.toUtc().toString(),
          place: state.identifyAddress,
        ),
        email: state.email,
        name: state.name,
        phone: state.phone,
        gender: state.gender.toAPICode,
        address: state.mapAddress,
        rootAddress: state.rootAddress,
        dob: state.dateOfBirth?.toUtc().toString(),
        identityBackImage: 'not_empty',
        identityFrontImage: 'not_empty',
        avatarImage: 'not_empty',
      );
      await auth!.updateProfile(param: param);
      appCubit!.getProfile();
      emit(state.copyWith(loadUpdateProfileStatus: LoadStatus.SUCCESS));
    } catch (e) {
      logger.e(e);
      emit(state.copyWith(loadUpdateProfileStatus: LoadStatus.FAILURE));
      String message = S.current.error_occurred;
      if (e is DioException) {
        if (e.response?.data is Map) {
          final map = e.response?.data['errors'];
          map.forEach((key, value) {
            message = value;
          });
        }
      }
      showMessageController.sink.add(SnackBarMessage(
        message: message,
        type: SnackBarType.ERROR,
      ));
      return;
    }
  }

  Future<void> sendOTP({
    required String phoneUser,
    required String name,
  }) async {
    try {
      OTPTokenResponse? result;
      if (state.isChangePhone) {
        result = await auth!.userCreateOTP(
          type: "SMS",
          phone: state.phone,
          appName: "Care Plus",
        );
      } else if (state.isChangeEmail) {
        result = await auth!.userCreateOTP(
          type: "EMAIL",
          email: state.email,
          phone: phoneUser,
          appName: "Care Plus",
          name: name,
        );
      }
      emit(state.copyWith(tokenOTP: result?.token ?? ""));
    } catch (e, s) {
      logger.e(e);
      logger.e(s);
    }
  }

  ///Check trùng thông tin
  void checkInformation({
    required String phoneUser,
    required String name,
  }) async {
    emit(state.copyWith(verifyProfileStatus: LoadStatus.LOADING));
    try {
      final param = UpdateUserParam(
        identity: IdentitiesEntity(
          value: state.identifyNumber,
        ),
        email: state.email,
        phone: state.phone,
      );
      await auth!.isDuplicateAccountInfo(param: param);

      ///sent OTP
      await sendOTP(phoneUser: phoneUser, name: name);

      emit(state.copyWith(verifyProfileStatus: LoadStatus.SUCCESS));
    } catch (e, s) {
      logger.e(e);
      logger.e(s);
      emit(state.copyWith(verifyProfileStatus: LoadStatus.FAILURE));
      if (e is DioException) {
        if (e.response!.statusCode == 400) {
          showMessageController.sink.add(SnackBarMessage(
            message: S.current.error_accountInfoDuplicated,
            type: SnackBarType.ERROR,
          ));
          return;
        } else {
          showMessageController.sink.add(SnackBarMessage(
            message: S.current.error_occurred,
            type: SnackBarType.ERROR,
          ));
          return;
        }
      } else {
        showMessageController.sink.add(SnackBarMessage(
          message: S.current.error_occurred,
          type: SnackBarType.ERROR,
        ));
        return;
      }
    }
  }
}