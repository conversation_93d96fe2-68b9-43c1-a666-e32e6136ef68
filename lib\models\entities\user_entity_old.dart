import 'package:equatable/equatable.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/models/entities/identities_entity.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/models/entities/verify_images_entity.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_entity_old.g.dart';

@JsonSerializable()
class UserEntity extends Equatable {
  @JsonKey()
  String? description;
  @Json<PERSON>ey()
  dynamic active;
  @JsonKey()
  String? verifyAccount;
  @JsonKey()
  String? createdDate;
  @JsonKey()
  String? modifiedDate;
  @JsonKey()
  String? id;
  @JsonKey()
  String? name;
  @JsonKey()
  String? code;
  @JsonKey()
  String? email;
  @JsonKey()
  String? phone;
  @JsonKey()
  List<IdentitiesEntity>? identities;
  @JsonKey()
  VerifyImagesEntity? verifyImages;
  @JsonKey()
  String? dob;
  @JsonKey()
  String? gender;
  @JsonKey()
  String? avatar;
  @JsonKey()
  String? address;
  @JsonKey()
  String? buildingName;
  @JsonKey()
  List<String>? permissions;
  @JsonKey()
  String? buildingId;
  @JsonKey()
  String? accessStartDate;
  @JsonKey()
  String? accessEndDate;
  @JsonKey()
  bool? isActive;
  @JsonKey()
  bool? isSecurity;
  @JsonKey()
  String? company;
  @JsonKey()
  String? division;
  @JsonKey()
  String? team;
  @JsonKey()
  String? rootAddress;

  UserEntity({
    this.description,
    this.active,
    this.verifyAccount,
    this.createdDate,
    this.modifiedDate,
    this.id,
    this.name,
    this.code,
    this.email,
    this.phone,
    this.identities,
    this.verifyImages,
    this.dob,
    this.gender,
    this.avatar,
    this.address,
    this.buildingName,
    this.buildingId,
    this.accessStartDate,
    this.accessEndDate,
    this.isActive,
    this.isSecurity,
    this.permissions,
    this.company,
    this.division,
    this.team,
    this.rootAddress,
  });

  // GenderType? get genderType {
  //   return GenderTypeExtension.fromAPICode(gender);
  // }

  IdentitiesEntity? get identity {
    try {
      return identities!.first;
    } catch (e) {
      return null;
    }
  }

  DateTime? get getIdentityDate {
    return DateTimeExtension.fromString(identity?.date,
        format: AppConfig.dateAPIFormat);
  }

  DateTime? get birthday {
    return DateTimeExtension.fromString(dob);
  }

  VerifyAccountStatus? get verifyAccountStatus {
    return VerifyAccountStatusExtension.fromText(verifyAccount);
  }

  UserEntity copyWith({
    String? description,
    dynamic active,
    String? verifyAccount,
    String? createdDate,
    String? modifiedDate,
    String? id,
    String? name,
    String? code,
    String? email,
    String? phone,
    List<IdentitiesEntity>? identities,
    VerifyImagesEntity? verifyImages,
    String? dob,
    String? gender,
    String? avatar,
    String? address,
    String? buildingName,
    String? buildingId,
    String? accessStartDate,
    String? accessEndDate,
    bool? isActive,
    bool? isSecurity,
    List<String>? permissions,
    String? company,
    String? division,
    String? team,
    String? rootAddress,
  }) {
    return UserEntity(
      description: description ?? this.description,
      active: active ?? this.active,
      verifyAccount: verifyAccount ?? this.verifyAccount,
      createdDate: createdDate ?? this.createdDate,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      identities: identities ?? this.identities,
      verifyImages: verifyImages ?? this.verifyImages,
      dob: dob ?? this.dob,
      gender: gender ?? this.gender,
      avatar: avatar ?? this.avatar,
      address: address ?? this.address,
      buildingName: buildingName ?? this.buildingName,
      buildingId: buildingId ?? this.buildingId,
      accessStartDate: accessStartDate ?? this.accessStartDate,
      accessEndDate: accessEndDate ?? this.accessEndDate,
      isActive: isActive ?? this.isActive,
      isSecurity: isSecurity ?? this.isSecurity,
      permissions: permissions ?? this.permissions,
      company: company ?? this.company,
      division: division ?? this.division,
      team: team ?? this.team,
      rootAddress: rootAddress ?? this.rootAddress,
    );
  }

  factory UserEntity.fromJson(Map<String, dynamic> json) => _$UserEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$UserEntityToJson(this);

  @override
  List<Object?> get props => [
        this.description,
        this.active,
        this.verifyAccount,
        this.createdDate,
        this.modifiedDate,
        this.id,
        this.name,
        this.code,
        this.email,
        this.phone,
        this.identities,
        this.verifyImages,
        this.dob,
        this.gender,
        this.avatar,
        this.address,
        this.buildingName,
        this.buildingId,
        this.accessStartDate,
        this.accessEndDate,
        this.isActive,
        this.isSecurity,
        this.permissions,
        this.company,
        this.division,
        this.team,
        this.rootAddress,
      ];
}
