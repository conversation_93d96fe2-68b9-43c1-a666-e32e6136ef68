import 'package:real_care/models/entities/check_in/building_info.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';
part 'check_in_info.g.dart';

@JsonSerializable()
class CheckInInfo {
  @JsonKey()
  String? id;
  @JsonKey()
  String? createdDate;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  String? modifiedDate;
  @JsonKey()
  String? modifiedBy;
  @Json<PERSON>ey()
  String? checkinDate;
  @JsonKey()
  bool? isValid;
  @JsonKey()
  String? userId;
  @J<PERSON><PERSON><PERSON>()
  BuildingInfo? buildingInfo;

  CheckInInfo({
    this.id,
    this.createdDate,
    this.createdBy,
    this.modifiedDate,
    this.modifiedBy,
    this.checkinDate,
    this.userId,
    this.buildingInfo,
    this.isValid,
  });

  DateTime? get checkInDate {
    return DateUtils.fromString(checkinDate);
  }
  
  factory CheckInInfo.fromJson(Map<String, dynamic> json) => _$CheckInInfoFromJson(json);
  
  Map<String, dynamic> toJson() => _$CheckInInfoToJson(this);
}
