import 'package:json_annotation/json_annotation.dart';

part 'message_deleted.g.dart';

@JsonSerializable()
class MessageDeleted {
  @Json<PERSON>ey(name: "_id")
  String? id;
  
  @JsonKey()
  int? ts;
  
  @JsonKey()
  Map<String, dynamic>? message;
  
  @Json<PERSON>ey()
  bool? success;

  MessageDeleted({
    this.id,
    this.message,
    this.success,
    this.ts,
  });

  factory MessageDeleted.fromJson(Map<String, dynamic> json) => _$MessageDeletedFromJson(json);
  Map<String, dynamic> toJson() => _$MessageDeletedToJson(this);
}