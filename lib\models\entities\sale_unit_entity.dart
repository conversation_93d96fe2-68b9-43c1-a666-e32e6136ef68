import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sale_unit_entity.g.dart';

@JsonSerializable()
// ignore: must_be_immutable
class SalesUnit extends Equatable {
  @Json<PERSON>ey()
  List<dynamic>? staffIds;
  @JsonKey()
  String? id;
  @Json<PERSON>ey()
  String? parentId;
  @JsonKey()
  String? code;
  dynamic additionalFields;
  @JsonKey()
  String? name;
  @Json<PERSON>ey()
  String? erpAccount;
  @JsonKey()
  dynamic permissionNum;
  @Json<PERSON>ey()
  String? unitType;

  SalesUnit(
      {this.staffIds,
      this.id,
      this.parentId,
      this.code,
      this.additionalFields,
      this.name,
      this.erpAccount,
      this.permissionNum,
      this.unitType});

  factory SalesUnit.fromJson(Map<String, dynamic> json) => _$SalesUnitFromJson(json);
  //
  Map<String, dynamic> toJson() => _$SalesUnitToJson(this);

  @override
  List<Object?> get props => [
        staffIds,
        id,
        parentId,
        code,
        additionalFields,
        name,
        erpAccount,
        permissionNum,
        unitType
      ];
}
