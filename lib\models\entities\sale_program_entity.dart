import 'package:json_annotation/json_annotation.dart';

import 'index.dart';

part 'sale_program_entity.g.dart';

@JsonSerializable()
class SaleProgramEntity {
  @JsonKey()
  bool? lockConfirmable;
  @Json<PERSON>ey()
  bool? saleUnitLockConfirmable;
  @JsonKey()
  bool? customerConfirmRequired;
  @JsonKey()
  bool? productRegisterQueueAvailable;
  @JsonKey()
  int? lockedPoint;
  @Json<PERSON>ey()
  bool? hasUnitExcelTemplate;
  @Json<PERSON>ey()
  int? dwellTime;
  @JsonKey()
  String? sId;
  @J<PERSON><PERSON><PERSON>()
  ProjectEntity? project;
  @JsonKey()
  String? name;
  @JsonKey()
  String? code;
  @JsonKey()
  String? startTime;
  @JsonKey()
  String? startDate;
  @JsonKey()
  String? endTime;
  @JsonKey()
  String? endDate;
  @JsonKey()
  String? status;
  @J<PERSON><PERSON>ey()
  List<SalesUnit>? saleUnits;
  @JsonKey()
  String? webBannerUrl;
  @JsonKey()
  String? tabletBannerUrl;
  @JsonKey()
  String? phoneBannerUrl;
  @JsonKey()
  String? startTimeBanner;
  @JsonKey()
  String? startDateBanner;
  @JsonKey()
  String? endTimeBanner;
  @JsonKey()
  String? endDateBanner;
  @JsonKey()
  bool? lock;
  @JsonKey()
  String? id;
  @JsonKey()
  String? modifiedDate;
  @JsonKey()
  List<Blocks>? blocks;
  @JsonKey()
  int? iV;
  @JsonKey()
  bool? setOnTop;
  @JsonKey()
  bool? isWorkingTime;

  SaleProgramEntity(
      {this.lockConfirmable,
      this.saleUnitLockConfirmable,
      this.customerConfirmRequired,
      this.productRegisterQueueAvailable,
      this.lockedPoint,
      this.hasUnitExcelTemplate,
      this.dwellTime,
      this.sId,
      this.project,
      this.name,
      this.code,
      this.startTime,
      this.startDate,
      this.endTime,
      this.endDate,
      this.status,
      this.saleUnits,
      this.webBannerUrl,
      this.tabletBannerUrl,
      this.phoneBannerUrl,
      this.startTimeBanner,
      this.startDateBanner,
      this.endTimeBanner,
      this.endDateBanner,
      this.lock,
      this.id,
      this.modifiedDate,
      this.blocks,
      this.iV,
      this.setOnTop,
      this.isWorkingTime});

  factory SaleProgramEntity.fromJson(Map<String, dynamic> json) => _$SaleProgramEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$SaleProgramEntityToJson(this);
}
