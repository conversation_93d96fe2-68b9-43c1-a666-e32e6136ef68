import 'package:json_annotation/json_annotation.dart';
import 'hotline_entity.dart';

part 'custom_hotline_entity.g.dart';

@JsonSerializable()
class CustomHotlineEntity {
  @JsonKey()
  String? type;
  @JsonKey()
  List<HotlineEntity>? listHotline;

  CustomHotlineEntity({this.type, this.listHotline});

  factory CustomHotlineEntity.fromJson(Map<String, dynamic> json) => _$CustomHotlineEntityFromJson(json);
  Map<String, dynamic> toJson() => _$CustomHotlineEntityToJson(this);

  CustomHotlineEntity copyWith({
    String? type,
    List<HotlineEntity>? listHotline,
  }) {
    return CustomHotlineEntity(
      type: type ?? this.type,
      listHotline: listHotline ?? this.listHotline,
    );
  }

  List<Object?> get props =>
  [
    type,
    listHotline,
  ];
}