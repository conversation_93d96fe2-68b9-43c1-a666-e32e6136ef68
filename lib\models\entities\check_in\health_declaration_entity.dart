import 'package:real_care/models/entities/check_in/health_declaration_template_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'health_declaration_entity.g.dart';

@JsonSerializable()
class HealthDeclarationEntity {
  @JsonKey()
  String? id;
  @JsonKey()
  String? createdDate;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  String? modifiedDate;
  @Json<PERSON>ey()
  String? modifiedBy;
  @Json<PERSON>ey()
  String? name;
  @JsonKey()
  String? userId;
  @JsonKey()
  String? image;
  @JsonKey()
  UserEntity? userData;
  @JsonKey()
  List<HealthDeclarationTemplateEntity>? data;
  @JsonKey()
  bool? isValid;

  HealthDeclarationEntity({
    this.id,
    this.createdDate,
    this.createdBy,
    this.modifiedDate,
    this.modifiedBy,
    this.name,
    this.userId,
    this.image,
    this.data,
    this.isValid,
  });

  
  factory HealthDeclarationEntity.fromJson(Map<String, dynamic> json) => _$HealthDeclarationEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$HealthDeclarationEntityToJson(this);

  HealthDeclarationEntity copyWith({
    String? id,
    String? createdDate,
    String? createdBy,
    String? modifiedDate,
    String? modifiedBy,
    String? name,
    String? userId,
    String? image,
    List<HealthDeclarationTemplateEntity>? data,
    bool? isValid,
  }) {
    return HealthDeclarationEntity(
      id: id ?? this.id,
      createdDate: createdDate ?? this.createdDate,
      createdBy: createdBy ?? this.createdBy,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      modifiedBy: modifiedBy ?? this.modifiedBy,
      name: name ?? this.name,
      userId: userId ?? this.userId,
      image: image ?? this.image,
      data: data ?? this.data,
      isValid: isValid ?? this.isValid,
    );
  }
}
