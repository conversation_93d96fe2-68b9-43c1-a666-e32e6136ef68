import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/attachments_entity.dart';
import 'package:real_care/models/entities/chat_rocket/file_of_team_entity.dart';
import 'package:real_care/models/entities/chat_rocket/md_entity.dart';
import 'package:real_care/models/entities/chat_rocket/room_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pinned_message_entity.g.dart';

@JsonSerializable()
// ignore: must_be_immutable
class ListPinnedMessage extends Equatable {
  @JsonKey()
  List<PinnedMessageEntity>? messages;
  @JsonKey()
  int? count;
  @JsonKey()
  int? offset;
  @JsonKey()
  int? total;
  @JsonKey()
  bool? success;

  ListPinnedMessage({
    this.messages,
    this.count,
    this.offset,
    this.total,
    this.success,
  });

  factory ListPinnedMessage.fromJson(Map<String, dynamic> json) => _$ListPinnedMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ListPinnedMessageToJson(this);

  @override
  List<Object?> get props => [
        this.messages,
        this.count,
        this.offset,
        this.total,
        this.success,
      ];
}

@JsonSerializable()
// ignore: must_be_immutable
class PinnedMessageEntity extends Equatable {
  @JsonKey(name: '_id')
  String? id;
  @JsonKey(name: 'rid')
  String? rid;
  @JsonKey(name: 'ts')
  String? ts;
  @JsonKey(name: 'msg')
  String? msg;
  @JsonKey(name: 'file')
  FileOfTeamEntity? file;
  @JsonKey(name: 'groupable')
  bool? groupAble;
  @JsonKey(name: 'attachments')
  List<AttachmentsEntity>? attachments;
  @JsonKey(name: 'md')
  List<MdEntity>? md;
  @JsonKey(name: 'u')
  UEntity? user;
  @JsonKey(name: '_updatedAt')
  String? updatedAt;
  @JsonKey(name: 'urls')
  List<dynamic>? urls;
  @JsonKey(name: 'mentions')
  List<dynamic>? mentions;
  @JsonKey(name: 'channels')
  List<dynamic>? channels;
  @JsonKey(name: 'pinned')
  bool? pinned;
  @JsonKey(name: 'pinnedAt')
  String? pinnedAt;
  @JsonKey(name: 'pinnedBy')
  UEntity? pinnedBy;
  @JsonKey()
  bool? showOption;

  PinnedMessageEntity({
    this.id,
    this.rid,
    this.ts,
    this.msg,
    this.file,
    this.groupAble,
    this.attachments,
    this.user,
    this.updatedAt,
    this.urls,
    this.mentions,
    this.channels,
    this.pinned,
    this.pinnedAt,
    this.pinnedBy,
    this.md,
    this.showOption = false,
  });

  factory PinnedMessageEntity.fromJson(Map<String, dynamic> json) => _$PinnedMessageEntityFromJson(json);

  Map<String, dynamic> toJson() => _$PinnedMessageEntityToJson(this);

  @override
  List<Object?> get props => [
        this.id,
        this.rid,
        this.ts,
        this.msg,
        this.file,
        this.groupAble,
        this.attachments,
        this.user,
        this.updatedAt,
        this.urls,
        this.mentions,
        this.channels,
        this.pinned,
        this.pinnedAt,
        this.pinnedBy,
        this.showOption,
      ];
}
