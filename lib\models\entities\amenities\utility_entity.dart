import 'package:json_annotation/json_annotation.dart';

part 'utility_entity.g.dart';

@JsonSerializable()
class UtilityEntity {
  @Json<PERSON>ey()
  String? name;

  @<PERSON>son<PERSON>ey()
  String? projectId;

  @<PERSON><PERSON><PERSON><PERSON>()
  int? ordinal;

  @<PERSON><PERSON><PERSON><PERSON>()
  String? note;

  @<PERSON><PERSON><PERSON><PERSON>()
  String? description;

  @J<PERSON><PERSON><PERSON>()
  String? serviceType;

  @J<PERSON><PERSON>ey()
  UtilityIconEntity? icon;

  @<PERSON>son<PERSON>ey()
  String? id;

  @<PERSON><PERSON><PERSON><PERSON>()
  List<AreasEntity>? areas;

  @Json<PERSON><PERSON>()
  dynamic logo;

  @<PERSON><PERSON><PERSON><PERSON>()
  List<PromotionUtilityEntity>? promotions;

  @<PERSON><PERSON><PERSON>ey()
  List<ProviderUtilityEntity>? providers;

  @Json<PERSON>ey()
  List<ProviderUtilityEntity>? goodProviders;

  UtilityEntity({
    this.name,
    this.projectId,
    this.ordinal,
    this.note,
    this.description,
    this.serviceType,
    this.icon,
    this.logo,
    this.id,
    this.areas,
    this.promotions,
    this.providers,
    this.goodProviders,
  });

  UtilityEntity copyWith({
    String? name,
    String? projectId,
    int? ordinal,
    UtilityIconEntity? icon,
    String? note,
    String? description,
    String? serviceType,
    String? id,
    List<AreasEntity>? areas,
    dynamic logo,
    List<PromotionUtilityEntity>? promotions,
    List<ProviderUtilityEntity>? providers,
    List<ProviderUtilityEntity>? goodProviders,
  }) {
    return UtilityEntity(
      name: name ?? this.name,
      projectId: projectId ?? this.projectId,
      ordinal: ordinal ?? this.ordinal,
      note: note ?? this.note,
      description: description ?? this.description,
      serviceType: serviceType ?? this.serviceType,
      id: id ?? this.id,
      areas: areas ?? this.areas,
      logo: logo ?? this.logo,
      promotions: promotions ?? this.promotions,
      providers: providers ?? this.providers,
      goodProviders: goodProviders ?? this.goodProviders,
    );
  }

  factory UtilityEntity.fromJson(Map<String, dynamic> json) => _$UtilityEntityFromJson(json);
  Map<String, dynamic> toJson() => _$UtilityEntityToJson(this);
}

@JsonSerializable()
class UtilityIconEntity {
  @JsonKey()
  String? url;

  @JsonKey()
  String? name;

  UtilityIconEntity({this.url, this.name});

  factory UtilityIconEntity.fromJson(Map<String, dynamic> json) => _$UtilityIconEntityFromJson(json);
  Map<String, dynamic> toJson() => _$UtilityIconEntityToJson(this);
}

@JsonSerializable()
class AreasEntity {
  @JsonKey()
  String? id;

  @JsonKey()
  List<SeatsEntity>? seats;
  
  @JsonKey()
  String? name;
  
  @JsonKey(name: "_id")
  String? sId;
  
  @JsonKey()
  String? diagram;

  AreasEntity({this.id, this.seats, this.name, this.sId, this.diagram});

  factory AreasEntity.fromJson(Map<String, dynamic> json) => _$AreasEntityFromJson(json);
  Map<String, dynamic> toJson() => _$AreasEntityToJson(this);
}

@JsonSerializable()
class SeatsEntity {
  @JsonKey()
  String? id;
  
  @JsonKey()
  List<TimeEntity>? time;
  
  @JsonKey()
  String? name;
  
  @JsonKey(name: "_id")
  String? sId;

  SeatsEntity({this.id, this.time, this.name, this.sId});

  factory SeatsEntity.fromJson(Map<String, dynamic> json) => _$SeatsEntityFromJson(json);
  Map<String, dynamic> toJson() => _$SeatsEntityToJson(this);
}

@JsonSerializable()
class TimeEntity {
  @JsonKey()
  String? name;

  @JsonKey()
  bool? enabled;

  @JsonKey()
  String? status;

  @JsonKey()
  bool? mine;

  @JsonKey()
  String? bookingId;

  TimeEntity({this.name, this.enabled, this.status, this.mine, this.bookingId});

  factory TimeEntity.fromJson(Map<String, dynamic> json) => _$TimeEntityFromJson(json);
  Map<String, dynamic> toJson() => _$TimeEntityToJson(this);
}

@JsonSerializable()
class PromotionUtilityEntity {
  @JsonKey()
  String? id;

  @JsonKey()
  String? name;

  @JsonKey()
  ProImageEntity? image;

  @JsonKey()
  String? promotionType;

  @JsonKey()
  bool? bannerShow;

  @JsonKey()
  String? status;

  @JsonKey()
  int? promotionValue;

  @JsonKey()
  int? quantity;

  @JsonKey()
  String? expiryDate;

  @JsonKey()
  String? code;

  @JsonKey(name: "_id")
  String? sId;

  PromotionUtilityEntity({
    this.id,
    this.name,
    this.image,
    this.promotionType,
    this.bannerShow,
    this.status,
    this.promotionValue,
    this.quantity,
    this.expiryDate,
    this.code,
    this.sId,
  });

  factory PromotionUtilityEntity.fromJson(Map<String, dynamic> json) => _$PromotionUtilityEntityFromJson(json);
  Map<String, dynamic> toJson() => _$PromotionUtilityEntityToJson(this);
}

@JsonSerializable()
class ProImageEntity {
  @JsonKey()
  String? avatar;

  @JsonKey()
  String? descriptionImage;
  
  @JsonKey()
  String? bannerImage;

  ProImageEntity({this.avatar, this.descriptionImage, this.bannerImage});

  factory ProImageEntity.fromJson(Map<String, dynamic> json) => _$ProImageEntityFromJson(json);
  Map<String, dynamic> toJson() => _$ProImageEntityToJson(this);
}

@JsonSerializable()
class ProviderUtilityEntity {
  @JsonKey()
  bool? active;

  @JsonKey()
  String? id;
  
  @JsonKey()
  String? name;
  
  @JsonKey()
  String? phone;
  
  @JsonKey()
  String? email;
  
  @JsonKey()
  String? tax;
  
  @JsonKey()
  bool? good;
  
  @JsonKey()
  UtilityLocationEntity? location;
  
  @JsonKey()
  BusinessHours? businessHours;
  
  @JsonKey()
  String? description;
  
  @JsonKey()
  AvatarUtilityEntity? avatar;
  
  @JsonKey()
  AvatarUtilityEntity? banner;
  
  @JsonKey()
  UtilityAddressEntity? address;
  
  @JsonKey(name: "_id")
  String? sId;
  
  @JsonKey()
  double? distance;
  
  @JsonKey()
  String? website;

  ProviderUtilityEntity({
    this.active,
    this.id,
    this.name,
    this.phone,
    this.email,
    this.tax,
    this.good,
    this.location,
    this.businessHours,
    this.description,
    this.avatar,
    this.banner,
    this.sId,
    this.address,
    this.distance,
    this.website,
  });

  factory ProviderUtilityEntity.fromJson(Map<String, dynamic> json) => _$ProviderUtilityEntityFromJson(json);
  Map<String, dynamic> toJson() => _$ProviderUtilityEntityToJson(this);
}

@JsonSerializable()
class UtilityLocationEntity {
  dynamic latitude;
  dynamic longitude;

  UtilityLocationEntity({this.latitude, this.longitude});

  factory UtilityLocationEntity.fromJson(Map<String, dynamic> json) => _$UtilityLocationEntityFromJson(json);
  Map<String, dynamic> toJson() => _$UtilityLocationEntityToJson(this);
}

@JsonSerializable()
class BusinessHours {
  String? closingTime;
  String? openingTime;

  BusinessHours({this.closingTime, this.openingTime});

  factory BusinessHours.fromJson(Map<String, dynamic> json) => _$BusinessHoursFromJson(json);
  Map<String, dynamic> toJson() => _$BusinessHoursToJson(this);
}

@JsonSerializable()
class AvatarUtilityEntity {
  String? name;
  String? url;

  AvatarUtilityEntity({this.name, this.url});

  factory AvatarUtilityEntity.fromJson(Map<String, dynamic> json) => _$AvatarUtilityEntityFromJson(json);
  Map<String, dynamic> toJson() => _$AvatarUtilityEntityToJson(this);
}

@JsonSerializable()
class UtilityAddressEntity {
  String? country;
  String? district;
  String? province;
  String? street;
  String? ward;

  UtilityAddressEntity({
    this.country,
    this.district,
    this.province,
    this.street,
    this.ward,
  });

  factory UtilityAddressEntity.fromJson(Map<String, dynamic> json) => _$UtilityAddressEntityFromJson(json);
  Map<String, dynamic> toJson() => _$UtilityAddressEntityToJson(this);
}