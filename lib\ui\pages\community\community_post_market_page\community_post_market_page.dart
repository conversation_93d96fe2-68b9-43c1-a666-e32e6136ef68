import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/commons/screen_size.dart';
import 'package:real_care/models/entities/group/group_post_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/community_repository.dart';
import 'package:real_care/repositories/file_repository.dart';
import 'package:real_care/ui/buttons/h24-outlined-white-button.dart';
import 'package:real_care/ui/components/app_cache_image.dart';
import 'package:real_care/ui/components/my_app_bar.dart';
import 'package:real_care/ui/dialogs/app_dialog.dart';
import 'package:real_care/ui/pages/community/community_home_page/item_picker/item_picker_page.dart';
import 'package:real_care/ui/pages/community/community_post_market_page/community_post_market_page_cubit.dart';
import 'package:real_care/ui/pages/community/community_post_market_page/widgets/market_image_picker_widget.dart';
import 'package:real_care/ui/widgets/empty_list_widget.dart';
import 'package:real_care/ui/widgets/loading_indicator_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:real_care/models/entities/community_category_entity.dart';

class CommunityPostMarketPage extends StatefulWidget {
  final GroupPostEntity? itemPost;
  final bool isEdit;

  const CommunityPostMarketPage({
    Key? key,
    this.itemPost,
    this.isEdit = false,
  }) : super(key: key);

  @override
  State<CommunityPostMarketPage> createState() =>
      _CommunityPostMarketPageState();
}

class _CommunityPostMarketPageState extends State<CommunityPostMarketPage> {
  late final AppCubit _appCubit;

  final MarketImagePickerController _marketImagePickerController =
      MarketImagePickerController([]);

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _typeProductController = TextEditingController();
  final TextEditingController _priceProductController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  late CommunityPostMarketPageCubit _cubit;

  @override
  void dispose() {
    _marketImagePickerController.dispose();
    _titleController.dispose();
    _typeProductController.dispose();
    _priceProductController.dispose();
    _descriptionController.dispose();
    _cubit.close();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _appCubit = BlocProvider.of<AppCubit>(context);
    final uploadRepo = RepositoryProvider.of<UploadRepository>(context);
    final groupRepository = RepositoryProvider.of<CommunityRepository>(context);
    _cubit = CommunityPostMarketPageCubit(
      uploadRepository: uploadRepo,
      communityRepository: groupRepository,
    );
    _cubit.getData();

    _marketImagePickerController.addListener(() {
      _cubit.changeMedias(_marketImagePickerController.value);
    });

    /// for edit
    initEdit();
  }

  void initEdit() {
    if (widget.isEdit) {
      GroupPostEntity? groupPostEntity = widget.itemPost;
      if (groupPostEntity == null) return;

      /// Title
      _cubit.changeTitle(groupPostEntity.title ?? '');
      _titleController.text = groupPostEntity.title ?? '';

      /// Category
      if (groupPostEntity.category is Map) {
        Map<String, dynamic> map =
            groupPostEntity.category as Map<String, dynamic>;
        Category category = Category.fromJson(map);

        /// Convert category from post to category of list
        _cubit.setCommunityCategoryEntity(
            CommunityCategoryEntity(name: category.name, id: category.id));
        _typeProductController.text = category.name ?? '';
      }

      /// Price
      _cubit.changeAmount(groupPostEntity.amount.toString());
      _priceProductController.text = groupPostEntity.amount.toString();

      /// Description
      _cubit.changeDescription(groupPostEntity.detailedDescription ?? '');
      _descriptionController.text = groupPostEntity.detailedDescription ?? '';

      if (groupPostEntity.medias != null) {
        _cubit.changeMedias(groupPostEntity.medias!);
        _marketImagePickerController.value = groupPostEntity.medias!;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Positioned(
          top: 0,
          right: 0,
          left: 0,
          child: Image.asset(
            AppImages.bgHomeHeader,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.contain,
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          left: 0,
          child: Container(
            padding: EdgeInsets.only(top: ScreenSize.of(context).topPadding),
            child: MyAppBar(
                title: widget.isEdit ? 'Sửa bài đăng' : 'Đăng bán',
                onBackPressed: () {
                  Navigator.of(context).pop();
                },
                actionWidget: _buildActionPost()),
          ),
        ),
        Positioned(
          top: ScreenSize.of(context).topPadding + 45,
          right: 0,
          left: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 28),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(20.0),
                  topLeft: Radius.circular(20.0)),
              color: Colors.white,
            ),
            child: BlocBuilder<CommunityPostMarketPageCubit,
                    CommunityPostMarketPageState>(
                bloc: _cubit,
                buildWhen: (previousState, state) {
                  return previousState.getCategoryStatus !=
                      state.getCategoryStatus;
                },
                builder: (context, state) {
                  if (state.getCategoryStatus == LoadStatus.LOADING) {
                    return LoadingIndicatorWidget();
                  }
                  if (state.getCategoryStatus == LoadStatus.FAILURE) {
                    return EmptyListWidget(
                      text: 'Đã xảy ra lỗi, vui lòng kéo để thử lại',
                      onRefresh: _onRefreshData,
                    );
                  }
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 18,
                        ),
                        _buildCardUserInfo(),
                        const SizedBox(
                          height: 15,
                        ),
                        MarketImagePickerWidget(
                          controller: _marketImagePickerController,
                        ),
                        const SizedBox(
                          height: 11,
                        ),
                        _buildTextFieldTitle(),
                        const SizedBox(
                          height: 29,
                        ),
                        _buildTextFieldTypeProduct(),
                        const SizedBox(
                          height: 29,
                        ),
                        _buildTextFieldPriceProduct(),
                        const SizedBox(
                          height: 29,
                        ),
                        _buildTextFieldDescriptionDetail(),
                      ],
                    ),
                  );
                }),
          ),
        ),
      ],
    );
  }

  Widget _buildActionPost() {
    return BlocConsumer<CommunityPostMarketPageCubit,
            CommunityPostMarketPageState>(
        bloc: _cubit,
        listenWhen: (previous, current) {
          return previous.createPostStatus != current.createPostStatus ||
              previous.updatePostStatus != current.updatePostStatus;
        },
        listener: (context, state) {
          if (state.createPostStatus == LoadStatus.SUCCESS ||
              state.updatePostStatus == LoadStatus.SUCCESS) {
            AppDialog(
                context: context,
                dismissible: true,
                autoDismiss: true,
                titleStyle: AppTextStyle.blackS14Bold.copyWith(
                  color: AppColors.main,
                ),
                title: widget.isEdit
                    ? 'Sửa bài đăng thành công'
                    : 'Đăng bán thành công.',
                description:
                    'Bài đăng của bạn sẽ được kiểm duyệt trước khi hiển thị trên Market',
                onDismissed: () {
                  Navigator.of(context).pop(true);
                },
                icon: Image.asset(
                  AppImages.icCheckInSuccess,
                  height: 70,
                  width: 70,
                  fit: BoxFit.cover,
                )).show();
          }
          if (state.createPostStatus == LoadStatus.FAILURE ||
              state.updatePostStatus == LoadStatus.FAILURE) {
            AppDialog(
                context: context,
                dismissible: true,
                autoDismiss: true,
                title: 'Đã có lỗi xảy ra',
                onDismissed: () {},
                icon: Image.asset(
                  AppImages.icError,
                  height: 70,
                  width: 70,
                  fit: BoxFit.cover,
                )).show();
          }
        },
        buildWhen: (previous, current) {
          return previous.createPostStatus != current.createPostStatus ||
              previous.updatePostStatus != current.updatePostStatus ||
              previous.title != current.title ||
              previous.description != current.description ||
              previous.amount != current.amount ||
              previous.communityCategoryEntitySelected !=
                  current.communityCategoryEntitySelected ||
              previous.listFileInfo != current.listFileInfo;
        },
        builder: (context, state) {
          return H24OutlinedLightblueButton(
            title: "Đăng",
            isLoading: state.createPostStatus == LoadStatus.LOADING ||
                state.updatePostStatus == LoadStatus.LOADING,
            isDisable: !state.isValid(),
            onPressed: () {
              widget.isEdit
                  ? _cubit.updatePost(widget.itemPost?.id ?? '')
                  : _cubit.createPost();
            },
          );
        });
  }

  Widget _buildCardUserInfo() {
    return Container(
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border.all(width: 2, color: AppColors.blue009CFC),
              shape: BoxShape.circle,
            ),
            height: 42,
            width: 42,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: AppCacheImage(
                url: _appCubit.state.user?.images?.avatar ?? '',
                height: 42,
                width: 42,
              ),
            ),
          ),
          SizedBox(
            width: 10,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _appCubit.state.user?.name ?? "",
                style: AppTextStyle.blackS14Bold,
              ),
              SizedBox(
                height: 4,
              ),
              Text(
                'Niêm yết trên Market',
                style: AppTextStyle.greyS12,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextFieldTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tiêu đề bài đăng',
          style: AppTextStyle.blackS12Regular
              .copyWith(color: const Color(0xFF6F6F6F)),
        ),
        TextField(
          decoration: InputDecoration(
            labelStyle: AppTextStyle.blackS14Regular,
            contentPadding: EdgeInsets.only(top: 8, bottom: 12),
            suffixIconConstraints: BoxConstraints(maxHeight: 32, maxWidth: 32),
          ),
          controller: _titleController,
          onChanged: (value) {
            _cubit.changeTitle(value);
          },
        )
      ],
    );
  }

  Widget _buildTextFieldTypeProduct() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loại sản phẩm',
          style: AppTextStyle.blackS12Regular
              .copyWith(color: const Color(0xFF6F6F6F)),
        ),
        TextField(
          readOnly: true,
          textInputAction: TextInputAction.search,
          controller: _typeProductController,
          style: AppTextStyle.blackS16,
          maxLines: 1,
          decoration: InputDecoration(
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.textFieldEnabledBorder),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.textFieldFocusedBorder),
            ),
            disabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.textFieldDisabledBorder),
            ),
            fillColor: Colors.white,
            // hintStyle: hintStyle ?? AppTextStyle.greyS16,
            // hintText: hintText ?? "",
            isDense: true,
            contentPadding: EdgeInsets.only(top: 8, bottom: 12),
            suffixIcon: Image.asset(AppImages.icArrowDownBlack),
            suffixIconConstraints: BoxConstraints(maxHeight: 32, maxWidth: 32),
          ),
          onTap: () {
            _handleSelectCategory();
          },
          cursorColor: AppColors.gray,
        ),
      ],
    );
  }

  Widget _buildTextFieldPriceProduct() {
    var formatCurrency = CurrencyTextInputFormatter.currency(
        decimalDigits: 0, locale: 'vi', name: '');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Giá sản phẩm',
          style: AppTextStyle.blackS12Regular
              .copyWith(color: const Color(0xFF6F6F6F)),
        ),
        TextFormField(
          textInputAction: TextInputAction.done,
          keyboardType: TextInputType.number,
          controller: _priceProductController,
          style: AppTextStyle.blackS16,
          maxLines: 1,
          decoration: InputDecoration(
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColors.textFieldEnabledBorder),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColors.textFieldFocusedBorder),
              ),
              disabledBorder: UnderlineInputBorder(
                borderSide:
                    BorderSide(color: AppColors.textFieldDisabledBorder),
              ),
              fillColor: Colors.white,
              contentPadding: EdgeInsets.only(top: 8, bottom: 12),
              suffixIconConstraints: BoxConstraints(maxHeight: 32),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 16,
                    width: 1,
                    color: AppColors.gray,
                  ),
                  const SizedBox(
                    width: 9,
                  ),
                  Text(
                    'VND',
                    style: AppTextStyle.blackS14Regular,
                  ),
                  const SizedBox(
                    width: 17,
                  ),
                  Image.asset(AppImages.icArrowDownBlack)
                ],
              )),
          inputFormatters: [
            formatCurrency,
          ],
          cursorColor: AppColors.gray,
          onChanged: (value) {
            _cubit
                .changeAmount(formatCurrency.getUnformattedValue().toString());
          },
        ),
      ],
    );
  }

  Widget _buildTextFieldDescriptionDetail() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mô tả chi tiết',
          style: AppTextStyle.blackS12Regular
              .copyWith(color: const Color(0xFF6F6F6F)),
        ),
        TextField(
          controller: _descriptionController,
          keyboardType: TextInputType.multiline,
          onChanged: (value) {
            _cubit.changeDescription(value);
          },
          maxLines: null,
        )
      ],
    );
  }

  void _handleSelectCategory() async {
    final index = await showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.0),
      ),
      builder: (BuildContext context) {
        return ItemPickerPage(
          titlePage: 'Danh mục sản phẩm',
          data: _cubit.state.communityListCategory,
        );
      },
      context: context,
    );
    if (index == null) return;
    CommunityCategoryEntity result = _cubit.state.communityListCategory[index];
    _cubit.setCommunityCategoryEntity(result);
    _typeProductController.text = result.name ?? '';
  }

  Future<void> _onRefreshData() async {
    _cubit.getData();
  }
}
