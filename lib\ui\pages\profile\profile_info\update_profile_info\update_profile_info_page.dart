import 'dart:async';

import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/address/area_json_entity.dart';
import 'package:real_care/models/entities/identities_entity.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/auth_repository.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/app_page_widget.dart';
import 'package:real_care/ui/pages/common/verify_identity/verify_identity_page.dart';
import 'package:real_care/ui/pages/common/verify_otp/verify_otp_page.dart';
import 'package:real_care/ui/widgets/app_label_date_picker.dart';
import 'package:real_care/ui/widgets/app_snackbar.dart';
import 'package:real_care/ui/widgets/input/app_email_input.dart';
import 'package:real_care/ui/widgets/input/app_fullname_input.dart';
import 'package:real_care/ui/widgets/input/app_identify_address_input.dart';
import 'package:real_care/ui/widgets/input/app_identify_date_input.dart';
import 'package:real_care/ui/widgets/input/app_identify_number_input.dart';
import 'package:real_care/ui/widgets/input/app_map_address_input.dart';
import 'package:real_care/ui/widgets/input/app_phone_input.dart';
import 'package:real_care/ui/widgets/picker/app_gender_picker.dart';
import 'package:real_care/utils/dialog_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:real_care/utils/date_utils.dart';

import 'update_profile_info_cubit.dart';
import 'update_profile_info_state.dart';

class UpdateProfileInfoPage extends StatefulWidget {
  @override
  _UpdateProfileInfoPageState createState() => _UpdateProfileInfoPageState();
}

class _UpdateProfileInfoPageState extends State<UpdateProfileInfoPage> {
  final fullNameTextController = TextEditingController(text: "");
  final birthDateController = DatePickerController(dateTime: null);
  final genderController = GenderPickerController();
  final phoneTextController = TextEditingController(text: "");
  final emailTextController = TextEditingController(text: "");
  final identityNumberTextController = TextEditingController(text: "");
  final identityDateTextController = DatePickerController(dateTime: null);
  final identityAddressTextController = IdentifyAddressPickerController();
  final mapAddressController = TextEditingController(text: "");
  final mapPermanentAddressController = TextEditingController(text: "");
  UpdateProfileInfoCubit? _cubit;
  AppCubit? _appCubit;

  late StreamSubscription _showMessageSubscription;
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();

    //Cubit
    _appCubit = BlocProvider.of<AppCubit>(context);
    final repo = RepositoryProvider.of<AuthRepository>(context);
    _cubit = UpdateProfileInfoCubit(auth: repo, appCubit: _appCubit);
    //Setup data
    final user = _appCubit!.state.user!;
    _cubit!.setData(user.phone ?? "", user.email ?? "");
    //UI
    fullNameTextController.text = user.name ?? "";
    birthDateController.date = user.dob;
    genderController.gender = user.genderType;

    phoneTextController.text = user.phone ?? "";
    emailTextController.text = user.email ?? "";
    // [Fix]: Fix later
    // identityNumberTextController.text = user.identities?.last.value ?? "";
    // identityDateTextController.date = user.getIdentityDate;
    // identityAddressTextController.address =
    //     IdentifyAddressEntity(user.identity?.place ?? "");
    // mapAddressController.text = user.address ?? "";
    // mapPermanentAddressController.text = user.rootAddress ?? "";
    //Set initial cubit
    _cubit!.changeName(user.name ?? "");
    _cubit!.changeDateOfBirth(user.dob);
    _cubit!.changeGender(user.genderType);
    _cubit!.changePhone(user.phone ?? "");
    _cubit!.changeEmail(user.email ?? "");
    // [Fix]: Fix later
    // _cubit!.changeIdentifyNumber(user.identity?.value ?? "");
    // _cubit!.changeIdentifyDate(user.getIdentityDate);
    // _cubit!.changeDateOfBirth(user.birthday);
    // _cubit!.changeIdentifyAddress(user.identity?.place ?? "");
    // _cubit!.changeMapAddress(user.address ?? "");
    // _cubit!.changeMapRootAddress(user.rootAddress ?? "");

    _showMessageSubscription =
        _cubit!.showMessageController.stream.listen((event) {
      _showMessage(event);
    });
  }

  @override
  void dispose() {
    _cubit!.close();
    fullNameTextController.dispose();
    birthDateController.dispose();
    genderController.dispose();
    phoneTextController.dispose();
    emailTextController.dispose();
    identityNumberTextController.dispose();
    identityDateTextController.dispose();
    identityAddressTextController.dispose();
    mapAddressController.dispose();
    mapPermanentAddressController.dispose();
    _showMessageSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: AppPageWidget(
        title: S.of(context).update_profile_info,
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 27),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: 26),
                _buildBodyWidget(context),
                _buildUpdateButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBodyWidget(BuildContext context) {
    return Column(
      children: <Widget>[
        Text(
          S.of(context).reset_password_describe,
          style: AppTextStyle.blackS14,
          textAlign: TextAlign.left,
        ),
        SizedBox(height: 17),
        Container(
          child: _buildInfoField(),
          color: Colors.white,
        ),
      ],
    );
  }

  Widget _buildInfoField() {
    return BlocBuilder<AppCubit, AppState>(
      buildWhen: (prev, current) {
        return prev.user != current.user;
      },
      builder: (context, state) {
        // final allowEditIdentify =
        //     state.verifyAccountStatus != VerifyAccountStatus.APPROVED;
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AppFullNameInput(
              textEditingController: fullNameTextController,
              enabled: false,
              onChanged: (text) {
                _cubit!.changeName(text);
              },
              // textStyle: AppTextStyle.greyS16,
            ),
            Row(
              children: <Widget>[
                Expanded(
                  child: AppLabelDatePicker(
                    labelText: S.of(context).profile_date_birth,
                    controller: birthDateController,
                    minTime: AppConfig.birthMinDate,
                    maxTime: AppConfig.birthMaxDate,
                    enabled:
                        _appCubit!.state.user?.dob == null ? true : false,
                    textStyle: _appCubit!.state.user?.dob == null
                        ? null
                        : AppTextStyle.greyS16,
                    onChanged: (date) {
                      _cubit!.changeDateOfBirth(date);
                    },
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 40, right: 5),
                    child: AppGenderPicker(
                      controller: genderController,
                      // enabled: (_appCubit?.state.user?.gender ?? "").isEmpty
                      enabled: (_appCubit?.state.user?.gender?.toString() ?? "").isEmpty
                          ? true
                          : false,
                      onChanged: (value) {
                        _cubit!.changeGender(value);
                      },
                    ),
                  ),
                ),
              ],
            ),
            AppPhoneInput(
              labelText: S.of(context).profile_phone,
              textEditingController: phoneTextController,
              // enabled: false,
              // textStyle: AppTextStyle.greyS16,
              onChanged: (text) {
                _cubit!.changePhone(text);
              },
            ),
            AppEmailInput(
              textEditingController: emailTextController,
              onChanged: (text) {
                _cubit!.changeEmail(text);
              },
            ),
            AppIdentifyNumberInput(
              labelText: S.of(context).profile_identities,
              textEditingController: identityNumberTextController,
              // enabled: allowEditIdentify,
              // textStyle: allowEditIdentify ? null : AppTextStyle.greyS16,
              onChanged: (text) {
                _cubit!.changeIdentifyNumber(text);
              },
            ),
            AppIdentifyDateInput(
              controller: identityDateTextController,
              // enabled: allowEditIdentify,
              // textStyle: allowEditIdentify ? null : AppTextStyle.greyS16,
              onChanged: (date) {
                _cubit!.changeIdentifyDate(date);
              },
            ),
            AppIdentifyAddressInput(
              controller: identityAddressTextController,
              // enabled: allowEditIdentify,
              // textStyle: allowEditIdentify ? null : AppTextStyle.greyS16,
              onChanged: (address) {
                _cubit!.changeIdentifyAddress(address!.name);
              },
            ),
            AppMapAddressInput(
              labelText: 'Địa chỉ liên hệ',
              hintText: S.of(context).import_or_select_address,
              textEditingController: mapAddressController,
              highlightText: "",
              onChanged: (mapAddress) {
                _cubit!.changeMapAddress(mapAddress);
              },
            ),
            AppMapAddressInput(
              labelText: S.of(context).permanent_address,
              hintText: S.of(context).import_or_select_address,
              textEditingController: mapPermanentAddressController,
              highlightText: "",
              onChanged: (mapAddress) {
                _cubit!.changeMapRootAddress(mapAddress);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildUpdateButton() {
    return BlocConsumer<UpdateProfileInfoCubit, UpdateProfileInfoState>(
      bloc: _cubit,
      listenWhen: (prev, current) {
        return prev.updateProfileStatus != current.updateProfileStatus ||
            prev.verifyProfileStatus != current.verifyProfileStatus;
      },
      listener: (context, state) {
        if (state.verifyProfileStatus == LoadStatus.SUCCESS) {
          if (state.isChangePhone || state.isChangeEmail) {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) {
                return VerifyOTPPage(
                  phone: state.isChangePhone
                      ? state.phone
                      : _appCubit!.state.user?.phone,
                  email: state.isChangeEmail ? state.email : null,
                  token: _cubit!.state.tokenOTP ?? "",
                  type: VerifyOTPType.UPDATE_INFO,
                  isChangePhoneEmail: true,
                  name: state.name,
                  gender: state.gender.toAPICode,
                  address: state.mapAddress,
                  rootAddress: state.rootAddress,
                  dob: state.dateOfBirth?.toUtc().toString(),
                  identity: IdentitiesEntity(
                    value: state.identifyNumber,
                    date: state.identifyDate?.toUtc().toString(),
                    place: state.identifyAddress,
                  ),
                  isOTPPhoneScreen:
                      (state.isChangePhone && state.isChangeEmail) ||
                          state.isChangePhone,
                );
              }),
            );
          } else if (state.isChangeIdentifyNumber) {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) {
                print('${state.name}');
                return VerifyIdentityPage(
                  phone: state.phone,
                  email: state.email,
                  name: state.name,
                  gender: state.gender.toAPICode,
                  address: state.mapAddress,
                  rootAddress: state.rootAddress,
                  dob: state.dateOfBirth?.toUtc().toString(),
                  identity: IdentitiesEntity(
                    value: state.identifyNumber,
                    date: state.identifyDate?.toUtc().toString(),
                    place: state.identifyAddress,
                  ),
                );
              }),
            );
          } else {
            _cubit!.updateProfile();
          }
        }

        if (state.updateProfileStatus == LoadStatus.SUCCESS) {
          _showUpdateProfileSuccess();
        }
      },
      builder: (context, state) {
        final isValidData = state.isValidData;
        final isLoading = (state.updateProfileStatus == LoadStatus.LOADING ||
            state.verifyProfileStatus == LoadStatus.LOADING);
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 15),
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 36,
                    child: Text(
                      "Quay lại",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: AppBlueButton(
                  title: "Tiếp tục",
                  onPressed: () async {
                    String? phoneUser = state.isChangePhone
                        ? state.phone
                        : _appCubit!.state.user?.phone;
                    if (state.isChangePhoneOrEmail()) {
                      _cubit!.checkInformation(
                        phoneUser: phoneUser ?? "",
                        name: _appCubit!.state.user?.name ?? "",
                      );
                    } else if (state.isChangePhone || state.isChangeEmail) {
                      await _cubit!.sendOTP(
                        phoneUser: phoneUser ?? "",
                        name: _appCubit!.state.user?.name ?? "",
                      );
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) {
                          return VerifyOTPPage(
                            phone: phoneUser ?? "",
                            email: state.isChangeEmail ? state.email : null,
                            token: _cubit!.state.tokenOTP ?? "",
                            type: VerifyOTPType.UPDATE_INFO,
                            isChangePhoneEmail: true,
                            name: state.name,
                            gender: state.gender.toAPICode,
                            address: state.mapAddress,
                            rootAddress: state.rootAddress,
                            dob: state.dateOfBirth?.toUtc().toString(),
                            identity: IdentitiesEntity(
                              value: state.identifyNumber,
                              date: state.identifyDate?.toUtc().toString(),
                              place: state.identifyAddress,
                            ),
                            isOTPPhoneScreen:
                                (state.isChangePhone && state.isChangeEmail) ||
                                    state.isChangePhone,
                            nameUser: _appCubit!.state.user?.name ?? "",
                          );
                        }),
                      );
                    } else if (state.isChangeIdentifyNumber ||
                        state.isChangeRootAddress) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) {
                          print('${state.name}');
                          return VerifyIdentityPage(
                            phone: state.phone,
                            email: state.email,
                            name: state.name,
                            gender: state.gender.toAPICode,
                            address: state.mapAddress,
                            rootAddress: state.rootAddress,
                            dob: state.dateOfBirth?.toUtc().toString(),
                            identity: IdentitiesEntity(
                              value: state.identifyNumber,
                              date: state.identifyDate!.toUtc().toString(),
                              place: state.identifyAddress,
                            ),
                          );
                        }),
                      );
                    } else {
                      _cubit!.updateProfile();
                    }
                  },
                  isEnable: isValidData,
                  isLoading: isLoading,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showUpdateProfileSuccess() async {
    DialogUtils.showSuccessDialog(
      context,
      title: S.of(context).update_information_success,
      autoDismiss: true,
      dismissible: true,
      icon: Image.asset(AppImages.icUpdateProfileSuccess),
      onDismissed: () {
        _appCubit!.getProfile();
        Navigator.pop(context);
      },
    );
  }

  void _showMessage(SnackBarMessage message) {
    final context = _scaffoldKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(AppSnackBar(message: message));
    }
  }
}
