import 'dart:async';
import 'dart:io';

import 'package:another_flushbar/flushbar.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_flush_bar.dart';
import 'package:real_care/commons/app_gradient.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/commons/screen_size.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/group/comment_entity.dart';
import 'package:real_care/models/entities/group/group_post_entity.dart';
import 'package:real_care/models/entities/group/group_user_entity.dart';
import 'package:real_care/models/entities/group/survey_question.dart';
import 'package:real_care/models/entities/media_entity.dart';
import 'package:real_care/models/enums/comment_status.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/enums/setting_comment_type.dart';
import 'package:real_care/models/params/community/create_comment_param.dart';
import 'package:real_care/repositories/community_repository.dart';
import 'package:real_care/ui/components/avatar.dart';
import 'package:real_care/ui/components/group/group_item_comment_widget.dart';
import 'package:real_care/ui/components/group/group_item_post_body_widget.dart';
import 'package:real_care/ui/pages/community/detail_group_post/detail_group_post_cubit.dart';
import 'package:real_care/ui/pages/community/widget/app_bar_shadow.dart';
import 'package:real_care/ui/pages/community/widget/chat_group_bottom_sheet/chat_group_bottom_sheet_widget.dart';
import 'package:real_care/ui/pages/community/widget/loading/loading_group_item_comment_widget.dart';
import 'package:real_care/ui/pages/community/widget/loading/loading_group_item_post_body_widget.dart';
import 'package:real_care/ui/pages/community/widget/loading/loading_group_item_post_header_widget.dart';
import 'package:real_care/ui/widgets/error_list_widget.dart';
import 'package:real_care/ui/widgets/loading_more_row_widget.dart';
import 'package:real_care/utils/logger.dart';
import 'package:real_care/utils/utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

class DetailGroupPostPage extends StatefulWidget {
  final bool showGroupName;
  final GroupPostEntity? postData;

  //final bool isShowActionComment;
  final Function({
    List<File>? files,
    List<MediaEntity>? fileHasUpdate,
    String? content,
    String? thumbnail,
    String? groupId,
    List<SurveyQuestion>? poll,
  })? editPostCallback;

  const DetailGroupPostPage({
    Key? key,
    required this.postData,
    this.showGroupName = false,
    this.editPostCallback,
    //this.isShowActionComment = true,
  }) : super(key: key);

  @override
  _DetailGroupPostPageState createState() => _DetailGroupPostPageState();
}

class _DetailGroupPostPageState extends State<DetailGroupPostPage>
    with WidgetsBindingObserver {
  ScrollController _scrollController = ScrollController();
  TextEditingController _commentController = TextEditingController();
  FocusNode _commentFocus = FocusNode();
  late DetailGroupPostCubit _detailGroupPostCubit;
  late AppCubit _appCubit;
  bool _needRefresh = false;

  @override
  void initState() {
    super.initState();
    _detailGroupPostCubit = DetailGroupPostCubit(
      communityRepository: RepositoryProvider.of<CommunityRepository>(context),
    );
    _appCubit = BlocProvider.of<AppCubit>(context);

    _detailGroupPostCubit.getDetailPost(postId: widget.postData?.id ?? "");
    _detailGroupPostCubit.getListCommentOfPost(
        postId: widget.postData?.id ?? "");
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  _onScrollToBottom() {
    final maxScroll = _scrollController.position.maxScrollExtent + 75;
    _scrollController.animateTo(maxScroll,
        duration: Duration(milliseconds: 1000), curve: Curves.fastOutSlowIn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: WillPopScope(
          onWillPop: () async {
            _onBack();
            return true;
          },
          child: Stack(
            children: [
              BlocConsumer<DetailGroupPostCubit, DetailGroupPostState>(
                  bloc: _detailGroupPostCubit,
                  buildWhen: (prev, current) =>
                  prev.getDetailPostStatus != current.getDetailPostStatus ||
                      prev.likePostStatus != current.likePostStatus ||
                      prev.getListCommentStatus != current.getListCommentStatus,
                  listener: (context, state) {},
                  builder: (context, state) {
                    return Column(
                      children: [
                        _buildHeaderWidget(),
                        Expanded(child: _buildBodyWidget),
                        Visibility(
                          visible: widget.postData?.getNotiCommentPost != CommentStatus.HIDDEN,
                          child: _buildCommentWidget,
                        ),
                        //_loadMoreWidget,
                        // _buildSettingWidget,
                        // _buildCommentType,
                      ],
                    );
                  }),
              BlocListener<DetailGroupPostCubit, DetailGroupPostState>(
                bloc: _detailGroupPostCubit,
                listenWhen: (previous, current) =>
                previous.createCommentStatus != current.createCommentStatus,
                listener: (context, state) {
                  if (state.createCommentStatus == LoadStatus.SUCCESS) {
                    _onScrollToBottom();
                  }
                },
                child: Container(),
              )
            ],
          ),
        ),
      ),
    );

  }

  Widget get _buildErrorWidget {
    return Scaffold(
      body: Stack(
        children: [
          ErrorListWidget(onRefresh: _onRefresh),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: AppBarShadowCustom(
              onBack: _onBack,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderWidget() {
    return Container(
      decoration: BoxDecoration(
        gradient: AppGradient.linearGradientLeftToRight,
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(25),
        ),
      ),
      height: 60.0 + ScreenSize.of(context).topPadding,
      padding: EdgeInsets.only(top: ScreenSize.of(context).topPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              _onBack();
            },
            child: Container(
              width: 40,
              height: 40,
              child: Image.asset(AppImages.icBack),
            ),
          ),
          Avatar(
            url: _detailGroupPostCubit.state.detailPostData?.user?.avatar,
            avatarSize: 32,
          ),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  child: Text(
                    widget.postData?.user?.name ?? "",
                    style: AppTextStyle.whiteS14Bold,
                  ),
                ),
                SizedBox(height: 4),
                Container(
                  child: Text(
                    widget.postData?.getDateUpdatePost ?? "",
                    style: AppTextStyle.whiteS12,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            child: Image.asset(
              AppImages.icThreeDot,
              color: AppColors.background,
            ),
          ),
          SizedBox(width: 15),
        ],
      ),
    );
  }

  Widget get _buildBodyWidget {
    return BlocBuilder<DetailGroupPostCubit, DetailGroupPostState>(
      bloc: _detailGroupPostCubit,
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: _onRefresh,
          child: Stack(
            children: [
              ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.only(top: 15, bottom: 25),
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: _detailGroupPostCubit.state.listComments.length + 2,
                itemBuilder: (context, index) {
                  if (index == 0) {
                    if (_detailGroupPostCubit.state.getDetailPostStatus ==
                        LoadStatus.LOADING) {
                      return Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        child: LoadingGroupItemPostBodyWidget(),
                      );
                    }
                    return BlocBuilder<DetailGroupPostCubit,
                        DetailGroupPostState>(
                      bloc: _detailGroupPostCubit,
                      buildWhen: (previous, current) =>
                      previous.totalPostComments !=
                          current.totalPostComments,
                      builder: (context, state) {
                        return GroupItemPostBodyWidget(
                          contentText: _detailGroupPostCubit
                              .state.detailPostData?.description ??
                              "",
                          countFavorite: (_detailGroupPostCubit
                              .state.detailPostData?.totalFavorites ??
                              0),
                          countComment: state.totalPostComments,
                          countShare: (_detailGroupPostCubit
                              .state.detailPostData?.totalShares ??
                              0)
                              .toString(),
                          isFavorite: _detailGroupPostCubit
                              .state.detailPostData?.isFavorite ??
                              false,
                          isSeenComment: false,
                          // ?? false,
                          isShared: _detailGroupPostCubit
                              .state.detailPostData?.isShared ??
                              false,
                          initReadMore: false,
                          favoritePressed: () {
                            if (widget.postData?.requestStatusPost ==
                                "WAITING") {
                              AppFlushBar.showError(
                                context,
                                message:
                                "Bài viết đang chờ duyệt.\nBạn chưa thể tương tác trong bài viết này.",
                              );
                            } else if (_detailGroupPostCubit
                                .state.detailPostData?.isFavorite ??
                                false) {
                              _detailGroupPostCubit.disFavoritePost(
                                  postId: _detailGroupPostCubit
                                      .state.detailPostData?.id);
                            } else {
                              _detailGroupPostCubit.favoritePost(
                                  postId: _detailGroupPostCubit
                                      .state.detailPostData?.id);
                            }
                            _needRefresh = true;
                          },
                          commentPressed: () => {},
                          mediasPressed: (index) {
                            Utils.showMediasDialog(
                                context,
                                _detailGroupPostCubit
                                    .state.detailPostData?.medias ??
                                    [],
                                selectedIndex: index);
                          },
                          medias: _detailGroupPostCubit
                              .state.detailPostData?.medias ??
                              [],
                          // sharePressed: _handleSharePostPressed,
                          sharePressed: () {
                            _handleSharePostPressed(
                                postData:
                                _detailGroupPostCubit.state.detailPostData);
                          },
                        );
                      },
                    );
                  } else if (index == 1) {
                    return _buildNotiComment();
                  } else {

                    /// Build item thông báo ẩn bình luận
                    if (_detailGroupPostCubit.state.detailPostData?.settingCommentType == SettingCommentType.HIDDEN) {
                      return Container(
                        child: Text(
                          S.current.common_hidden_comment,
                          style: AppTextStyle.blackS14Bold,
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 15),
                      );
                    }

                    if (_detailGroupPostCubit.state.getListCommentStatus ==
                        LoadStatus.LOADING) {
                      return Container(
                        child:
                        LoadingGroupItemCommentWidget(canBuildChild: true),
                        padding: EdgeInsets.symmetric(horizontal: 15),
                      );
                    }
                    final CommentEntity? comment =
                    _detailGroupPostCubit.state.listComments[index - 2];
                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: BlocBuilder<DetailGroupPostCubit,
                          DetailGroupPostState>(
                        bloc: _detailGroupPostCubit,
                        buildWhen: (previous, current) =>
                        previous.listComments != current.listComments,
                        builder: (context, state) {
                          return GroupItemCommentWidget(
                            avatar: comment?.user?.avatarThumbnail ?? "",
                            name: comment?.user?.name ?? "",
                            contentText: comment?.description ?? '',
                            time: comment?.createdDate ?? "",
                            countFavorite:
                            (comment?.totalFavorites ?? 0).toString(),
                            countComment:
                            (comment?.totalComments ?? 0).toString(),
                            isFavorite: comment?.isFavorite ?? false,
                            isSeenComment: comment?.isComment ?? false,
                            //isItemAction: widget.isShowActionComment,
                            // isSelectedComment: (index - 1) == state.currentCommentIndex && state.isReply,
                            media: (comment?.medias ?? []).isNotEmpty
                                ? comment?.medias!.first
                                : null,
                            subComments: List.from(comment?.subComments ?? []),
                            isEnableLoadMore: true,
                            isLoadMore: true,
                            visibleLine: false,
                            onLoadMoreSubComment: () {
                              // _cubit.getListSubComment(commentId: comment.id ?? "", index: index - 1);
                            },
                            // isCreateSubComment: state.createSubCommentStatus == LoadStatus.LOADING &&
                            //     (index - 1) == state.currentCommentIndex,
                            isCreateSubComment: false,
                            // isMyComment: state.listComments[index - 1].user?.id == _cubit.currentUser?.id,
                            isMyComment: true,
                            // avatarPressed: () => _handleGroupUserPressed(user: comment.user),
                            avatarPressed: () => {},
                            // userNamePressed: () => _handleGroupUserPressed(user: comment.user),
                            userNamePressed: () => {},
                            // favoritePressed: () => _cubit.likeComment(index: index - 1),
                            favoritePressed: () {
                              _detailGroupPostCubit.favouriteComment(
                                  commentId: comment?.id ?? "");
                            },
                            // onViewLikePressed: () => DialogUtils.showBottomListLikePicker(context,
                            //     type: ViewerType.COMMENT_LIKE, id: comment.id ?? ''),
                            onViewLikePressed: () => {},
                            // commentPressed: () =>
                            //     _handleCommentInPostPressed(index - 1, state.postData!.settingCommentType),
                            // contentLongPressed: () =>
                            //     _handleSettingCommentPressed(index - 1, state.postData!.settingCommentType),
                            // childContentLongPressed: (position) =>
                            //     _handleSettingChildCommentPressed(index: position, parentIndex: index - 1),
                            // childAvatarPressed: (position) =>
                            //     _handleGroupUserPressed(user: comment.subComments?[position]?.user),
                            // childUserNamePressed: (position) =>
                            //     _handleGroupUserPressed(user: comment.subComments?[position]?.user),
                            // childFavoritePressed: (position) =>
                            //     _cubit.likeComment(parentIndex: index - 1, index: position),
                            // childViewLikePressed: (position) => DialogUtils.showBottomListLikePicker(context,
                            //     type: ViewerType.COMMENT_LIKE, id: comment.subComments?[position]?.id ?? ''),
                          );
                        },
                      ),
                    );
                  }
                },
              ),
              Positioned.fill(
                bottom: 15,
                child: Visibility(
                  // visible: _isLoadMore,
                  visible: false,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: LoadingMoreRowWidget(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotiComment() {
    return BlocBuilder<DetailGroupPostCubit, DetailGroupPostState>(
      bloc: _detailGroupPostCubit,
      buildWhen: (previous, current) {
        return true;
      },
      builder: (context, state) {
        if (widget.postData?.getNotiCommentPost == CommentStatus.HIDDEN) {
          return Padding(
            padding: const EdgeInsets.only(left: 15),
            child: Text(
              "Một số bình luận được ẩn",
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }

  Widget get _buildCommentWidget {
    return BlocBuilder<DetailGroupPostCubit, DetailGroupPostState>(
      bloc: _detailGroupPostCubit,
      buildWhen: (previous, current) {
        return true;
      },
      builder: (context, state) {
        if (widget.postData?.getNotiCommentPost == CommentStatus.OFF) {
          return Column(
            children: [
              Divider(),
              Text(
                "Bài viết đã tắt tính năng bình luận",
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: AppColors.gradientStart,
                ),
              ),
              Divider(),
            ],
          );
        } else {
          return ChatGroupBottomSheetWidget(
            isCreateNew: true,
            textController: _commentController,
            textFocus: _commentFocus,
            sendPressed: (file, isCreateNew) {
              if (widget.postData?.requestStatusPost == "WAITING") {
                AppFlushBar.showError(
                  context,
                  message:
                  "Bài viết đang chờ duyệt.\nBạn chưa thể bình luận trong bài viết này.",
                );
              } else if (_commentController.text.isNotEmpty) {
                CreateCommentParam param = CreateCommentParam(
                  postId: _detailGroupPostCubit.state.detailPostData?.id,
                  description: _commentController.text,
                );

                _detailGroupPostCubit.createComment(
                  params: param,
                  user: GroupUserEntity(
                    id: _appCubit.state.user?.id,
                    email: _appCubit.state.user?.email,
                    name: _appCubit.state.user?.name,
                    phone: _appCubit.state.user?.phone,
                    avatar: _appCubit.state.user?.images?.avatar,
                  ),
                );
                _commentController.clear();
              }
            },
            removeReply: () {},
            cancelEditComment: () {},
          );
        }
      },
    );
  }

  Widget get _buildCommentType {
    return Container(child: Text("_buildCommentType"));
    // return Visibility(
    //   visible: true,
    //   child: Container(
    //     color: AppColors.background,
    //     width: double.infinity,
    //     child: SafeArea(
    //       bottom: true,
    //       child: Column(
    //         children: [
    //           Visibility(
    //             visible: _cubit.isHideComment,
    //             child: Padding(
    //               padding: const EdgeInsets.symmetric(vertical: 12),
    //               child: Text(
    //                 state.postData?.settingCommentType.contentPostTitle ?? "",
    //                 style: AppTextStyle.redS12.copyWith(fontStyle: FontStyle.italic, color: AppColors.textRedHind),
    //               ),
    //             ),
    //           ),
    //           Visibility(
    //             visible: _cubit.isLockComment,
    //             child: Padding(
    //               padding: const EdgeInsets.symmetric(vertical: 12),
    //               child: Text(
    //                 state.postData?.settingCommentType.contentPostTitle ?? "",
    //                 style: AppTextStyle.redS14.copyWith(color: AppColors.textRedHind),
    //               ),
    //             ),
    //           )
    //         ],
    //       ),
    //     ),
    //   ),
    // );
  }

  Widget get _buildAllowCommentWidget {
    return Visibility(
      visible: _detailGroupPostCubit.state.detailPostData?.settingCommentType ==
          SettingCommentType.OFF,
      child: Column(
        children: [
          Container(height: 0.5, color: AppColors.main),
          Container(
            height: 40,
            alignment: Alignment.center,
            child: Text(
              SettingCommentType.OFF.contentPostTitle ,
              style: AppTextStyle.redS14
                  .copyWith(fontStyle: FontStyle.italic, color: AppColors.main),
            ),
          ),
          Container(height: 0.5, color: AppColors.main),
        ],
      ),
    );
  }

  Future<void> _checkInternetConnectivity() async {
    var result = await Connectivity().checkConnectivity();
    // if (result == ConnectivityResult.none) {
    //   _cubit.checkInternet(true);
    //   //_cubit.unSubscribe(destination: WSSocketConfig.GROUP_COMMENT.destination, postId: widget.postId);
    // } else {
    //   _cubit.checkInternet(false);
    // }
  }

  void _onScroll() {
    // final maxScroll = _scrollController.position.maxScrollExtent;
    // final currentScroll = _scrollController.position.pixels;
    // if (maxScroll - currentScroll <= 100.0 &&
    //     _cubit.state.commentPage < _cubit.state.totalCommentPages &&
    //     !_isLoadMore) {
    //   _isLoadMore = true;
    //   _cubit.loadMoreListComment();
    // }
  }

  void _clearCommentWidgetData({required bool isClearText}) {
    // if (isClearText) {
    //   _commentController.text = '';
    // }
    // FocusScope.of(context).unfocus();
  }

  Future<void> _onRefresh() async {
    _detailGroupPostCubit.getDetailPost(postId: widget.postData?.id ?? "");
    _detailGroupPostCubit.getListCommentOfPost(
        postId: widget.postData?.id ?? "");
  }

  void _onBack() {
    Get.back(
        result: GroupPostEntity(
          id: widget.postData?.id,
          user: widget.postData?.user,
          group: widget.postData?.group,
          medias: widget.postData?.medias,
          isPinned: widget.postData?.isPinned,
          isFavorite: _detailGroupPostCubit.state.detailPostData?.isFavorite,
          isComment: widget.postData?.isComment,
          isShared: widget.postData?.isShared,
          isAdmin: widget.postData?.isAdmin,
          totalFavorites:
          _detailGroupPostCubit.state.detailPostData?.totalFavorites,
          totalComments: _detailGroupPostCubit.state.totalPostComments,
          totalShares: widget.postData?.totalShares,
          comments: _detailGroupPostCubit.state.listComments,
          favorites: _detailGroupPostCubit.state.detailPostData?.favorites,
          allowComment: widget.postData?.allowComment,
          status: widget.postData?.status,
          requestStatusPost: widget.postData?.requestStatusPost,
          histories: widget.postData?.histories,
          description: widget.postData?.description,
          modifiedBy: widget.postData?.modifiedBy,
          createdBy: widget.postData?.createdBy,
          createdDate: widget.postData?.createdDate,
          modifiedDate: widget.postData?.modifiedDate,
          eventName: widget.postData?.eventName,
          actionName: widget.postData?.actionName,
          v: widget.postData?.v,
          shareUrl: widget.postData?.shareUrl,
          isFollow: widget.postData?.isFollow,
          poll: widget.postData?.poll,
          category: widget.postData?.category,
          sellStatus: widget.postData?.sellStatus,
          isMarketPost: widget.postData?.isMarketPost,
          detailedDescription: widget.postData?.detailedDescription,
          amount: widget.postData?.amount,
          title: widget.postData?.title,
          isSave: widget.postData?.isSave,
        ));
  }

  void _handleSharePostPressed({required GroupPostEntity? postData}) async {
    if ((postData?.shareUrl ?? "").isNotEmpty) {
      await Share.share(postData?.shareUrl ?? "");
    } else {
      Flushbar(
        icon: Icon(Icons.error, size: 32, color: Colors.white),
        shouldIconPulse: false,
        message: S.current.alertTitle_linkHasNotUpdated,
        duration: Duration(seconds: 3),
        flushbarPosition: FlushbarPosition.TOP,
        margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
        borderRadius: BorderRadius.all(Radius.circular(16)),
      )..show(context);
    }
  }
}

class DetailPostArgument {
  GroupPostEntity? postData;

  DetailPostArgument({
    required this.postData,
  });
}
