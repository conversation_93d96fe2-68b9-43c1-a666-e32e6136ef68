import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/file_of_team_entity.dart';
import 'package:real_care/models/entities/chat_rocket/last_message_entity.dart';
import 'package:real_care/models/entities/chat_rocket/room_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'info_room_entity.g.dart';

@JsonSerializable()
class InfoRoomEntity extends Equatable {
  @Json<PERSON><PERSON>(name: "_id")
  final String? id;
  @Json<PERSON>ey(name: "fname")
  final String? fname;
  @<PERSON>son<PERSON>ey(name: "teamMain")
  final bool? teamMain;
  @<PERSON><PERSON><PERSON><PERSON>(name: "name")
  final String? name;
  @J<PERSON><PERSON><PERSON>(name: "t")
  final String? t;
  @J<PERSON><PERSON><PERSON>(name: "msgs")
  final double? msgs;
  @<PERSON><PERSON><PERSON><PERSON>(name: "usernames")
  final List<String>? userNames;
  @Json<PERSON>ey(name: "ts")
  final String? ts;
  @<PERSON><PERSON><PERSON><PERSON>(name: "usersCount")
  final int? usersCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "uids")
  final List<String>? uids;
  @<PERSON><PERSON><PERSON><PERSON>(name: "u")
  final UEntity? u;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ro")
  final bool? ro;
  @Json<PERSON>ey(name: "teamId")
  final String? teamId;
  @JsonKey(name: "default")
  final bool? def;
  @JsonKey(name: "sysMes")
  final bool? sysMes;
  @JsonKey(name: "_updatedAt")
  final String? updatedAt;
  @JsonKey()
  final LastMessageEntity? lastMessage;
  @JsonKey()
  final String? description;
  @JsonKey()
  final bool? broadcast;
  @JsonKey()
  final bool? encrypted;

  @JsonKey()
  final List<FileOfTeamEntity>? files;

  InfoRoomEntity({
    this.id,
    this.fname,
    this.teamMain,
    this.userNames,
    this.name,
    this.t,
    this.msgs,
    this.ts,
    this.usersCount,
    this.u,
    this.ro,
    this.teamId,
    this.def,
    this.sysMes,
    this.updatedAt,
    this.files,
    this.lastMessage,
    this.description,
    this.broadcast,
    this.encrypted,
    this.uids,
  });

  factory InfoRoomEntity.fromJson(Map<String, dynamic> json) => _$InfoRoomEntityFromJson(json);

  Map<String, dynamic> toJson() => _$InfoRoomEntityToJson(this);

  @override
  List<Object?> get props => [
        this.id,
        this.fname,
        this.teamMain,
        this.name,
        this.userNames,
        this.t,
        this.msgs,
        this.ts,
        this.usersCount,
        this.u,
        this.ro,
        this.teamId,
        this.def,
        this.sysMes,
        this.updatedAt,
        this.uids,
      ];
}

@JsonSerializable()
class InfoRoomResponse extends Equatable {
  @JsonKey(name: "room")
  final InfoRoomEntity? room;
  @JsonKey(name: "success")
  final bool? success;

  factory InfoRoomResponse.fromJson(Map<String, dynamic> json) => _$InfoRoomResponseFromJson(json);

  Map<String, dynamic> toJson() => _$InfoRoomResponseToJson(this);

  InfoRoomResponse({
    this.room,
    this.success,
  });

  @override
  // TODO: implement props
  List<Object?> get props => [
        this.room,
        this.success,
      ];
}
