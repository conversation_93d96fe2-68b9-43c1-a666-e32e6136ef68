import 'package:json_annotation/json_annotation.dart';

part 'token_entity.g.dart';

@JsonSerializable()
class TokenEntity {
  @Json<PERSON>ey(name: 'access_token')
  String? accessToken;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_in')
  int? expiresIn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_expires_in')
  int? refreshExpiresIn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_token')
  String? refreshToken;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'token_type')
  String? tokenType;

  @J<PERSON><PERSON><PERSON>(name: 'not-before-policy')
  int? notBeforePolicy;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'session_state')
  String? sessionState;

  @J<PERSON><PERSON><PERSON>(name: 'scope')
  String? scope;

  TokenEntity({
    this.accessToken,
    this.expiresIn,
    this.refreshExpiresIn,
    this.refreshToken,
    this.tokenType,
    this.notBeforePolicy,
    this.sessionState,
    this.scope,
  });

  factory TokenEntity.fromJson(Map<String, dynamic> json) => _$TokenEntityFromJson(json);
  Map<String, dynamic> toJson() => _$TokenEntityToJson(this);
}