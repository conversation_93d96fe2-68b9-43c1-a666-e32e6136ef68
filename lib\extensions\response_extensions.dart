import 'package:real_care/models/responses/detail_response.dart';
import 'package:real_care/models/responses/general_response.dart';

extension ResponseExtension<T> on Future<GeneralResponse<T>> {
  Future<T> get data async {
    final response = await this;
    return response.data!;
  }
}

extension DetailResponseExtension<T> on Future<DetailResponse<T>> {
  Future<T> get data async {
    final response = await this;
    return response.data!;
  }
} 