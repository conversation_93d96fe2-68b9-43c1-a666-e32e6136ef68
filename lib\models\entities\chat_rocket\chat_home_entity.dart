import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/last_message_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'chat_home_entity.g.dart';

@JsonSerializable()
class ChatHomeEntity extends Equatable {
  @Json<PERSON>ey(name: "_id")
  final String? id;
  @Json<PERSON>ey()
  final String? t;
  @Json<PERSON><PERSON>()
  final List<String>? usernames;
  @<PERSON>son<PERSON><PERSON>()
  final int? usersCount;
  @Json<PERSON>ey()
  final  String? ts;
  @<PERSON><PERSON><PERSON><PERSON>()
  final List<String>? uids;
  @<PERSON><PERSON><PERSON><PERSON>(name: "_updatedAt")
  final String? updatedAt;
  @J<PERSON><PERSON><PERSON>()
  final LastMessageEntity? lastMessage;
  @Json<PERSON>ey()
  final String? lm;

  ChatHomeEntity({
    this.id,
    this.t,
    this.usernames,
    this.usersCount,
    this.ts,
    this.uids,
    this.updatedAt,
    this.lastMessage,
    this.lm,
  });

  factory ChatHomeEntity.fromJson(Map<String, dynamic> json) => _$ChatHomeEntityFromJson(json);

  Map<String, dynamic> toJson() => _$ChatHomeEntityToJson(this);

  @override
  List<Object> get props => [
        this.id!,
        this.t!,
        this.usernames!,
        this.usersCount!,
        this.ts!,
        this.uids!,
        this.updatedAt!,
        this.lastMessage!,
        this.lm!,
      ];
}
