import 'package:json_annotation/json_annotation.dart';

part 'regulation_folder_entitty.g.dart';

@JsonSerializable()
class RegulationFolderEntity {

  @Json<PERSON>ey()
  String? name;

  @JsonKey()
  String? projectId;

  @JsonKey()
  String? status;

  @J<PERSON><PERSON>ey()
  String? id;

  @J<PERSON><PERSON>ey()
  String? createdDate;

  RegulationFolderEntity({
    this.name,
    this.projectId,
    this.status,
    this.id,
    this.createdDate,
  });

  factory RegulationFolderEntity.fromJson(Map<String, dynamic> json) => _$RegulationFolderEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$RegulationFolderEntityToJson(this);
}
