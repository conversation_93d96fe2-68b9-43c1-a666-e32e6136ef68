import 'package:json_annotation/json_annotation.dart';

import 'check_in_info.dart';
part 'check_in_of_building.g.dart';

@JsonSerializable()
class CheckInOfBuilding {
  @Json<PERSON>ey(name: 'key')
  String? buildingName;
  @Json<PERSON>ey(name: 'mapData')
  List<CheckInInfo>? listCheckInInfo;

  CheckInOfBuilding({this.buildingName, this.listCheckInInfo});

  factory CheckInOfBuilding.fromJson(Map<String, dynamic> json) => _$CheckInOfBuildingFromJson(json);
  
  Map<String, dynamic> toJson() => _$CheckInOfBuildingToJson(this);
}
