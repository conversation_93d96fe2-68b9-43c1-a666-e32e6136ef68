import 'package:real_care/models/entities/group/survey_question.dart';
import 'package:json_annotation/json_annotation.dart';

part 'poll_entity.g.dart';

@JsonSerializable()
class Poll {
  @JsonKey()
  String? createdDate;
  @JsonKey()
  List<SurveyQuestion?>? questions;
  @JsonKey()
  String? title;

  Poll({
    this.createdDate,
    this.questions,
    this.title,
  });

  Poll.fromJson(Map<String, dynamic> json) {
    createdDate = json['createdDate'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions!.add(SurveyQuestion.fromJson(v));
      });
    }
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdDate'] = this.createdDate;
    if (this.questions != null) {
      data['questions'] = (this.questions ?? []).map((v) => v!.toJson()).toList();
    }
    data['title'] = this.title;
    return data;
  }
}
