import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';

class AppFlushBar {
  static void showInfo(BuildContext context,
      {String? title, String? message}) {
    Flushbar(
      icon: Icon(Icons.verified, size: 32, color: Colors.white),
      shouldIconPulse: false,
      message: message,
      title: title,
      duration: Duration(seconds: 3),
      flushbarPosition: FlushbarPosition.TOP,
      margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
      borderRadius: BorderRadius.all(Radius.circular(16)),
    ).show(context);
  }

  static void showError(BuildContext context,
      {String? title, String? message}) {
    Flushbar(
      icon: Icon(Icons.error, size: 32, color: Colors.white),
      shouldIconPulse: false,
      message: message,
      title: title,
      duration: Duration(seconds: 3),
      flushbarPosition: FlushbarPosition.TOP,
      margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
      borderRadius: BorderRadius.all(Radius.circular(16)),
    ).show(context);
  }
}