import 'package:real_care/models/entities/chat_rocket/members_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'member_team_response.g.dart';

@JsonSerializable()
class MemberTeamResponse {
  @JsonKey()
  List<MembersEntity>? members;
  @Json<PERSON>ey()
  bool? success;
  @Json<PERSON>ey()
  int? count;
  @Json<PERSON>ey()
  int? offset;
  @Json<PERSON>ey()
  int? total;

  MemberTeamResponse({
    this.success,
    this.members,
    this.count,
    this.offset,
    this.total,
  });

  factory MemberTeamResponse.fromJson(Map<String, dynamic> json) => _$MemberTeamResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MemberTeamResponseToJson(this);
}
