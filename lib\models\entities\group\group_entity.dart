import 'package:real_care/models/entities/group/group_user_entity.dart';
import 'package:real_care/models/enums/group/group_join_type.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_entity.g.dart';

@JsonSerializable()
class GroupEntity {
  @JsonKey(name: 'totalPosts')
  int? totalPosts;
  @Json<PERSON>ey(name: 'totalComments')
  int? totalComments;
  @JsonKey()
  int? totalLikes;
  @Json<PERSON>ey()
  int? totalShares;
  @Json<PERSON>ey(name: 'type')
  String? type;
  @JsonKey()
  String? joinType;
  @Json<PERSON><PERSON>(name: 'showType')
  String? showType;
  @Json<PERSON>ey(name: 'name')
  String? name;
  @J<PERSON><PERSON><PERSON>(name: 'coverImage')
  String? coverImage;
  @JsonKey(name: 'id')
  String? id;
  @JsonKey(name: 'modifiedDate')
  String? modifiedDate;
  @Json<PERSON>ey(name: 'totalMembers')
  int? totalMembers;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'isAdmin')
  bool? isAdmin;
  @J<PERSON><PERSON><PERSON>(name: 'isMember')
  bool? isMember;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'isSendRequest')
  bool? isSendRequest;
  @JsonKey()
  int? totalPendingPosts;
  @JsonKey()
  int? totalPendingMembers;
  @JsonKey()
  String? description;
  @JsonKey()
  String? shareUrl;
  @JsonKey()
  List<GroupUserEntity?>? members;
  @JsonKey()
  bool? isFollow;
  @JsonKey()
  List<GroupUserEntity?>? owners;
  @JsonKey()
  String? groupChatId;
  @JsonKey()
  GroupChatInfoEntity? groupChatInfo;

  GroupEntity({
    this.totalPosts,
    this.totalComments,
    this.totalLikes,
    this.totalShares,
    this.type,
    this.joinType,
    this.showType,
    this.name,
    this.coverImage,
    this.id,
    this.modifiedDate,
    this.totalMembers,
    this.isAdmin,
    this.isMember,
    this.isSendRequest,
    this.totalPendingPosts,
    this.totalPendingMembers,
    this.description,
    this.shareUrl,
    this.members,
    this.isFollow,
    this.owners,
    this.groupChatId,
    this.groupChatInfo,
  }) {
    if (this.isAdmin == null) this.isAdmin = false;
    if (this.isMember == null) this.isMember = false;
  }

  GroupEntity copyWith({
    int? totalPosts,
    int? totalComments,
    int? totalLikes,
    int? totalShares,
    String? type,
    String? joinType,
    String? showType,
    String? name,
    String? coverImage,
    String? id,
    String? modifiedDate,
    int? totalMembers,
    bool? isAdmin,
    bool? isMember,
    bool? isSendRequest,
    int? totalPendingPosts,
    int? totalPendingMembers,
    String? description,
    String? shareUrl,
    List<GroupUserEntity?>? members,
    bool? isFollow,
    List<GroupUserEntity?>? owners,
    String? groupChatId,
    GroupChatInfoEntity? groupChatInfo,
  }) {
    return GroupEntity(
      totalPosts: totalPosts ?? this.totalPosts,
      totalComments: totalComments ?? this.totalComments,
      totalLikes: totalLikes ?? this.totalLikes,
      totalShares: totalShares ?? this.totalShares,
      type: type ?? this.type,
      joinType: joinType ?? this.joinType,
      showType: showType ?? this.showType,
      name: name ?? this.name,
      coverImage: coverImage ?? this.coverImage,
      id: id ?? this.id,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      totalMembers: totalMembers ?? this.totalMembers,
      isAdmin: isAdmin ?? this.isAdmin,
      isMember: isMember ?? this.isMember,
      isSendRequest: isSendRequest ?? this.isSendRequest,
      totalPendingPosts: totalPendingPosts ?? this.totalPendingPosts,
      totalPendingMembers: totalPendingMembers ?? this.totalPendingMembers,
      description: description ?? this.description,
      shareUrl: shareUrl ?? this.shareUrl,
      members: members ?? this.members,
      isFollow: isFollow ?? this.isFollow,
      owners: owners ?? this.owners,
      groupChatId: groupChatId ?? this.groupChatId,
      groupChatInfo: groupChatInfo ?? this.groupChatInfo,
    );
  }

  GroupJoinType get getJoinType{
    if((this.isMember ?? false) || (this.isAdmin ?? false)) {
      return GroupJoinType.JOINED;
    }
    if(this.isSendRequest ?? false) {
      return GroupJoinType.REQUEST_JOINED;
    }
    return GroupJoinType.NOT_JOINED;
  }

  bool get isPublicGroup{
    return type == "PUBLIC";
  }

  factory GroupEntity.fromJson(Map<String, dynamic> json) => _$GroupEntityFromJson(json);

  Map<String, dynamic> toJson() => _$GroupEntityToJson(this);
}

@JsonSerializable()
class GroupChatInfoEntity {
  @JsonKey()
  String? id;
  @JsonKey()
  String? roomId;
  @JsonKey()
  String? nameConversation;
  @JsonKey()
  bool? isGroup;
  @JsonKey()
  String? name;

  GroupChatInfoEntity({
    this.id,
    this.roomId,
    this.nameConversation,
    this.isGroup,
    this.name,
  });

  factory GroupChatInfoEntity.fromJson(Map<String, dynamic> json) => _$GroupChatInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => _$GroupChatInfoEntityToJson(this);
}
