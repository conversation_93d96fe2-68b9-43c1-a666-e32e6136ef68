import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:real_care/models/entities/address_entity.dart';
import 'package:real_care/models/entities/erp_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/job_title_entity.dart';
import 'package:real_care/models/entities/position_entity.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/utils/date_utils.dart';

part 'user_entity.g.dart';

class Account {
  String? id;

  Account({
    this.id,
  });

  factory Account.fromJson(Map<String, dynamic> json) => Account(
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
  };
}

class City {
  String? name;

  City({
    this.name,
  });

  factory City.fromJson(Map<String, dynamic> json) => City(
    name: json["name"],
  );

  Map<String, dynamic> toJson() => {
    "name": name,
  };
}

class Address {
  String? street;
  String? ward;
  String? district;
  String? province;
  City? city;
  String? country;

  Address({
    this.street,
    this.ward,
    this.district,
    this.province,
    this.city,
    this.country,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
    street: json["street"],
    ward: json["ward"],
    district: json["district"],
    province: json["province"],
    city: json["city"] != null ? City.fromJson(json["city"]) : null,
    country: json["country"],
  );

  Map<String, dynamic> toJson() => {
    "street": street,
    "ward": ward,
    "district": district,
    "province": province,
    "city": city?.toJson(),
    "country": country,
  };
}

@JsonSerializable()
class UserEntity extends Equatable {
  @JsonKey()
  String? description;
  
  @JsonKey()
  bool? active;
  
  @JsonKey()
  DateTime? createdAt;

  @JsonKey()
  DateTime? updatedAt;
  
  @JsonKey()
  String? workingAt;
  
  @JsonKey()
  String? managerAt;

  @JsonKey()
  AddressEntity? interestedArea;

  @JsonKey()
  List<SocialUrlEntity>? socialUrls;

  @JsonKey()
  List<String>? staffIds;

  @JsonKey()
  ErpEntity? erp;

  @JsonKey()
  bool? isAdmin;

  @JsonKey()
  bool? isBql;

  @JsonKey()
  List<String>? roleCanAssign;

  @JsonKey()
  List<HistoryEntity>? historyUpgrade; // [Fix]: the response keep empty so can't not define (crmadmin) - Fix later!

  @JsonKey()
  String? id;

  @JsonKey()
  String? email;
  
  @JsonKey()
  String? phone;

  @JsonKey()
  PosEntity? pos;
  
  @JsonKey()
  String? code;

  @JsonKey()
  String? slug;

  @JsonKey()
  RoleEntity? role;

  @JsonKey()
  List<AttributeEntity>? attributes;

  @JsonKey()
  String? extPhone;

  @JsonKey()
  String? identityAddress;

  @JsonKey()
  String? identityCode;

  @JsonKey()
  String? identityCode2;

  @JsonKey()
  DateTime? identityDate;

  @JsonKey()
  ImageEntity? images;

  @JsonKey()
  String? name;

  @JsonKey()
  String? secondEmail;

  @JsonKey()
  String? status;
  
  @JsonKey()
  String? taxId;

  @JsonKey()
  JobTitleEntity? jobTitle;

  @JsonKey()
  PositionEntity? position;

  @JsonKey()
  Account? account;

  @JsonKey()
  Address? address;

  @JsonKey()
  DateTime? dob;

  @JsonKey()
  int? gender;

  @JsonKey()
  bool? isActive;

  // "level1": {
  //           "code": "1000",
  //           "nameVN": "Công ty 1000"
  //       },
  //       "level2": {
  //           "code": "HOAN2001",
  //           "nameVN": "Khối ********"
  //       },
  //       "level3": {
  //           "code": "HOAN3001",
  //           "nameVN": "Phòng ********"
  //       },
  //       "level4": {
  //           "code": "HOAN4001",
  //           "nameVN": "Bộ phận ********"
  //       },
  //       "level5": {
  //           "code": "HOAN5001",
  //           "nameVN": "Nhóm ********"
  //       },
  //       "level6": {
  //           "code": "1370",
  //           "nameVN": "Tổ đội ********"
  //       },
  //       "source": "IHRP",
  //       "identityCode2": "************",
  //       "isLinemanager": true,
  //       "orgCode": "HOANG QUÂN GROUP",
  //       "accounts": []

  UserEntity({
    this.description,
    this.active,
    this.createdAt,
    this.updatedAt,
    this.workingAt,
    this.managerAt,
    this.interestedArea,
    this.socialUrls,
    this.staffIds,
    this.erp,
    this.isAdmin,
    this.isBql,
    this.roleCanAssign,
    this.historyUpgrade,
    this.id,
    this.email,
    this.phone,
    this.pos,
    this.code,
    this.slug,
    this.role,
    this.attributes,
    this.extPhone,
    this.identityAddress,
    this.identityCode,
    this.identityCode2,
    this.identityDate,
    this.images,
    this.name,
    this.secondEmail,
    this.status,
    this.taxId,
    this.jobTitle,
    this.position,
    this.account,
    this.address,
    this.dob,
    this.gender,
    this.isActive,
  });

  GenderType? get genderType {
    return GenderTypeExtension.fromAPICode(gender);
  }
  
  // DateTime? get birthday {
  //   return DateTimeExtension.fromString(dob);
  // }

  UserEntity copyWith({
    String? description,
    bool? active,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? workingAt,
    String? managerAt,
    AddressEntity? interestedArea,
    List<SocialUrlEntity>? socialUrls,
    List<String>? staffIds,
    ErpEntity? erp,
    bool? isAdmin,
    bool? isBql,
    List<String>? roleCanAssign,
    List<HistoryEntity>? historyUpgrade,
    String? id,
    String? email,
    String? phone,
    PosEntity? pos,
    String? code,
    String? slug,
    RoleEntity? role,
    List<AttributeEntity>? attributes,
    String? extPhone,
    String? identityAddress,
    String? identityCode,
    String? identityCode2,
    DateTime? identityDate,
    ImageEntity? images,
    String? name,
    String? secondEmail,
    String? status,
    String? taxId,
    JobTitleEntity? jobTitle,
    PositionEntity? position,
    Account? account,
    Address? address,
    DateTime? dob,
    int? gender,
    bool? isActive,
  }) {
    return UserEntity(
      description: description ?? this.description,
      active: active ?? this.active,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      workingAt: workingAt ?? this.workingAt,
      managerAt: managerAt ?? this.managerAt,
      interestedArea: interestedArea ?? this.interestedArea,
      socialUrls: socialUrls ?? this.socialUrls,
      staffIds: staffIds ?? this.staffIds,
      erp: erp ?? this.erp,
      isAdmin: isAdmin ?? this.isAdmin,
      isBql: isBql ?? this.isBql,
      roleCanAssign: roleCanAssign ?? this.roleCanAssign,
      historyUpgrade: historyUpgrade ?? this.historyUpgrade,
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      pos: pos ?? this.pos,
      code: code ?? this.code,
      slug: slug ?? this.slug,
      role: role ?? this.role,
      attributes: attributes ?? this.attributes,
      extPhone: extPhone ?? this.extPhone,
      identityAddress: identityAddress ?? this.identityAddress,
      identityCode: identityCode ?? this.identityCode,
      identityCode2: identityCode2 ?? this.identityCode2,
      identityDate: identityDate ?? this.identityDate,
      images: images ?? this.images,
      name: name ?? this.name,
      secondEmail: secondEmail ?? this.secondEmail,
      status: status ?? this.status,
      taxId: taxId ?? this.taxId,
      jobTitle: jobTitle ?? this.jobTitle,
      position: position ?? this.position,
      account: account ?? this.account,
      address: address ?? this.address,
      dob: dob ?? this.dob,
      gender: gender ?? this.gender,
      isActive: isActive ?? this.isActive,
    );
  }

  factory UserEntity.fromJson(Map<String, dynamic> json) => _$UserEntityFromJson(json);
  Map<String, dynamic> toJson() => _$UserEntityToJson(this);

  @override
  List<Object?> get props => [
    description,
    active,
    createdAt,
    updatedAt,
    workingAt,
    managerAt,
    interestedArea,
    socialUrls,
    staffIds,
    erp,
    isAdmin,
    isBql,
    roleCanAssign,
    historyUpgrade,
    id,
    email,
    phone,
    pos,
    code,
    slug,
    role,
    attributes,
    extPhone,
    identityAddress,
    identityCode,
    identityCode2,
    identityDate,
    images,
    name,
    secondEmail,
    status,
    taxId,
    jobTitle,
    position,
    account,
    address,
    dob,
    gender,
    isActive,
  ];
}