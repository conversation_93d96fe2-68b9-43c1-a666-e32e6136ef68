import 'package:real_care/models/entities/check_in/health_declaration_entity.dart';
import 'package:real_care/models/entities/global_config.dart';
import 'package:real_care/models/entities/group/community_rules_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/intro_entity.dart';
import 'package:geolocator/geolocator.dart';

class GlobalData {
  GlobalData._privateConstructor();

  static final GlobalData instance = GlobalData._privateConstructor();

  TokenEntity? token;
  UserRoleEntity? userRole;
  List<BankEntity>? banks;
  List<IntroEntity>? intoItems;
  String? appVersion;
  GlobalConfig? config;
  HealthDeclarationEntity? lastCheckInData;
  CommunityRulesEntity? communityRules;
  Position? currentLocation;
}