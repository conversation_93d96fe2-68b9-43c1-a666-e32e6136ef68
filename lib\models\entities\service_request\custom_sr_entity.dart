import 'package:json_annotation/json_annotation.dart';
import 'custom_sr_rent_entity.dart';

part 'custom_sr_entity.g.dart';

@JsonSerializable()
class CustomSREntity {
  @JsonKey()
  CustomSRRentEntity? rentData;

  @JsonKey()
  String? contractId;

  CustomSREntity({
    this.rentData,
    this.contractId,
  });

  factory CustomSREntity.fromJson(Map<String, dynamic> json) => _$CustomSREntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$CustomSREntityToJson(this);
}
