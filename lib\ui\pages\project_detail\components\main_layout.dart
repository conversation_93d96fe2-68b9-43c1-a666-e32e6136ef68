import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_shadow.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/main.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/pages/home/<USER>';
import 'package:real_care/ui/pages/main/main_cubit.dart';
import 'package:real_care/ui/pages/main/main_page.dart';
import 'package:real_care/ui/pages/main/widgets/main_search_bar_widget.dart';
import 'package:real_care/ui/pages/notification/notification_home/notification_home.dart';
import 'package:real_care/ui/pages/profile/request_verify_account/request_verify_account_page.dart';
import 'package:real_care/ui/pages/property_management/property_management_navigation_page.dart';
import 'package:real_care/ui/pages/search/search_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MainLayout extends StatefulWidget {
  final Widget contentWidget;
  // final BuildContext context;
  MainLayout({
    required this.contentWidget,
    // required this.context,
  });

  @override
  _MainLayoutState createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  AppCubit? _appCubit;
  MainCubit? _mainCubit;
  PageController _pageController = PageController(initialPage: 0);
  final _homeNavKey = new GlobalKey<HomeNavigationPageState>();

  @override
  void initState() {
    super.initState();
    _mainCubit = BlocProvider.of<MainCubit>(context);
    _appCubit = BlocProvider.of<AppCubit>(context);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned(
              top: 0, left: 0, right: 0, child: _buildHeaderBackground()),
          SafeArea(
            bottom: false,
            child: Column(
              children: [
                _buildSearchBar(),
                SizedBox(
                  height: 10,
                ),
                Expanded(
                  child: widget.contentWidget,
                ),
              ],
            ),
          ),
          // _buildBottomTabBar()
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return SafeArea(
      child: Container(
        padding: EdgeInsets.only(left: 15, right: 15, top: 12),
        child: BlocBuilder<AppCubit, AppState>(
            bloc: _appCubit,
            buildWhen: (prev, current) {
              return prev.isHasNotification != current.isHasNotification ||
                  prev.isLoggedIn != current.isLoggedIn;
            },
            builder: (context, state) {
              return MainSearchBarWidget(
                onNotificationPressed: openNotificationListPage,
                onSearchPressed: openSearchPage,
                isLoggedIn: state.isLoggedIn,
              );
            }),
      ),
    );
  }

  Widget _buildHeaderBackground() {
    return Container(
      child: Image.asset(
        AppImages.bgHomeHeader,
        fit: BoxFit.fitWidth,
      ),
    );
  }

  void openNotificationListPage() async {
    if (_appCubit!.state.isLoggedIn) {
      var result = await Navigator.push(
        notifyNavigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => NotificationHomePage(),
        ),
      );
      if (result == null) {
        _appCubit!.getNotificationCountUnread();
      }
    } else {
      showSignIn();
    }
  }

  Widget _buildBottomTabBar() {
    return Stack(
      children: [
        Positioned(
          bottom: 0,
          right: 0,
          left: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10),
            height: 52,
            decoration: BoxDecoration(
              boxShadow: AppShadow.boxShadowBottomBar,
              color: Colors.white,
            ),
            child: BlocBuilder<MainCubit, MainState>(
              bloc: _mainCubit,
              buildWhen: (previous, current) =>
                  previous.currentTabIndex != current.currentTabIndex,
              builder: (context, state) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(0);
                        },
                        child: _item(
                          title: S.of(context).main_tabHome,
                          titleStyle: _mainCubit!.state.currentTabIndex == 0
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _mainCubit!.state.currentTabIndex == 0
                              ? AppImages.icHomeSelected
                              : AppImages.icHomeNormal,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(1);
                        },
                        child: _item(
                          title: S.current.main_properties,
                          titleStyle: _mainCubit!.state.currentTabIndex == 1
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _mainCubit!.state.currentTabIndex == 1
                              ? AppImages.icPropertySelected
                              : AppImages.icPropertyNormal,
                        ),
                      ),
                    ),
                    // Expanded(flex: 1, child: Container()),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(2);
                        },
                        child: _item(
                          title: S.current.main_help,
                          titleStyle: _mainCubit!.state.currentTabIndex == 2
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _mainCubit!.state.currentTabIndex == 2
                              ? AppImages.icHelpSelected
                              : AppImages.icHelpNormal,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(3);
                        },
                        child: _item(
                          title: S.current.main_account,
                          titleStyle: _mainCubit!.state.currentTabIndex == 3
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _mainCubit!.state.currentTabIndex == 3
                              ? AppImages.icProfileSelected
                              : AppImages.icProfileNormal,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _item(
      {required String title, TextStyle? titleStyle, required String image}) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(height: 18, width: 18, child: Image.asset(image)),
          Text(
            title,
            style: titleStyle,
          )
        ],
      ),
    );
  }

  void _changeIndexTab(int index) {
    if (_appCubit!.state.isLoggedIn || index == 0) {
      // [Fix]: Fix later
      // if (index == 1 &&
      //     _appCubit!.state.verifyAccountStatus !=
      //         VerifyAccountStatus.APPROVED) {
      //   _requestVerifyAccount();
      //   return;
      // }

      if (_mainCubit!.state.currentTabIndex != index) {
        _mainCubit!.changeTab(index);
      } else {
        _handleTabOnTabAgain(index);
      }
    } else {
      showSignIn();
    }
  }

  void _handleTabOnTabAgain(int index) {
    if (_pageController.page == MainTabType.home.index &&
        homeNavigatorKey.currentState!.canPop()) {
      homeNavigatorKey.currentState!.pop();
    } else {
      _homeNavKey.currentState!.scrollToTop();
    }
    if (_pageController.page == MainTabType.property.index &&
        propertyNavigatorKey.currentState!.canPop()) {
      propertyNavigatorKey.currentState!.pop();
    }
  }

  void _requestVerifyAccount() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RequestVerifyAccountPage(),
      ),
    );
    _appCubit!.getProfile();
  }

  ///Navigate search Page
  void openSearchPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchPage(),
      ),
    );
  }

  ///Navigate
  void showSignIn() async {
    Application.router!
        .navigateTo(appNavigatorKey.currentContext!, Routes.signIn);
  }
}
