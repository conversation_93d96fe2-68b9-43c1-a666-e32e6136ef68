import 'package:json_annotation/json_annotation.dart';

part 'user_role_entity.g.dart';

@JsonSerializable()
class UserRoleEntity {
  @Json<PERSON>ey()
  String? id;
  @Json<PERSON>ey()
  List<String>? permissions;
  @Json<PERSON>ey()
  bool? isFirstLogin;
  @Json<PERSON>ey()
  String? description;
  @Json<PERSON>ey()
  bool? active;
  @Json<PERSON>ey()
  bool? softDelete;
  @Json<PERSON>ey()
  dynamic modifiedBy;
  @<PERSON>son<PERSON><PERSON>()
  dynamic registerStatus;
  @Json<PERSON>ey()
  bool? authOtp;
  @Json<PERSON>ey()
  String? name;
  @JsonKey()
  String? email;
  @Json<PERSON>ey()
  String? status;
  @Json<PERSON>ey()
  String? phone;
  @JsonKey()
  String? roleId;
  @Json<PERSON>ey()
  dynamic workingAt;
  @JsonKey()
  dynamic managerAt;
  @JsonKey()
  String? welcomeId;
  @Json<PERSON>ey()
  String? codeDx;
  @JsonKey()
  int? v;
  @J<PERSON><PERSON><PERSON>()
  List<dynamic>? role;
  @J<PERSON><PERSON>ey()
  String? otp;

  // bool isContainPermission(String permission) {
  //   if (permissions.isEmpty || permissions == null) {
  //     return false;
  //   }
  //   try {
  //     final perItem = permissions.firstWhere((element) {
  //       return element.featureName == permission;
  //     });
  //     return perItem.action.readOwn == true;
  //   } catch (e) {
  //     return false;
  //   }
  // }

  UserRoleEntity({
    this.id,
    this.permissions,
    this.isFirstLogin,
    this.description,
    this.active,
    this.softDelete,
    this.modifiedBy,
    this.registerStatus,
    this.authOtp,
    this.name,
    this.email,
    this.status,
    this.phone,
    this.roleId,
    this.workingAt,
    this.managerAt,
    this.welcomeId,
    this.codeDx,
    this.v,
    this.role,
    this.otp,
  });

  factory UserRoleEntity.fromJson(Map<String, dynamic> json) => _$UserRoleEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserRoleEntityToJson(this);
}
