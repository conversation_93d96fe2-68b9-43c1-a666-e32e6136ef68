import 'package:json_annotation/json_annotation.dart';

part 'team_entity.g.dart';

@JsonSerializable()
class TeamEntity {
  @Json<PERSON>ey(name: "_id")
  String? id;
  @Json<PERSON>ey()
  String? name;
  @J<PERSON><PERSON>ey()
  int? type;
  @<PERSON><PERSON><PERSON>ey()
  String? createdAt;
  @J<PERSON><PERSON>ey()
  CreatedBy? createdBy;
  @J<PERSON><PERSON><PERSON>(name: "_updateAt")
  String? updateAt;
  @JsonKey()
  String? roomId;

  TeamEntity({
    this.createdBy,
    this.id,
    this.name,
    this.type,
    this.createdAt,
    this.roomId,
    this.updateAt,
  });

  factory TeamEntity.fromJson(Map<String, dynamic> json) => _$TeamEntityFromJson(json);

  Map<String, dynamic> toJson() => _$TeamEntityToJson(this);
}

@JsonSerializable()
class CreatedBy {
  @JsonKey(name: "_id")
  String? id;
  @Json<PERSON>ey()
  String? username;

  CreatedBy({
    this.id,
    this.username,
  });

  factory CreatedBy.fromJson(Map<String, dynamic> json) => _$CreatedByFromJson(json);

  Map<String, dynamic> toJson() => _$CreatedByToJson(this);
}
