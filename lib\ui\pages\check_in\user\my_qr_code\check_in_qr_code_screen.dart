import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/global/global_data.dart';
import 'package:real_care/models/entities/check_in/check_in_info.dart';
import 'package:real_care/models/entities/check_in/check_in_of_building.dart';
import 'package:real_care/models/entities/check_in/list_check_in_history_by_date.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/check_in_repository.dart';
import 'package:real_care/ui/components/app_page_widget.dart';
import 'package:real_care/ui/pages/check_in/user/my_qr_code/check_in_qr_code_cubit.dart';
import 'package:real_care/ui/pages/check_in/widgets/qr_code_info.dart';
import 'package:real_care/ui/widgets/loading_check_in_history_widget.dart';
import 'package:real_care/ui/widgets/loading_more_row_widget.dart';
import 'package:real_care/utils/date_utils.dart' as date;
import 'package:flutter_bloc/flutter_bloc.dart';

class CheckInQRCodeScreen extends StatefulWidget {
  const CheckInQRCodeScreen({super.key});

  @override
  State<CheckInQRCodeScreen> createState() => _CheckInQRCodeScreenState();
}

class _CheckInQRCodeScreenState extends State<CheckInQRCodeScreen> {
  CheckInQrCodeCubit? _cubit;
  late AppCubit _appCubit;
  final ScrollController _controller = ScrollController();

  @override
  void initState() {
    super.initState();
    _appCubit = BlocProvider.of<AppCubit>(context);
    final repo = RepositoryProvider.of<CheckInRepository>(context);
    _cubit = CheckInQrCodeCubit(repository: repo);
    _cubit!.getCheckInHistory();
    _controller.addListener(() {
      onScrollListener();
    });
  }

  void onScrollListener() {
    double maxScroll = _controller.position.maxScrollExtent;
    double currentScroll = _controller.position.pixels;
    if (maxScroll - currentScroll < 50 &&
        _cubit!.state.getCheckInHistoryStatus != LoadStatus.LOADING &&
        currentScroll > 50) {
      _cubit!.loadMoreCheckInHistory();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          AppPageWidget(
            title: S.of(context).checkin_my_qrcode,
            visibleFooter: false,
            backgroundColor: AppColors.greyE6E6E6,
            child: _buildBodyWidget(),
          ),
          BlocBuilder<CheckInQrCodeCubit, CheckInQrCodeState>(
            bloc: _cubit,
            buildWhen: (pre, current) =>
                pre.getCheckInHistoryStatus != current.getCheckInHistoryStatus,
            builder: (context, state) {
              return Container(
                margin: EdgeInsets.only(bottom: 15),
                alignment: Alignment.bottomCenter,
                child: Visibility(
                  visible:
                      state.getCheckInHistoryStatus == LoadStatus.LOADING_MORE,
                  child: LoadingMoreRowWidget(),
                ),
              );
            },
          )
        ],
      ),
    );
  }

  Widget _buildBodyWidget() {
    return RefreshIndicator(
      onRefresh: () async {
        _cubit!.getCheckInHistory();
      },
      child: SingleChildScrollView(
        controller: _controller,
        physics: AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            BlocBuilder<CheckInQrCodeCubit, CheckInQrCodeState>(
              bloc: _cubit,
              buildWhen: (pre, current) =>
                  pre.isExpired != current.isExpired ||
                  pre.statusRequireHealthDeclaration !=
                      current.statusRequireHealthDeclaration,
              builder: (context, state) {
                return Container(
                  padding: EdgeInsets.only(top: 30, bottom: 22),
                  color: AppColors.background,
                  width: double.infinity,
                  child: QRCodeInfo(
                    expiredTime: DateTime.now().add(Duration(
                        minutes:
                            GlobalData.instance.config?.qrCodeDuration ?? 30)),
                    data: base64.encode(utf8.encode(json.encode({
                      "timestamp":
                          DateTime.now().toUtc().millisecondsSinceEpoch,
                      "userId": _appCubit.state.user?.id ?? ''
                    }))),
                    name: _appCubit.state.user?.name ?? '',
                    // team: _appCubit.state.user?.team ?? '',
                    // division: _appCubit.state.user?.division ?? '',
                    // company: _appCubit.state.user?.company ?? '', // [Fix]: Fix later
                    isLoading: state.statusRequireHealthDeclaration ==
                        LoadStatus.LOADING,
                    onFinish: () {
                      _cubit!.onFinish();
                    },
                    isValid: state.isRequireHealthDeclaration,
                    healthDeclaration: state.healthDeclaration ?? false,
                  ),
                );
              },
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(top: 19, bottom: 12, left: 14),
              child: Text('Lịch sử quét mã',
                  style: AppTextStyle.blackS16Bold, textAlign: TextAlign.left),
            ),
            BlocBuilder<CheckInQrCodeCubit, CheckInQrCodeState>(
              bloc: _cubit,
              buildWhen: (prev, current) =>
                  prev.getCheckInHistoryStatus !=
                  current.getCheckInHistoryStatus,
              builder: (context, state) {
                if (state.getCheckInHistoryStatus == LoadStatus.LOADING) {
                  return SizedBox(
                      height: 500, child: LoadingCheckInHistoryWidget());
                } else if (state.getCheckInHistoryStatus ==
                    LoadStatus.FAILURE) {
                  return _buildEmptyOrErrorWidget('Đã xảy ra lỗi');
                } else {
                  if (state.listCheckInHistoryByDate == null ||
                      state.listCheckInHistoryByDate!.isEmpty) {
                    return _buildEmptyOrErrorWidget('Chưa có lịch sử quét mã');
                  } else {
                    List<Widget> listCheckInHistory = [];

                    listCheckInHistory.addAll(state.listCheckInHistoryByDate!
                        .asMap()
                        .map((index, e) {
                          return MapEntry(
                            index,
                            _itemCheckInHistory(
                              checkInHistoryByDate:
                                  state.listCheckInHistoryByDate![index],
                              isFirstItem: index == 0,
                            ),
                          );
                        })
                        .values
                        .toList());
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 14),
                      child: Column(children: listCheckInHistory),
                    );
                  }
                }
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyOrErrorWidget(String content) {
    return SizedBox(
      height: 200,
      child: Center(
        child: Text(
          content,
          style: AppTextStyle.blackS14Regular,
        ),
      ),
    );
  }

  Widget _itemCheckInHistory(
      {required CheckInHistoryByDate checkInHistoryByDate,
      bool isFirstItem = false}) {
    List<Widget> listCheckIn = [];

    listCheckIn.add(Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Text(
        '${_cubit!.state.titleDate(checkInHistoryByDate.date)}${date.DateUtils.fromString(checkInHistoryByDate.date ?? '')!.toDateString(format: 'dd/MM/yyyy')}',
        style: AppTextStyle.blackS12.copyWith(color: AppColors.grayIntro),
      ),
    ));

    listCheckIn.addAll((checkInHistoryByDate.listCheckInOfBuilding ?? [])
        .asMap()
        .map((index, e) {
          return MapEntry(
            index,
            _itemCheckIn(
              checkInOfBuilding:
                  checkInHistoryByDate.listCheckInOfBuilding![index],
              isFirstItem: index == 0,
            ),
          );
        })
        .values
        .toList());

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 17),
      margin: EdgeInsets.only(top: isFirstItem ? 0 : 8),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.all(
          Radius.circular(5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: listCheckIn,
      ),
    );
  }

  Widget _itemCheckIn(
      {required CheckInOfBuilding checkInOfBuilding,
      bool isFirstItem = false}) {
    List<Widget> listCheckInItem = [];
    listCheckInItem.addAll((checkInOfBuilding.listCheckInInfo ?? [])
        .asMap()
        .map((index, e) {
          return MapEntry(
              index, _checkInTime(checkInOfBuilding.listCheckInInfo![index]));
        })
        .values
        .toList());
    String buildingAddress = '';
    if (checkInOfBuilding.listCheckInInfo != null &&
        checkInOfBuilding.listCheckInInfo!.isNotEmpty) {
      buildingAddress =
          checkInOfBuilding.listCheckInInfo!.first.buildingInfo?.address ?? '';
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: !isFirstItem,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Divider(
              color: AppColors.greyE6E6E6,
              thickness: 1,
              height: 1,
            ),
          ),
        ),
        Text(checkInOfBuilding.buildingName ?? '',
            style: AppTextStyle.blackS14Bold),
        Visibility(
          visible: buildingAddress.isNotEmpty,
          child: Padding(
            padding: const EdgeInsets.only(top: 5.0, bottom: 10),
            child: Text(buildingAddress, style: AppTextStyle.blackS14Regular),
          ),
        ),
        Column(children: listCheckInItem)
      ],
    );
  }

  Widget _checkInTime(CheckInInfo checkInInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Row(
          children: [
            Image.asset(
              (checkInInfo.isValid ?? false)
                  ? AppImages.icUserCheckInSuccess
                  : AppImages.icUserCheckInFail,
              width: 15,
              height: 15,
            ),
            SizedBox(width: 5),
            Text('Check-in vào',
                style: AppTextStyle.blackS12Regular
                    .copyWith(color: AppColors.grayIntro)),
            SizedBox(width: 10),
            Text(
              checkInInfo.checkInDate?.toDateString(format: 'HH:mm') ?? '',
              style: AppTextStyle.tintS12Regular,
            ),
          ],
        ),
        Visibility(
          visible: !(checkInInfo.isValid ?? false),
          child: Padding(
            padding: const EdgeInsets.only(left: 20.0, top: 4),
            child: Text('Khai báo không hợp lệ', style: AppTextStyle.redS10),
          ),
        ),
      ],
    );
  }
}