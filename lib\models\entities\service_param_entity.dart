import 'package:json_annotation/json_annotation.dart';

part 'service_param_entity.g.dart';

@JsonSerializable()
class ServiceParamEntity {
  @JsonKey()
  List<dynamic>? images;

  @JsonKey()
  String? contractId; //Mã hợp đồng

  @JsonKey()
  String? unitCode; //Mã sản phẩm

  @JsonKey(fromJson: intSafeToString)
  String? price; //Gi<PERSON> sản phẩm

  @JsonKey()
  String? transferPrice; //<PERSON>i<PERSON> chuyển nhượng

  @JsonKey()
  String? commission; //Hoa hồng

  @JsonKey()
  String? code; //Mã hợp đồng

  @JsonKey()
  String? address;

  @Json<PERSON>ey()
  ProjectDataParam? projectData;

  ServiceParamEntity({
    this.images,
    this.contractId,
    this.unitCode,
    this.price,
    this.transferPrice,
    this.commission,
    this.code,
    this.address,
    this.projectData,
  });

  ServiceParamEntity copyWith({
    List<dynamic>? images,
    String? contractId,
    String? unitCode,
    String? price,
    String? transferPrice,
    String? commission,
    String? code,
    String? address,
    ProjectDataParam? projectData,
  }) {
    return new ServiceParamEntity(
      images: images ?? this.images,
      contractId: contractId ?? this.contractId,
      unitCode: unitCode ?? this.unitCode,
      price: price ?? this.price,
      transferPrice: transferPrice ?? this.transferPrice,
      commission: commission ?? this.commission,
      code: code ?? this.code,
      address: address ?? this.address,
      projectData: projectData ?? this.projectData,
    );
  }

  static String intSafeToString(dynamic jsonVal) {
    return jsonVal?.toString() ?? "";
  }

  factory ServiceParamEntity.fromJson(Map<String, dynamic> json) =>
      _$ServiceParamEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$ServiceParamEntityToJson(this);
}

@JsonSerializable()
class ForLeadCareParam {
  @JsonKey()
  List<dynamic>? images;

  @JsonKey()
  String? contractId; //Mã hợp đồng

  @JsonKey()
  String? unitCode; //Mã sản phẩm

  @JsonKey(fromJson: intSafeToString)
  String? price; //Giá sản phẩm

  @JsonKey()
  String? transferPrice; //Giá chuyển nhượng

  @JsonKey()
  String? commission; //Hoa hồng

  @JsonKey()
  String? code; //Mã hợp đồng

  @JsonKey()
  String? address;

  static String intSafeToString(dynamic jsonVal) {
    return jsonVal?.toString() ?? "";
  }

  ForLeadCareParam({
    this.images,
    this.contractId,
    this.unitCode,
    this.price,
    this.transferPrice,
    this.commission,
    this.code,
    this.address,
  });

  ForLeadCareParam copyWith({
    List<dynamic>? images,
    String? contractId,
    String? unitCode,
    String? price,
    String? transferPrice,
    String? commission,
    String? code,
    String? address,
  }) {
    return new ForLeadCareParam(
      images: images ?? this.images,
      contractId: contractId ?? this.contractId,
      unitCode: unitCode ?? this.unitCode,
      price: price ?? this.price,
      transferPrice: transferPrice ?? this.transferPrice,
      commission: commission ?? this.commission,
      code: code ?? this.code,
      address: address ?? this.address,
    );
  }

  factory ForLeadCareParam.fromJson(Map<String, dynamic> json) =>
      _$ForLeadCareParamFromJson(json);
  Map<String, dynamic> toJson() => _$ForLeadCareParamToJson(this);
}

@JsonSerializable()
class ProjectDataParam {
  @JsonKey()
  String? requestType; //loại yêu cầu

  @JsonKey()
  String? name; //tên câu hỏi

  @JsonKey()
  String? value; //nội dung câu hỏi

  @JsonKey()
  String? type; //text

  @JsonKey()
  List<dynamic>? images;

  @JsonKey()
  List<dynamic>? files;

  @JsonKey()
  String? customerCode;

  static String intSafeToString(dynamic jsonVal) {
    return jsonVal?.toString() ?? "";
  }

  ProjectDataParam({
    this.images,
    this.requestType,
    this.name,
    this.value,
    this.type,
    this.files,
    this.customerCode,
  });

  ProjectDataParam copyWith({
    List<dynamic>? images,
    List<dynamic>? files,
    String? requestType,
    String? name,
    String? value,
    String? type,
    String? customerCode,
  }) {
    return new ProjectDataParam(
      images: images ?? this.images,
      files: files ?? this.files,
      requestType: requestType ?? this.requestType,
      name: name ?? this.name,
      value: value ?? this.value,
      type: type ?? this.type,
      customerCode: customerCode ?? this.customerCode,
    );
  }

  factory ProjectDataParam.fromJson(Map<String, dynamic> json) =>
      _$ProjectDataParamFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectDataParamToJson(this);
}
