import 'package:json_annotation/json_annotation.dart';

part 'sum_status_entity.g.dart';

@JsonSerializable()
class SumStatusEntity {
  @JsonKey(name: "_id")
  String? id;
  @JsonKey()
  int? count;

  SumStatusEntity({this.id, this.count});

  factory SumStatusEntity.fromJson(Map<String, dynamic> json) => _$SumStatusEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$SumStatusEntityToJson(this);
}
