import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'response_entity.g.dart';

@JsonSerializable()
class ResponseEntity {
  @JsonKey()
  String? id;

  ResponseEntity({
    this.id,
  });

  factory ResponseEntity.fromJson(Map<String, dynamic> json) =>
      _$ResponseEntityFromJson(json);
  Map<String, dynamic> toJson() => _$ResponseEntityToJson(this);
}
