import 'package:real_care/models/enums/check_in_type_ui.dart';
import 'package:json_annotation/json_annotation.dart';
//
part 'health_declaration_template_entity.g.dart';

@JsonSerializable()
class HealthDeclarationTemplateEntity {
  @J<PERSON><PERSON>ey()
  String? description;
  @Json<PERSON>ey()
  String? index;
  @Json<PERSON>ey()
  bool? isRequired;
  @J<PERSON><PERSON><PERSON>()
  bool? isSingle;
  @Json<PERSON>ey()
  List<HealthDeclarationInfoEntity>? list;
  @JsonKey()
  bool? isCheck;
  @Json<PERSON>ey()
  String? value;
  @JsonKey()
  String? typeUi;
  @J<PERSON><PERSON>ey()
  List<SubInforEntity>? subInfor;

  HealthDeclarationTemplateEntity({
    this.description,
    this.index,
    this.isRequired,
    this.isSingle,
    this.list,
    this.isCheck,
    this.value,
    this.typeUi,
    this.subInfor,
  });

  factory HealthDeclarationTemplateEntity.fromJson(Map<String, dynamic> json) => _$HealthDeclarationTemplateEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$HealthDeclarationTemplateEntityToJson(this);

  CheckInTypeUI? get getUIType {
    return CheckInTypeUIExtension.getTypeUI(typeUi);
  }

  HealthDeclarationTemplateEntity copyWith({
    String? description,
    String? index,
    bool? isRequired,
    bool? isSingle,
    List<HealthDeclarationInfoEntity>? list,
    bool? isCheck,
    String? value,
    String? typeUi,
    List<SubInforEntity>? subInfor,
  }) {
    return HealthDeclarationTemplateEntity(
      description: description ?? this.description,
      index: index ?? this.index,
      isRequired: isRequired ?? this.isRequired,
      isSingle: isSingle ?? this.isSingle,
      list: list ?? this.list,
      isCheck: isCheck ?? this.isCheck,
      value: value ?? this.value,
      typeUi: typeUi ?? this.typeUi,
      subInfor: subInfor ?? this.subInfor,
    );
  }
}

@JsonSerializable()
class HealthDeclarationInfoEntity {
  @JsonKey()
  String? index;
  @JsonKey()
  String? description;
  @JsonKey()
  String? value;
  @JsonKey()
  bool? isCheck;
  @JsonKey()
  bool? isRequired;
  @JsonKey()
  String? typeUi;
  @JsonKey()
  List<SubInforEntity>? subInfor;
  @JsonKey()
  SubResultEntity? subResult;
  @JsonKey()
  bool? isLine;

  HealthDeclarationInfoEntity(
      {this.index,
      this.description,
      this.isCheck,
      this.value,
      this.isRequired,
      this.typeUi,
      this.subInfor,
      this.subResult,
      this.isLine});

  factory HealthDeclarationInfoEntity.fromJson(Map<String, dynamic> json) => _$HealthDeclarationInfoEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$HealthDeclarationInfoEntityToJson(this);

  HealthDeclarationInfoEntity copyWith({
    String? index,
    String? description,
    String? value,
    bool? isCheck,
    bool? isRequired,
    String? typeUi,
    List<SubInforEntity>? subInfor,
    SubResultEntity? subResult,
    bool? isLine,
  }) {
    return HealthDeclarationInfoEntity(
      index: index ?? this.index,
      description: description ?? this.description,
      value: value ?? this.value,
      isCheck: isCheck ?? this.isCheck,
      isRequired: isRequired ?? this.isRequired,
      typeUi: typeUi ?? this.typeUi,
      subInfor: subInfor ?? this.subInfor,
      subResult: subResult ?? this.subResult,
      isLine: isLine ?? this.isLine,
    );
  }

  CheckInTypeUI? get getUIType {
    return CheckInTypeUIExtension.getTypeUI(typeUi);
  }
}

@JsonSerializable()
class SubInforEntity {
  @JsonKey()
  String? index;
  @JsonKey()
  String? title;
  @JsonKey()
  bool? isRequired;
  @JsonKey()
  String? value;
  @JsonKey()
  bool? isCheck;
  @JsonKey()
  String? typeUi;

  SubInforEntity({
    this.index,
    this.title,
    this.isRequired,
    this.value,
    this.isCheck,
    this.typeUi,
  });

  factory SubInforEntity.fromJson(Map<String, dynamic> json) => _$SubInforEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$SubInforEntityToJson(this);

  SubInforEntity copyWith({
    String? index,
    String? title,
    bool? isRequired,
    String? value,
    bool? isCheck,
    String? typeUi,
  }) {
    return SubInforEntity(
      index: index ?? this.index,
      title: title ?? this.title,
      isRequired: isRequired ?? this.isRequired,
      value: value ?? this.value,
      isCheck: isCheck ?? this.isCheck,
      typeUi: typeUi ?? this.typeUi,
    );
  }

  CheckInTypeUI? get getUIType {
    return CheckInTypeUIExtension.getTypeUI(typeUi);
  }
}

@JsonSerializable()
class SubResultEntity {
  @JsonKey()
  String? title;
  @JsonKey()
  bool? isCheck;
  @JsonKey()
  String? titleCheckTrue;
  @JsonKey()
  String? titleCheckFalse;
  @JsonKey()
  String? typeUi;

  SubResultEntity(
      {this.title,
      this.isCheck,
      this.titleCheckTrue,
      this.titleCheckFalse,
      this.typeUi});

  factory SubResultEntity.fromJson(Map<String, dynamic> json) => _$SubResultEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$SubResultEntityToJson(this);

  SubResultEntity copyWith({
    String? title,
    bool? isCheck,
    String? titleCheckTrue,
    String? titleCheckFalse,
    String? typeUi,
  }) {
    return SubResultEntity(
      title: title ?? this.title,
      isCheck: isCheck ?? this.isCheck,
      titleCheckTrue: titleCheckTrue ?? this.titleCheckTrue,
      titleCheckFalse: titleCheckFalse ?? this.titleCheckFalse,
      typeUi: typeUi ?? this.typeUi,
    );
  }

  CheckInTypeUI? get getUIType {
    return CheckInTypeUIExtension.getTypeUI(typeUi);
  }
}
