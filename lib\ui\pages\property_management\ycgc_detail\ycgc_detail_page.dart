import 'package:flutter/material.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_shadow.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/entities/YCGC_entity.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/models/enums/ycgc_status.dart';
import 'package:real_care/ui/components/dashview/dash_painter.dart';
import 'package:real_care/ui/widgets/separator_widget.dart';
import 'package:real_care/utils/date_utils.dart' as date;
import 'package:real_care/utils/utils.dart';

class YCGCDetailPage extends StatelessWidget {
  final YCGCEntity ycgc;

  YCGCDetailPage({required this.ycgc});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              margin: EdgeInsets.symmetric(horizontal: 15, vertical: 25),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(20)),
                color: Colors.white,
              ),
              child: Column(
                children: [
                  SizedBox(height: 20),
                  Text(S.of(context).ycgcInfo_title,
                      style: AppTextStyle.blackS18Bold),
                  SizedBox(height: 10),
                  Text(ycgc.bookingTicketCode ?? "-",
                      style: AppTextStyle.tintS14),
                  SizedBox(height: 10),
                  Container(
                    height: 20,
                    width: 86,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: ycgc.getYCGCStatus.borderColor, width: 1),
                      borderRadius: BorderRadius.circular(10),
                      color: ycgc.getYCGCStatus.backgroundColor,
                    ),
                    child: Text(
                      ycgc.getYCGCStatus.text ?? "-",
                      style: AppTextStyle.tintS11
                          .copyWith(color: ycgc.getYCGCStatus.textColor),
                    ),
                  ),
                  SizedBox(height: 10),
                  Container(
                    height: 2,
                    width: double.infinity,
                    child: ClipRect(
                      child: CustomPaint(
                        painter: DashPainter(
                            color: Color(0xFFCBCBCB),
                            dashSpace: 4,
                            dashWidth: 4,
                            strokeWidth: 1.5),
                      ),
                    ),
                  ),
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 18),
                      child: SingleChildScrollView(
                        physics: ClampingScrollPhysics(),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_fullName + ":",
                                information: _getText(
                                    ycgc.customer?.personalInfo?.name)),
                            SizedBox(height: 12),
                            // _informationWidget(
                            //     title: S.of(context).common_gender + ":",
                            //     information: _getText(ycgc.customer?.info
                            //         ?.getGenderType?.displayText)), // [Fix]: Fix later
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_dateOfBirth + ":",
                                information: _getTextBirthDay(
                                    fullDate: ycgc.customer?.info?.birthday,
                                    year: ycgc.customer?.info?.birthdayYear)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_phone + ":",
                                information: _getText(
                                    ycgc.customer?.personalInfo?.phone)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_email + ":",
                                information: _getText(
                                    ycgc.customer?.personalInfo?.email)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).ycgcDetail_identityNumber +
                                    ":",
                                information: _getText(ycgc.customer
                                    ?.personalInfo?.identities![0].value)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_identityDate + ":",
                                information: _getText(date.DateUtils.fromString(
                                        ycgc.customer?.personalInfo
                                            ?.identities![0].date,
                                        format: AppConfig.dateDisplayFormat)!
                                    .toDateString())),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_identityPlace + ":",
                                information: _getText(ycgc.customer
                                    ?.personalInfo?.identities![0].place)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title:
                                    S.of(context).ycgcDetail_permanentAddress +
                                        ":",
                                information: _getText(ycgc
                                    .customer?.info?.rootAddress?.fullAddress)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).ycgcDetail_address + ":",
                                information: _getText(
                                    ycgc.customer?.info?.address?.fullAddress)),
                            SizedBox(height: 12),
                            SeparatorWidget(),
                            SizedBox(height: 12),
                            _informationWidget(
                                title:
                                    S.of(context).ycgcDetail_propertyType + ":",
                                information: _getText(ycgc.project?.type)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S.of(context).common_project + ":",
                                information: _getText(ycgc.project?.name)),
                            SizedBox(height: 12),
                            _informationWidget(
                                title: S
                                        .of(context)
                                        .ycgcDetail_registrationAmount +
                                    ":",
                                information: Utils.moneyToString(
                                    ycgc.amountRegistration,
                                    unit: "VNĐ")),
                            SizedBox(height: 12),
                            SeparatorWidget(),
                            SizedBox(height: 12),
                            _informationListWidget(
                                title:
                                    S.of(context).ycgcDetail_productCode + ":",
                                listInformation: (ycgc.unitCode != null &&
                                        ycgc.unitCode!.isNotEmpty)
                                    ? ["${ycgc.unitCode}"]
                                    : []),
                            // SizedBox(height: 12),
                            // _informationWidget(title: "Nhu cầu vay NH:", information: "Không"), //todo
                            SizedBox(height: 12),
                            // _informationWidget(title: S.of(context).ycgcDetail_note + ":", information: _getText(ycgc?.note)),
                            // SizedBox(height: 12),
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Positioned(
                right: 0,
                top: 14,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    height: 26,
                    width: 26,
                    margin: EdgeInsets.all(3),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: AppShadow.boxShadow,
                    ),
                    child: Image.asset(AppImages.icCloseOnlyText),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _informationWidget(
      {required String title, required String information}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 150,
          child: Text(title, style: AppTextStyle.blackS14Bold),
        ),
        Expanded(
          child: Text(
            (information ?? "-").trim(),
            style: AppTextStyle.blackS14.copyWith(height: 1.2),
          ),
        ),
      ],
    );
  }

  Widget _informationListWidget(
      {required String title, required List<String> listInformation}) {
    List<Widget> listWidgets = [];
    listInformation.forEach((element) {
      listWidgets.add(Container(
        padding: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
        decoration: BoxDecoration(
            color: Color(0xFFD5E4FF), borderRadius: BorderRadius.circular(5)),
        child: Text(element, style: AppTextStyle.blackS14),
      ));
    });

    return Row(
      children: [
        Container(
          width: 150,
          child: Text(title, style: AppTextStyle.blackS14Bold),
        ),
        Expanded(
          child: Wrap(
            spacing: 5,
            children: listWidgets,
          ),
        ),
      ],
    );
  }

  String _getText(String? text) {
    if (text != null && text.isNotEmpty) {
      return text;
    } else {
      return "-";
    }
  }

  String _getTextBirthDay({String? fullDate, String? year}) {
    if (fullDate != null && fullDate.isNotEmpty) {
      return date.DateUtils.fromString(ycgc.customer?.info?.birthday,
              format: AppConfig.dateDisplayFormat)!
          .toDateString();
    } else if (year != null && year.isNotEmpty) {
      return year;
    } else {
      return "-";
    }
  }
}
