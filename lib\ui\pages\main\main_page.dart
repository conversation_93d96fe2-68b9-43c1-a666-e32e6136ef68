import 'dart:async';

import 'package:another_flushbar/flushbar.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_shadow.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/global/global_data.dart';
import 'package:real_care/global/global_event.dart';
import 'package:real_care/main.dart';
import 'package:real_care/models/entities/notification/push_notification_entity.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/dialogs/app_dialog_sign_out.dart';
import 'package:real_care/ui/pages/community/community_intro_page.dart';
import 'package:real_care/ui/pages/home/<USER>';
import 'package:real_care/ui/pages/main/main_cubit.dart';
import 'package:real_care/ui/pages/main/widgets/main_search_bar_widget.dart';
import 'package:real_care/ui/pages/notification/notification_home/notification_home.dart';
import 'package:real_care/ui/pages/profile/profile_navigation_page.dart';
import 'package:real_care/ui/pages/profile/request_verify_account/request_verify_account_page.dart';
import 'package:real_care/ui/pages/property_management/property_management_navigation_page.dart';
import 'package:real_care/ui/pages/search/search_page.dart';
import 'package:real_care/utils/dialog_utils.dart';
import 'package:real_care/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:upgrader/upgrader.dart';

import 'widgets/main_background_widget.dart';

enum MainTabType {
  home,
  property,
  utilities,
  supportCenter,
  profile,
}

extension MainTabTypeExtension on MainTabType {
  int get index {
    switch (this) {
      case MainTabType.home:
        return 0;
      case MainTabType.property:
        return 1;
      case MainTabType.utilities:
        return 2;
      case MainTabType.supportCenter:
        return 3;
      case MainTabType.profile:
        return 4;
      }
  }
}

final notifyNavigatorKey = GlobalKey<NavigatorState>();
final bottomBarNavigatorKey = GlobalKey<NavigatorState>();

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MainPageState();
  }
}

class _MainPageState extends State<MainPage>
    with SingleTickerProviderStateMixin {
  MainCubit? _cubit;
  AppCubit? _appCubit;
  final PageController _pageController = PageController(initialPage: 0);
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final _homeNavKey = GlobalKey<HomeNavigationPageState>();
  late StreamSubscription _tokenExpiredSubscription;
  bool isHasNotification = false;

  late List<Widget> pages;

  @override
  void initState() {
    _cubit = BlocProvider.of<MainCubit>(context);
    _appCubit = BlocProvider.of<AppCubit>(context);

    pages = [
      HomeNavigationPage(key: _homeNavKey),
      PropertyManagementNavigationPage(),
      // HelpPage(),
      Container(),
      ProfileNavigationPage(),
    ];
    //Build page
    super.initState();

    _setupTokenExpired();

    //Setup Firebase message
    _setupFireBaseMessage();

    if (_appCubit?.state.isLoggedIn ?? false) {
      _appCubit!.getNotificationCountUnread();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _tokenExpiredSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (!didPop) {
          if (await _onWillPop()) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        key: _scaffoldKey,
        body: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(bottom: 50),
              child: MainBackgroundWidget()
            ),
            SafeArea(
              child: Navigator(
                key: bottomBarNavigatorKey,
                onGenerateRoute: (settings) {
                  late Widget page;
                  switch (settings.name) {
                    case "/":
                      page = _buildPageWithAppBar();
                      break;
                  }

                  return MaterialPageRoute<dynamic>(
                    builder: (context) {
                      return page;
                    },
                    settings: settings,
                  );
                },
              ),
            ),
            SafeArea(
              child: _buildBottomTabBar(),
            ),
          ],
        ),
      ),
    );
  }

  _buildPageWithAppBar() {
    return Stack(
      children: [
        BlocListener<AppCubit, AppState>(
          bloc: _appCubit,
          listenWhen: (prev, current) {
            return prev.isLoggedIn != current.isLoggedIn;
          },
          listener: (context, state) {
            _changeIndexTab(0);
            if (homeNavigatorKey.currentState?.canPop() ?? false) {
              homeNavigatorKey.currentState!.pop();
            }
            if (propertyNavigatorKey.currentState?.canPop() ?? false) {
              propertyNavigatorKey.currentState!.pop();
            }
          },
          child: Container(),
        ),
        Column(
          children: [
            BlocBuilder<AppCubit, AppState>(
              buildWhen: (prev, current) =>
                  prev.currentTab != current.currentTab ||
                  prev.openBottomBarFromHome != current.openBottomBarFromHome ||
                  prev.openBottomBarFromProfile !=
                      current.openBottomBarFromProfile ||
                  prev.openHomeUtil != current.openHomeUtil ||
                  prev.openProfileUtil != current.openProfileUtil,
              builder: (context, state) {
                if (state.currentTab == 0) {
                  return state.hasAppBarTab0 ? _buildSearchBar() : Container();
                } else if (state.currentTab == 3) {
                  return state.hasAppBarTab3 ? _buildSearchBar() : Container();
                } else {
                  return Container();
                }
              },
            ),
            Expanded(
              child: Navigator(
                key: notifyNavigatorKey,
                initialRoute: "/",
                onGenerateRoute: _onGenerateRoute,
              ),
            ),
            SizedBox(height: 50),
          ],
        ),
      ],
    );
  }

  Route _onGenerateRoute(RouteSettings settings) {
    late Widget page;
    switch (settings.name) {
      case "/":
        page = _buildPageView();
        break;
    }

    return MaterialPageRoute<dynamic>(
      builder: (context) {
        return page;
      },
      settings: settings,
    );
  }

  Widget _item(
      {required String title, TextStyle? titleStyle, required String image}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 18, width: 18, child: Image.asset(image)),
        Text(
          title,
          style: titleStyle,
        )
      ],
    );
  }

  Widget _iconMenu() {
    return Container(
      height: 58,
      width: 58,
      padding: EdgeInsets.only(bottom: 4, left: 4),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.orange,
        border: Border.all(
          width: 3,
          color: Colors.white,
        ),
      ),
      child: Image.asset(AppImages.icMenu),
    );
  }

  void _changeIndexTab(int index) {
    if (_appCubit!.state.isLoggedIn || index == 0) {
      // [Fix]: Fix later
      // if (index == 1 &&
      //     _appCubit!.state.verifyAccountStatus !=
      //         VerifyAccountStatus.APPROVED) {
      //   _requestVerifyAccount();
      //   return;
      // }

      if (index == 2) {
        if (_cubit!.state.currentTabIndex != 2) {
          bool isHasProperty = _appCubit?.state.hasProperty ?? false;
          if (isHasProperty) {
            _cubit!
                .checkShowIntroCommunity(
              emailUser: context.read<AppCubit>().state.user?.email ?? "",
            )
                .then((value) {
              Application.router!.navigateTo(
                value
                    ? bottomBarNavigatorKey.currentContext!
                    : appNavigatorKey.currentContext!,
                value ? Routes.communityMain : Routes.communityIntro,
                transitionDuration: Duration(milliseconds: 200),
                transition: value
                    ? TransitionType.nativeModal
                    : TransitionType.inFromBottom,
              );
            });
            _cubit!.changeTab(index);
          } else {
            Flushbar(
              icon: Icon(Icons.error, size: 32, color: Colors.white),
              shouldIconPulse: false,
              message: S.current.community_require_has_apartment,
              duration: Duration(seconds: 3),
              flushbarPosition: FlushbarPosition.TOP,
              margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ).show(context);
          }
        } else {
          _handleTabOnTabAgain(index);
        }
        return;
      }

      if (_appCubit!.state.openNotificationPage) {
        Navigator.of(notifyNavigatorKey.currentContext!).pop();
        _appCubit!.openNotificationPage(false);
      }

      Navigator.of(bottomBarNavigatorKey.currentContext!)
          .popUntil((route) => route.settings.name == '/');
      if (_cubit!.state.currentTabIndex != index) {
        _cubit!.changeTab(index);
      } else {
        _handleTabOnTabAgain(index);
      }
    } else {
      showSignIn();
    }
  }

  Widget _buildPageView() {
    return BlocListener<MainCubit, MainState>(
      bloc: _cubit,
      child: PageView(
        controller: _pageController,
        physics: NeverScrollableScrollPhysics(),
        children: pages,
      ),
      listenWhen: (prev, current) {
        return prev.currentTabIndex != current.currentTabIndex;
      },
      listener: (context, state) {
        if (state.currentTabIndex == 0) {
          _appCubit!.setOpenBottomBarFromHome(false);
          _appCubit!.setCurrentTab(0);
        } else {
          _appCubit!.setOpenBottomBarFromHome(true);
        }
    
        if (state.currentTabIndex == 3) {
          _appCubit!.setOpenBottomBarFromProfile(false);
          _appCubit!.setCurrentTab(3);
        } else {
          _appCubit!.setOpenBottomBarFromProfile(true);
        }
    
        _pageController.jumpToPage(state.currentTabIndex);
      },
    );
  }

  Widget _buildSearchBar() {
    return SafeArea(
      child: BlocBuilder<AppCubit, AppState>(
          bloc: _appCubit,
          buildWhen: (prev, current) {
            return prev.isHasNotification != current.isHasNotification ||
                prev.isLoggedIn != current.isLoggedIn ||
                prev.currentTab != current.currentTab;
          },
          builder: (context, state) {
            return Container(
              padding: EdgeInsets.only(left: 15, right: 15, top: 12),
              child: MainSearchBarWidget(
                onNotificationPressed: openNotificationListPage,
                onSearchPressed: openSearchPage,
                isLoggedIn: state.isLoggedIn,
              ),
            );
          }),
    );
  }

  Widget _buildBottomTabBar() {
    return Stack(
      children: [
        Positioned(
          bottom: 0,
          right: 0,
          left: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10),
            height: 52,
            decoration: BoxDecoration(
              boxShadow: AppShadow.boxShadowBottomBar,
              color: Colors.white,
            ),
            child: BlocBuilder<MainCubit, MainState>(
              bloc: _cubit,
              buildWhen: (previous, current) =>
                  previous.currentTabIndex != current.currentTabIndex,
              builder: (context, state) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(0);
                        },
                        child: _item(
                          title: S.of(context).main_tabHome,
                          titleStyle: _cubit!.state.currentTabIndex == 0
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _cubit!.state.currentTabIndex == 0
                              ? AppImages.icHomeSelected
                              : AppImages.icHomeNormal,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(1);
                        },
                        child: _item(
                          title: S.current.main_properties,
                          titleStyle: _cubit!.state.currentTabIndex == 1
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _cubit!.state.currentTabIndex == 1
                              ? AppImages.icPropertySelected
                              : AppImages.icPropertyNormal,
                        ),
                      ),
                    ),
                    // Expanded(flex: 1, child: Container()),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(2);
                        },
                        child: _item(
                          title: S.current.main_community,
                          titleStyle: _cubit!.state.currentTabIndex == 2
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _cubit!.state.currentTabIndex == 2
                              ? AppImages.icCommunitySelected
                              : AppImages.icCommunity,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          _changeIndexTab(3);
                        },
                        child: _item(
                          title: S.current.main_account,
                          titleStyle: _cubit!.state.currentTabIndex == 3
                              ? AppTextStyle.tintS12
                              : AppTextStyle.blackS12,
                          image: _cubit!.state.currentTabIndex == 3
                              ? AppImages.icProfileSelected
                              : AppImages.icProfileNormal,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        // Positioned(
        //   bottom: 6,
        //   left: MediaQuery.of(context).size.width / 2 - 29,
        //   child: _iconMenu(),
        // )
      ],
    );
  }

  ///Token expired
  void _setupTokenExpired() {
    _tokenExpiredSubscription =
        GlobalEvent.instance.onTokenExpired.stream.listen((expired) {
      if (!mounted) return;
      if (expired) {
        _appCubit!.removeUserSection();
        showTokenExpiredDialog();
      }
    });
  }

  void showTokenExpiredDialog() {
    DialogUtils.showErrorDialog(
      context,
      title: "Phiên đăng nhập đã hết hạn.",
      okText: "Đăng nhập lại!",
      showCloseButton: true,
      onOkPressed: () {
        Application.router!
            .navigateTo(context, Routes.signIn, clearStack: true);
      },
    );
  }

  ///Firebase message
  void _setupFireBaseMessage() async {
    final deviceToken = await FirebaseMessaging.instance.getToken();
    logger.i("deviceToken = $deviceToken");

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (!mounted) return;
      if (_appCubit?.state.isLoggedIn ?? false) {
        _appCubit!.getNotificationCountUnread();
      }
      handleShowDialogWithIncomingMessage(message);
    });

    // FirebaseMessaging.instance.configure(
    //   onMessage: (Map<String, dynamic> message) async {
    //     logger.i("onMessage: $message");
    //     handleShowDialogWithIncomingMessage(message);
    //   },
    // onBackgroundMessage: myBackgroundMessageHandler, //No need handle on Android
    // onLaunch: (Map<String, dynamic> message) async {
    //   logger.i("onLaunch: $message");
    //   handleOpenPageWithIncomingMessage(message);
    // },
    // onResume: (Map<String, dynamic> message) async {
    //   logger.i("onResume: $message");
    //   handleOpenPageWithIncomingMessage(message);
    // },
    // );
  }

  void handleShowDialogWithIncomingMessage(RemoteMessage message) {
    Flushbar flushBar = Flushbar(
      flushbarStyle: FlushbarStyle.FLOATING,
      title: message.notification!.title ?? "",
      message: message.notification!.body ?? "",
      borderRadius: BorderRadius.all(Radius.circular(16)),
      margin: EdgeInsets.all(12),
      boxShadows: AppShadow.boxShadow,
      isDismissible: true,
      flushbarPosition: FlushbarPosition.TOP,
      backgroundColor: AppColors.main,
      icon: Icon(Icons.assignment, color: Colors.white),
      duration: Duration(seconds: 3),
      onStatusChanged: (status) {
        DialogUtils.isShowFlushBarNotification =
            status != FlushbarStatus.DISMISSED;
      },
    );
    flushBar.show(context);
  }

  Future<dynamic> myBackgroundMessageHandler(
      Map<String, dynamic> message) async {
    logger.i("myBackgroundMessageHandler: $message");
  }

  void handleOpenPageWithIncomingMessage(Map<String, dynamic> message) {
    final notification = PushNotificationEntity.fromMap(message);
    final type = notification.data?.notificationType;
    if (type == null) {
      return;
    }
    //Todo
  }

  Future<bool> _onWillPop() async {
    if (_pageController.page == MainTabType.home.index &&
        homeNavigatorKey.currentState!.canPop()) {
      homeNavigatorKey.currentState!.pop();
      return false;
    }
    if (_pageController.page == MainTabType.property.index &&
        propertyNavigatorKey.currentState!.canPop()) {
      propertyNavigatorKey.currentState!.pop();
      return false;
    }
    if (_cubit!.state.currentTabIndex != MainTabType.home.index &&
        _cubit!.state.currentTabIndex != 2) {
      _cubit!.changeTab(MainTabType.home.index);
      return false;
    }

    AppDialogSignOut(
        context: context,
        title: "Thoát khỏi ứng dụng!",
        titleRight: "Đồng ý",
        onDismissCallback: () {},
        onSignOutCallback: () {
          SystemNavigator.pop();
        }).show();
    return false;
  }

  void _handleTabOnTabAgain(int index) {
    if (_pageController.page == MainTabType.home.index &&
        homeNavigatorKey.currentState!.canPop()) {
      homeNavigatorKey.currentState!.pop();
    } else {
      _homeNavKey.currentState!.scrollToTop();
    }
    if (_pageController.page == MainTabType.property.index &&
        propertyNavigatorKey.currentState!.canPop()) {
      propertyNavigatorKey.currentState!.pop();
    }
  }

  void _requestVerifyAccount() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RequestVerifyAccountPage(),
      ),
    );
    if (!mounted) return;
    _appCubit!.getProfile();
  }

  ///Navigate
  void openNotificationListPage() async {
    if (_appCubit!.state.isLoggedIn) {
      var result = await Navigator.pushAndRemoveUntil(
        notifyNavigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => NotificationHomePage(),
        ),
        (router) => router.isFirst,
      );
      if (!mounted) return;
      if (result == null) {
        _appCubit!.getNotificationCountUnread();
      }
    } else {
      showSignIn();
    }
  }

  ///Navigate search Page
  void openSearchPage() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchPage(),
      ),
    );
    if (!mounted) return;
  }

  ///Navigate
  void showSignIn() async {
    Application.router!.navigateTo(context, Routes.signIn);
  }
}