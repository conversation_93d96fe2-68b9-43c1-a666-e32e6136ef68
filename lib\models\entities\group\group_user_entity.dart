import 'package:real_care/configs/app_config.dart';
import 'package:real_care/global/global_data.dart';
import 'package:real_care/models/enums/status_cmnd_enum.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';
import '../pos_entity.dart';

part 'group_user_entity.g.dart';

@JsonSerializable()
class GroupUserEntity {
  @JsonKey()
  String? id; //đây là userId của TVV trong group
  @JsonKey()
  String? avatarThumbnailUrl;
  @JsonKey()
  String? avatar;
  @Json<PERSON>ey()
  String? name;
  @JsonKey()
  String? email;
  @Json<PERSON>ey()
  String? phone;
  @JsonKey()
  String? status;
  @JsonKey()
  String? dob;
  @JsonKey()
  bool? isAdmin;
  @JsonKey()
  String? joinedDate;
  @JsonKey()
  String? userId; //đây là userId của employee
  @<PERSON><PERSON><PERSON><PERSON>()
  String? requestedDate;
  @JsonKey()
  PosEntity? pos;
  @JsonKey()
  String? groupStatus;

  StatusCmndEnum get verifyType {
    return StatusCmndEnumExtension.getStatusType(status);
  }

  GroupUserEntity({
    this.id,
    this.avatarThumbnailUrl,
    this.avatar,
    this.name,
    this.email,
    this.phone,
    this.status,
    this.dob,
    this.pos,
    this.requestedDate,
    this.userId,
    this.joinedDate,
    this.isAdmin,
    this.groupStatus,
  });

  // DateTime get getDateOfBirth{
  //   return dob.checkDateTime();
  // }

  String get avatarThumbnail {
    String avt = '';
    if ((avatarThumbnailUrl ?? "").isNotEmpty) {
      avt = avatarThumbnailUrl!;
    } else {
      avt = avatar ?? "";
    }
    return avt;
  }

  String get getId {
    return userId ?? id ?? "";
  }

  String get getJoinedDate {
    return DateUtils.toDateTimeString(
      DateTime.parse(joinedDate ?? ''),
      format: AppConfig.dateDisplayFormat,
    );
  }

  factory GroupUserEntity.fromJson(Map<String, dynamic> json) => _$GroupUserEntityFromJson(json);

  Map<String, dynamic> toJson() => _$GroupUserEntityToJson(this);
}
