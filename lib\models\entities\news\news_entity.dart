import 'package:real_care/models/entities/project_entity.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';

part 'news_entity.g.dart';

@JsonSerializable()
class NewsEntity {
  @Json<PERSON>ey()
  String? status;
  @Json<PERSON>ey()
  List<String>? favorites;
  @JsonKey()
  String? name;
  @Json<PERSON>ey()
  ProjectEntity? category;
  @JsonKey()
  ProjectEntity? project;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? coverImage;
  @JsonKey()
  bool? isHot;
  @Json<PERSON>ey()
  String? id;
  @JsonKey()
  String? code;
  @J<PERSON><PERSON><PERSON>()
  String? startDate;
  @Json<PERSON>ey()
  DateTime? endDate;
  @J<PERSON><PERSON><PERSON>()
  String? slug;
  @JsonKey()
  String? modifiedBy;
  @JsonKey()
  DateTime? createdDate;
  @JsonKey()
  DateTime? modifiedDate;
  @JsonKey()
  int? v;
  @<PERSON><PERSON><PERSON><PERSON>()
  bool? isFavorite;

  NewsEntity({
    this.status,
    this.favorites,
    this.name,
    this.category,
    this.project,
    this.coverImage,
    this.isHot,
    this.id,
    this.code,
    this.startDate,
    this.endDate,
    this.slug,
    this.modifiedBy,
    this.createdDate,
    this.modifiedDate,
    this.v,
    this.isFavorite,
  });

  factory NewsEntity.fromJson(Map<String, dynamic> json) => _$NewsEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$NewsEntityToJson(this);

  NewsEntity copyWith({
    String? status,
    List<String>? favorites,
    String? name,
    ProjectEntity? category,
    ProjectEntity? project,
    String? coverImage,
    bool? isHot,
    String? id,
    String? code,
    DateTime? startDate,
    DateTime? endDate,
    String? slug,
    String? modifiedBy,
    DateTime? createdDate,
    DateTime? modifiedDate,
    int? v,
    bool? isFavorite,
  }) {
    return new NewsEntity(
      status: status ?? this.status,
      favorites: favorites ?? this.favorites,
      name: name ?? this.name,
      category: category ?? this.category,
      project: project ?? this.project,
      coverImage: coverImage ?? this.coverImage,
      isHot: isHot ?? this.isHot,
      id: id ?? this.id,
      code: code ?? this.code,
      startDate: startDate as String? ?? this.startDate,
      endDate: endDate ?? this.endDate,
      slug: slug ?? this.slug,
      modifiedBy: modifiedBy ?? this.modifiedBy,
      createdDate: createdDate ?? this.createdDate,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      v: v ?? this.v,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}
