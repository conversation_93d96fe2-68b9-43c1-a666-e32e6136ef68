import 'package:real_care/models/entities/chat_rocket/room_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'room_chat_entity.g.dart';

@JsonSerializable()
class RoomChatEntity {
  @Json<PERSON>ey(name: 'update')
  List<RoomEntity>? update;

  @Json<PERSON>ey(name: 'remove')
  List<dynamic>? remove;

  @Json<PERSON>ey(name: 'success')
  bool? success;

  RoomChatEntity({
    this.update,
    this.remove,
    this.success,
  });

  factory RoomChatEntity.fromJson(Map<String, dynamic> json) => _$RoomChatEntityFromJson(json);

  Map<String, dynamic> toJson() => _$RoomChatEntityToJson(this);
}
