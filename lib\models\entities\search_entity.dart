import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/investor_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'search_entity.g.dart';

@JsonSerializable()
class SearchEntity {
  @Json<PERSON>ey()
  String? name;
  @Json<PERSON>ey()
  int? clickCount;
  @Json<PERSON>ey()
  String? status;
  @JsonKey()
  String? description;
  @JsonKey()
  String? modifiedBy;
  @Json<PERSON>ey()
  String? id;
  @Json<PERSON>ey()
  String? eventName;
  @JsonKey()
  String? actionName;
  @JsonKey()
  String? createdDate;
  @JsonKey()
  String? modifiedDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: "__v")
  int? v;
  @Json<PERSON>ey()
  List<ProjectEntity>? projects;
  @JsonKey()
  List<InvestorEntity>? investors;
  @JsonKey()
  List<PromotionEntity>? promotions;
  @JsonKey()
  List<NewsEntity>? news;

  SearchEntity({
    this.name,
    this.clickCount,
    this.status,
    this.description,
    this.modifiedBy,
    this.id,
    this.eventName,
    this.actionName,
    this.createdDate,
    this.modifiedDate,
    this.v,
  });

  factory SearchEntity.fromJson(Map<String, dynamic> json) => _$SearchEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$SearchEntityToJson(this);
}
