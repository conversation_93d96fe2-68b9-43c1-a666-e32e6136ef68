import 'dart:io';

import 'package:real_care/ui/pages/profile/verify_profile/verify_profile_page.dart';
import 'package:real_care/utils/logger.dart';
import 'package:image_picker/image_picker.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:uuid/uuid.dart';

class AttachFileHelper {
  AttachFileHelper._privateConstructor();

  static final AttachFileHelper shared = AttachFileHelper._privateConstructor();

  Future<List<AssetEntity>> getListImageFromLibrary({required bool enableCamera}) async {
    List<AssetEntity> resultList = [];
    try {
      // Request permission
      final permitted = await PhotoManager.requestPermissionExtend();
      if (!permitted.isAuth) {
        logger.d('Permission denied');
        return resultList;
      }

      // Get all albums/folders
      final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: RequestType.image,
        onlyAll: true,
      );

      if (albums.isEmpty) return resultList;

      // Get all assets (images) from the first album (usually "Recent" or "All Photos")
      final List<AssetEntity> assets = await albums[0].getAssetListRange(
        start: 0,
        end: 100, // Limit to 100 images like the original implementation
      );

      resultList = assets;
    } catch (e) {
      logger.e('Error picking images: $e');
    }

    return resultList;
  }

  Future<File?> getImageFromLibrary() async {
    File? image;
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        image = File(pickedFile.path);
      } else {
        logger.d('No image selected.');
      }
    } on Exception catch (e) {
      logger.e('Error picking image: $e');
    }
    return image;
  }

  Future<File?> getPictureByCamera() async {
    final picker = ImagePicker();
    File? image;
    try {
      var resultList = await picker.pickImage(source: ImageSource.camera);
      //đã bỏ image quality
      if (resultList != null) {
        image = File(resultList.path);
      } else {
        logger.d('No image selected.');
      }
    } on Exception catch (e) {
      e.toString();
    }
    return image;
  }

  Future<List<AttachFile>> get imageFromLibrary async {
    List<AttachFile> attachFiles = [];

    File? attachFile = await getImageFromLibrary();

    if (attachFile == null) return [];

    /// size của file... tạm thời chưa cần
    var uuid = Uuid();
    var id = uuid.v1();

    /// cần biết file upload có param như nào mới đóng gói dc trong đoạn này
    AttachFile file = AttachFile(
      id: id,
      file: File(attachFile.path),
      name: attachFile.path.split("/").last,
      mime: getMimeType(attachFile.path.split("/").last),
    );
    attachFiles.add(file);

    return attachFiles;
  }

  Future<List<AttachFile>> get imageFromCamera async {
    List<AttachFile> attachFiles = [];

    File? cameraPickers = await getPictureByCamera();

    if (cameraPickers == null) return [];

    /// size của file... tạm thời chưa cần
    //int countSize = event.countSize ?? 0;

    // cấp cho nó 1 cái id nào đó để dễ xử lý
    var uuid = Uuid();
    var id = uuid.v1();

    /// cần biết file upload có param như nào mới đóng gói dc trong đoạn này
    AttachFile file = AttachFile(
      id: id,
      path: cameraPickers.path,
      file: cameraPickers,
      mime: getMimeType(cameraPickers.path.split("/").last),
      name: cameraPickers.path.split("/").last,
    );
    attachFiles.add(file);

    return attachFiles;
  }

  String getMimeType(String name) {
    final extension = name.toLowerCase();
    switch (extension) {
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case '.zip':
        return 'application/zip';
      case '.rtf':
        return 'application/rtf';
      case '.eps':
        return 'application/postscript';
      case '.txt':
        return 'text/plain';
      case '.m4v':
        return 'application/pdf';
      case '.mp4':
        return 'video/x-m4v';
      case '.psd':
        return 'image/x-photoshop';
      case '.ai':
        return 'application/postscript';
      case '.img':
        return 'application/octet-stream';
      case '.svg':
        return 'image/svg+xml';
      case '.bmp':
        return 'image/x-ms-bmp';
      case '.png':
        return 'image/png';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.gif':
        return 'image/gif';
      case '.heic':
        return 'image/heic';
      case '.heif':
        return 'image/heif';
      default:
        return '';
    }
  }

  // bool equalsIgnoreCase(String string1, String string2) {
  //   return string1.toLowerCase().contains(string2.toLowerCase());
  // }
}