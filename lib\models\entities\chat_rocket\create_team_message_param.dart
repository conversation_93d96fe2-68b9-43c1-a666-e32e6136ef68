import 'package:json_annotation/json_annotation.dart';

part 'create_team_message_param.g.dart';

@JsonSerializable()
class CreateTeamMessageParam {
  @Json<PERSON>ey()
  String? name;
  @Json<PERSON>ey()
  int? type;
  @J<PERSON><PERSON>ey()
  List<String>? members;
  @JsonKey()
  String? room;

  CreateTeamMessageParam({
    this.room,
    this.type,
    this.name,
    this.members,
  });

  factory CreateTeamMessageParam.fromJson(Map<String, dynamic> json) => _$CreateTeamMessageParamFromJson(json);

  Map<String, dynamic> toJson() => _$CreateTeamMessageParamToJson(this);
}
