import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/invoice/service_change_entity.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:real_care/models/entities/invoice/service_invoice_detail_entity.dart';

part 'invoice_entity.g.dart';

@JsonSerializable()
class InvoiceEntity extends Equatable {
  @Json<PERSON>ey(name: '_id')
  String? id_;
  @JsonKey()
  String? id;
  @Json<PERSON>ey()
  bool? softDelete;
  @JsonKey()
  String? name;
  @JsonKey()
  String? code;
  @JsonKey()
  String? apartment;
  @JsonKey()
  String? type;
  @JsonKey()
  int? unitPrice;
  @JsonKey()
  int? amount;
  @JsonKey()
  int? firstNumber;
  @JsonKey()
  int? lastNumber;
  @JsonKey()
  int? paymentTotal;
  @JsonKey()
  String? paymentDueDate;
  @J<PERSON><PERSON><PERSON>()
  String? status;
  @Json<PERSON>ey()
  String? createdDate;
  @<PERSON><PERSON><PERSON><PERSON>()
  ServiceChangeInvoiceEntity? serviceCharge;
  @Json<PERSON>ey()
  String? note;
  @JsonKey()
  String? paymentDate;
  @JsonKey()
  String? paymentUrl;
  @JsonKey()
  List<ServiceInvoiceDetailEntity>? services;

  InvoiceEntity({
    this.id_,
    this.id,
    this.softDelete,
    this.name,
    this.code,
    this.apartment,
    this.type,
    this.unitPrice,
    this.amount,
    this.firstNumber,
    this.lastNumber,
    this.paymentTotal,
    this.paymentDueDate,
    this.status,
    this.createdDate,
    this.serviceCharge,
    this.note,
    this.paymentDate,
    this.paymentUrl,
    this.services,
  });

  factory InvoiceEntity.fromJson(Map<String, dynamic> json) => _$InvoiceEntityFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceEntityToJson(this);

  InvoiceEntity copyWith({
    String? id_,
    String? id,
    bool? softDelete,
    String? name,
    String? code,
    String? apartment,
    String? type,
    int? unitPrice,
    int? amount,
    int? firstNumber,
    int? lastNumber,
    int? paymentTotal,
    String? paymentDueDate,
    String? status,
    String? createdDate,
    ServiceChangeInvoiceEntity? serviceCharge,
    String? note,
    String? paymentDate,
    String? paymentUrl,
  }) {
    return InvoiceEntity(
      id_: id_ ?? this.id_,
      id: id ?? this.id,
      softDelete: softDelete ?? this.softDelete,
      name: name ?? this.name,
      code: code ?? this.code,
      status: status ?? this.status,
      apartment: apartment ?? this.apartment,
      type: type ?? this.type,
      unitPrice: unitPrice ?? this.unitPrice,
      amount: amount ?? this.amount,
      firstNumber: firstNumber ?? this.firstNumber,
      lastNumber: lastNumber ?? this.lastNumber,
      paymentTotal: paymentTotal ?? this.paymentTotal,
      paymentDueDate: paymentDueDate ?? this.paymentDueDate,
      createdDate: createdDate ?? this.createdDate,
      serviceCharge: serviceCharge ?? this.serviceCharge,
      note: note ?? this.note,
      paymentDate: paymentDate ?? this.paymentDate,
      paymentUrl: paymentUrl ?? this.paymentUrl,
      services: services ?? services,
    );
  }

  @override
  List<Object?> get props => [
    id_,
    id,
    softDelete,
    name,
    code,
    apartment,
    type,
    unitPrice,
    amount,
    firstNumber,
    lastNumber,
    paymentTotal,
    paymentDueDate,
    status,
    createdDate,
    serviceCharge,
    note,
    paymentDate,
    paymentUrl,
    services,
  ];
}