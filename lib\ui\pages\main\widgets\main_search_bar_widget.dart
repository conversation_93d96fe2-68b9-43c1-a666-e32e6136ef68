import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:real_care/ui/components/app_cache_image.dart';

import '../main_cubit.dart';

class MainSearchBarWidget extends StatelessWidget {
  final TextEditingController? textEditingController;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onNotificationPressed;
  final VoidCallback? onSearchPressed;
  final bool? isLoggedIn;

  const MainSearchBarWidget(
      {super.key, this.textEditingController,
      this.onSubmitted,
      this.onNotificationPressed,
      this.onSearchPressed,
      this.isLoggedIn});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 30,
      child: Row(
        children: [
          BlocBuilder<MainCubit, MainState>(builder: (context, mainState) {
            return BlocBuilder<AppCubit, AppState>(builder: (context, state) {
              if (mainState.currentTabIndex == 3) {
                return Container();
              } else {
                return Container(
                    width: 32,
                    height: 32,
                    margin: EdgeInsets.only(right: 6),
                    child: state.user?.images?.avatar != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(16.0),
                            child: AppCacheImage(url: state.user?.images?.avatar))
                        : Image.asset(
                            AppImages.icAvatar,
                          ));
              }
            });
          }),
          Expanded(child:
              BlocBuilder<MainCubit, MainState>(builder: (context, mainState) {
            return BlocBuilder<AppCubit, AppState>(builder: (context, state) {
              if (mainState.currentTabIndex == 3) {
                return Text(S.of(context).profile_management,
                    style: AppTextStyle.whiteS16Bold);
              } else {
                if (state.user == null) {
                  return Row(children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: Image.asset(
                        AppImages.icPadlock,
                        color: Colors.white,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        _openLoginPage(context);
                      },
                      child: Text(
                        S.current.sign_up_btn,
                        style: const TextStyle(
                        color: Colors.white, fontSize: 14),
                      )
                    )
                  ]);
                } else {
                  return Text(
                    "Chào, ${state.user!.name!}!",
                    style: const TextStyle(color: Colors.white, fontSize: 14)
                  );
                }
              }
            });
          })),
          GestureDetector(
            onTap: onSearchPressed,
            child: SizedBox(
              width: 32,
              height: 32,
              child: Image.asset(
                AppImages.icSearch,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(width: 16),
          BlocBuilder<AppCubit, AppState>(buildWhen: (prev, current) {
            return prev.unreadNotification != current.unreadNotification ||
                prev.isHasNotification != current.isHasNotification;
          }, builder: (context, state) {
            return GestureDetector(
              onTap: onNotificationPressed,
              child: Container(
                  width: 42,
                  height: 42,
                  clipBehavior: Clip.none,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Stack(alignment: Alignment.center, children: [
                    Image.asset(AppImages.icBellWhite),
                    isLoggedIn! && state.unreadNotification > 0
                        ? Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 4),
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle, color: Colors.red),
                              alignment: Alignment.center,
                              child: Text("${state.unreadNotification}",
                                  style: AppTextStyle.whiteS9),
                            ),
                          )
                        : Center()
                  ])),
            );
          }),
        ],
      ),
    );
  }

  void _openLoginPage(context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Application.router!.navigateTo(context, Routes.signIn, rootNavigator: true);
    });
  }
}
