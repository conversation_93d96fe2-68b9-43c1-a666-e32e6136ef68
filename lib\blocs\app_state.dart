part of 'app_cubit.dart';

class AppState extends Equatable {
  final UserEntity? user;
  final LoadStatus fetchUser;
  final LoadStatus signOutStatus;

  ///Verify
  final LoadStatus verifyProfile;

  ///Upload Avatar
  final LoadStatus uploadAvatarStatus;
  final File? personalAvatar;
  final String? avatar;

  ///Noti
  final bool isHasNotification;
  final int unreadNotification;
  final bool openNotificationPage;

  final bool openBottomBarFromHome;
  final bool openBottomBarFromProfile;
  final bool openHomeUtil;
  final bool openProfileUtil;
  final int currentTab;

  ///property
  final bool hasProperty;

  bool get hasAppBarTab0 {
    if (!openHomeUtil && !openBottomBarFromHome) {
      return true;
    } else if (!openHomeUtil && openBottomBarFromHome) {
      return true;
    } else if (openHomeUtil && openBottomBarFromHome) {
      return true;
    } else if (openHomeUtil && !openBottomBarFromHome) {
      return false;
    } else {
      return false;
    }
  }

  bool get hasAppBarTab3 {
    if (!openProfileUtil && !openBottomBarFromProfile) {
      return true;
    } else if (!openProfileUtil && openBottomBarFromProfile) {
      return true;
    } else if (openProfileUtil && openBottomBarFromProfile) {
      return true;
    } else if (openProfileUtil && !openBottomBarFromProfile) {
      return false;
    } else {
      return false;
    }
  }

  const AppState({
    this.user,
    this.fetchUser = LoadStatus.INITIAL,
    this.signOutStatus = LoadStatus.INITIAL,
    this.verifyProfile = LoadStatus.INITIAL,
    this.uploadAvatarStatus = LoadStatus.INITIAL,
    this.personalAvatar,
    this.avatar,
    this.isHasNotification = false,
    this.hasProperty = false,
    this.unreadNotification = 0,
    this.openNotificationPage = false,
    this.currentTab = 0,
    this.openBottomBarFromHome = false,
    this.openBottomBarFromProfile = false,
    this.openProfileUtil = false,
    this.openHomeUtil = false
  });

  AppState copyWith({
    UserEntity? user,
    LoadStatus? fetchUser,
    LoadStatus? signOutStatus,
    LoadStatus? verifyProfile,
    LoadStatus? uploadAvatarStatus,
    File? personalAvatar,
    String? avatar,
    bool? isHasNotification,
    int? unreadNotification,
    bool? openNotificationPage,
    bool? openBottomBarFromProfile,
    bool? openBottomBarFromHome,
    bool? openHomeUtil,
    bool? openProfileUtil,
    bool? hasProperty,
    int? currentTab,
  }) {
    return AppState(
      user: user ?? this.user,
      hasProperty: hasProperty ?? this.hasProperty,
      fetchUser: fetchUser ?? this.fetchUser,
      signOutStatus: signOutStatus ?? this.signOutStatus,
      verifyProfile: verifyProfile ?? this.verifyProfile,
      uploadAvatarStatus: uploadAvatarStatus ?? this.uploadAvatarStatus,
      personalAvatar: personalAvatar ?? this.personalAvatar,
      avatar: avatar ?? this.avatar,
      isHasNotification: isHasNotification ?? this.isHasNotification,
      unreadNotification: unreadNotification ?? this.unreadNotification,
      openNotificationPage: openNotificationPage ?? this.openNotificationPage,
      openBottomBarFromHome:
          openBottomBarFromHome ?? this.openBottomBarFromHome,
      openBottomBarFromProfile:
          openBottomBarFromProfile ?? this.openBottomBarFromProfile,
      openHomeUtil: openHomeUtil ?? this.openHomeUtil,
      openProfileUtil: openProfileUtil ?? this.openProfileUtil,
      currentTab: currentTab ?? this.currentTab
    );
  }

  bool get isLoggedIn {
    return user != null;
  }

  // [Fix]: Fix later
  // VerifyAccountStatus? get verifyAccountStatus {
  //   return user!.verifyAccountStatus;
  // }

  @override
  List<Object?> get props => [
    user,
    fetchUser,
    signOutStatus,
    verifyProfile,
    uploadAvatarStatus,
    personalAvatar,
    avatar,
    isHasNotification,
    unreadNotification,
    openNotificationPage,
    openBottomBarFromHome,
    openBottomBarFromProfile,
    openHomeUtil,
    openProfileUtil,
    currentTab,
    hasProperty
  ];
}