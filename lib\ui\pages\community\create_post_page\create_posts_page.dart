import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:another_flushbar/flushbar.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_gradient.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_shadow.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/commons/screen_size.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/global/global_data.dart';
import 'package:real_care/models/entities/group/group_post_entity.dart';
import 'package:real_care/models/entities/group/survey_question.dart';
import 'package:real_care/models/entities/media_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/buttons/h24-outlined-white-button.dart';
import 'package:real_care/ui/components/app_cache_image.dart';
import 'package:real_care/ui/components/app_cache_image_default_size.dart';
import 'package:real_care/ui/components/avatar.dart';
import 'package:real_care/ui/components/group/images_group_item_post.dart';
import 'package:real_care/ui/components/my_app_bar.dart';
import 'package:real_care/ui/components/normal_textfield.dart';
import 'package:real_care/ui/dialogs/app_dialog.dart';
import 'package:real_care/ui/dialogs/file_picker_dialog.dart';
import 'package:real_care/ui/pages/community/create_post_page/create_posts_cubit.dart';
import 'package:real_care/ui/pages/community/create_post_page/create_posts_state.dart';
import 'package:real_care/ui/pages/community/widget/item_post_group.dart';
import 'package:real_care/ui/pages/community/widget/mock.dart';
import 'package:real_care/ui/widgets/loading_indicator_widget.dart';
import 'package:real_care/utils/dialog_utils.dart';
import 'package:real_care/utils/file_utils.dart';
import 'package:real_care/utils/logger.dart';
import 'package:real_care/utils/utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class CreatePostsPage extends StatefulWidget {
  final Function({
    List<File>? files,
    String? content,
    String? thumbnail,
    List<SurveyQuestion>? poll,
  })? onCreatePost;
  final Function({
    List<File>? files,
    List<MediaEntity>? fileHasUpdate,
    String? content,
    String? thumbnail,
    List<SurveyQuestion>? poll,
  })? onEditPost;
  final String groupId;
  final bool isEdit;

  final GroupPostEntity? itemPost;
  final String nameGroup;
  final String? postId;
  final String? content;
  final List<MediaEntity>? listMedia;

  const CreatePostsPage({
    Key? key,
    required this.groupId,
    this.itemPost,
    required this.nameGroup,
    this.onCreatePost,
    this.onEditPost,
    required this.isEdit,
    this.content,
    this.listMedia,
    this.postId,
  }) : super(key: key);

  @override
  _CreatePostsPageState createState() => _CreatePostsPageState();
}

class _CreatePostsPageState extends State<CreatePostsPage> {
  bool isLogin = false;
  late final CreatePostsCubit _createPostsCubit;
  late final AppCubit _appCubit;
  late List<File> filesPost = <File>[];
  late double imageSize;

  late TextEditingController controller;

  @override
  void initState() {
    _createPostsCubit = BlocProvider.of<CreatePostsCubit>(context);
    _appCubit = BlocProvider.of<AppCubit>(context);
    controller = TextEditingController(text: widget.content);
    controller.addListener(() {
      setState(() {});
    });
    if (widget.listMedia != null) {
      List<MediaEntity>? listMedia = [];
      listMedia.addAll(widget.listMedia!);
      _createPostsCubit.loadListMedia(listMedia);
    }
  }

  @override
  void dispose() {
    // _showMessageSubscription.cancel();
    // _removeAnswerSubscription.cancel();
    super.dispose();
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InkWell(
        onTap: Utils.dismissKeyboard,
        child: Scaffold(
            body: BlocConsumer<CreatePostsCubit, CreatePostsState>(
          bloc: _createPostsCubit,
          buildWhen: (prev, current) =>
              prev.createPostState != current.createPostState ||
              prev.uploadFileState != current.uploadFileState,
          listener: (context, state) async {
            if (state.createPostState == LoadStatus.SUCCESS) {
              AppDialog(
                  context: context,
                  dismissible: true,
                  autoDismiss: true,
                  title: widget.isEdit
                      ? "Sửa bài đăng thành công"
                      : S.current.group_createPost_createPostSuccess,
                  onDismissed: () {
                    Navigator.of(context).pop(true);
                  },
                  icon: Image.asset(
                    AppImages.icCheckInSuccess,
                    height: 70,
                    width: 70,
                    fit: BoxFit.cover,
                  )).show();
            }

            if (state.createPostState == LoadStatus.FAILURE) {
              _createPostsCubit.resetState();
              AppDialog(
                  context: context,
                  dismissible: true,
                  autoDismiss: true,
                  title: widget.isEdit
                      ? "Có lỗi xảy ra khi sửa bài đăng"
                      : S.current.group_createPost_createPostFailure,
                  onDismissed: () {},
                  icon: Image.asset(
                    AppImages.icError,
                    height: 70,
                    width: 70,
                    fit: BoxFit.cover,
                  )).show();
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                _buildAppBarWidget,
                Column(
                  children: [
                    SizedBox(
                      height: 45 + ScreenSize.of(context).topPadding,
                    ),
                    Expanded(
                      child: Container(
                        // width: double.infinity,
                        padding: const EdgeInsets.only(top: 15, bottom: 60),
                        decoration: const BoxDecoration(
                          color: AppColors.background,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 5,
                              ),
                              _buildCardUserInfo(),
                              _buildTextField,
                              const SizedBox(height: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  BlocBuilder<CreatePostsCubit, CreatePostsState>(
                                    bloc: _createPostsCubit,
                                    buildWhen: (previous, current) {
                                      return previous.numberOfText !=
                                          current.numberOfText;
                                    },
                                    builder: (context, state) {
                                      if (state.numberOfText > 0)
                                        return Text(
                                          "${state.numberOfText}/1000",
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 12,
                                          ),
                                        );
                                      else
                                        return const SizedBox();
                                    },
                                  ),
                                  const SizedBox(width: 15),
                                ],
                              ),
                              _normalPostBody,
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                action,
              ],
            );
          },
        )),
      ),
    );
  }

  Widget _buildCardUserInfo() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          Avatar(
            url: _appCubit.state.user?.images?.avatar,
            avatarSize: 40,
            onSelectImage: () {},
          ),
          SizedBox(
            width: 10,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _appCubit.state.user?.name ?? "",
                style: AppTextStyle.blackS14Bold,
              ),
              SizedBox(
                height: 4,
              ),
              Container(
                height: 19,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.greyCBCBCB,
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                ),
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                child: Row(
                  children: [
                    Image.asset(AppImages.icFriends),
                    SizedBox(
                      width: 4,
                    ),
                    Text(
                      widget.nameGroup,
                      style: AppTextStyle.greyS12,
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget get _buildAppBarWidget {
    return Stack(
      children: [
        Image.asset(
          AppImages.bgHeaderCreatePost,
          width: MediaQuery.of(context).size.width,
          fit: BoxFit.contain,
        ),
        Container(
          padding: EdgeInsets.only(top: ScreenSize.of(context).topPadding),
          child: MyAppBar(
            title: widget.isEdit ? "Sửa bài viết" : "Tạo bài viết",
            onBackPressed: () {
              // Get.back();
              Navigator.of(context).pop();
            },
            actionWidget: H24OutlinedLightblueButton(
              title: widget.isEdit ? "Sửa" : "Đăng",
              isDisable: controller.text.isEmpty,
              isLoading: _createPostsCubit.state.uploadFileState ==
                      LoadStatus.LOADING ||
                  _createPostsCubit.state.createPostState == LoadStatus.LOADING,
              onPressed: () {
                Utils.dismissKeyboard();
                if (widget.isEdit) {
                  _createPostsCubit.updatePost(
                    content: controller.text,
                    postId: widget.postId ?? "",
                  );
                } else {
                  _createPostsCubit.createPost(
                      content: controller.text,
                      groupId: widget.groupId,
                      attachFiles: _createPostsCubit.state.listFilesUpload);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget get _normalPostBody {
    if (widget.isEdit) {
      return BlocBuilder<CreatePostsCubit, CreatePostsState>(
        bloc: _createPostsCubit,
        buildWhen: (prev, current) {
          return prev.uploadMediaStatus != current.uploadMediaStatus;
        },
        builder: (context, state) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                const SizedBox(height: 16),
                getListMediaWidget(),
              ],
            ),
          );
        },
      );
    } else
      return BlocBuilder<CreatePostsCubit, CreatePostsState>(
        bloc: _createPostsCubit,
        buildWhen: (prev, current) {
          return prev.uploadFileState != current.uploadFileState;
        },
        builder: (context, state) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                const SizedBox(height: 16),
                getListFilesWidget(),
              ],
            ),
          );
        },
      );
  }

  Widget get action {
    return Visibility(
      visible: true,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          height: 40,
          width: MediaQuery.of(context).size.width - 40,
          margin: EdgeInsets.only(bottom: 10),
          padding: EdgeInsets.symmetric(horizontal: 15),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: AppShadow.boxShadow,
            borderRadius: const BorderRadius.all(Radius.circular(20)),
          ),
          child: BlocBuilder<CreatePostsCubit, CreatePostsState>(
            bloc: _createPostsCubit,
            buildWhen: (prev, current) =>
                prev.uploadFileState != current.uploadFileState,
            builder: (context, state) {
              return _buildAction(
                icon: AppImages.icAttachImageTint,
                onPress: () async {
                  _uploadImages();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAction({required String icon, required VoidCallback onPress}) {
    return GestureDetector(
      onTap: onPress,
      behavior: HitTestBehavior.opaque,
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("Thêm vào bài viết"),
            Image.asset(
              icon,
              width: 24,
              height: 22,
              fit: BoxFit.contain,
            ),
          ],
        ),
      ),
    );
  }

  void _uploadImages() async {
    List<File> listFiles = _createPostsCubit.state.listFilesUpload ?? [];
    List<dynamic> listMedia = _createPostsCubit.state.listMediaFile;
    ///count number of image and video in list file upload.
    int numberOfImages = listFiles.fold(
      0, (sum, element) => FileUtils.isImage(element) ? sum + 1 : sum,
    );
    int numberOfVideo = listFiles.fold(
      0, (sum, element) => FileUtils.isMp4Video(element) ? sum + 1 : sum,
    );
    int numberOfImageEdit = listMedia.fold(
      0,
      (sum, element) => (element is MediaEntity
              ? element.type == "IMAGE"
              : FileUtils.isImage(element))
          ? sum + 1
          : sum,
    );
    int numberOfVideoEdit = listMedia.fold(
      0,
      (sum, element) => (element is MediaEntity
              ? element.type == "VIDEO"
              : FileUtils.isMp4Video(element))
          ? sum + 1
          : sum,
    );
    int sumOfImage = numberOfImages + numberOfImageEdit;
    int sumOfVideo = numberOfVideo + numberOfVideoEdit;

    if (_createPostsCubit.state.uploadFileState != LoadStatus.LOADING) {
      FileInfo? file = await DialogUtils.pickFile(
        context,
        sourceTypes: [
          FileSourceType.camera,
          FileSourceType.library,
          FileSourceType.video
        ],
        title: "Chọn ảnh/ video cần tải",
      );

      /// Kiểm tra định dạng video, chỉ cho phép tải video MP4
      if (file?.sourceType == FileSourceType.video &&
          !FileUtils.isMp4Video(file?.file)) {
        Flushbar(
          icon: Icon(Icons.error, size: 32, color: Colors.white),
          shouldIconPulse: false,
          message: S.current.group_createPost_onlySupportMp4,
          duration: Duration(seconds: 3),
          flushbarPosition: FlushbarPosition.TOP,
          margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
          borderRadius: BorderRadius.all(Radius.circular(16)),
        )..show(context);
        return;
      }

      /// kiem tra so luong vuot qua cho phep hay khong
      /// image <= 5
      /// video = 1
      if ((FileUtils.isImage(file?.file) && sumOfImage >= 5) ||
          (FileUtils.isMp4Video(file?.file) && sumOfVideo >= 1)) {
        Flushbar(
          icon: Icon(Icons.error, size: 32, color: Colors.white),
          shouldIconPulse: false,
          message: "Giới hạn 1 bài đăng chỉ có 5 hình ảnh, 1 video",
          duration: Duration(seconds: 3),
          flushbarPosition: FlushbarPosition.BOTTOM,
          margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
          borderRadius: BorderRadius.all(Radius.circular(16)),
        )..show(context);
        return;
      }

      if (file!.file != null) {
        if (!widget.isEdit)
          _createPostsCubit.addFilesUpload(file: file.file);
        else
          _createPostsCubit.addFileUploadToMediaFile(file: file.file);
      }
    }
  }

  Widget getListFilesWidget() {
    List<File> listFiles = _createPostsCubit.state.listFilesUpload ?? [];
    return Column(
      children: [
        Container(
            margin: EdgeInsets.only(bottom: 15),
            child: ItemPostGroup(
              files: listFiles,
              deleteFile: (index) {
                _createPostsCubit.removeFiles(index: index);
              },
              context: context,
              isCreatePost: true,
            )),
        Visibility(
          visible: listFiles.length > 0,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: DottedBorder(
              options: RectDottedBorderOptions(
                dashPattern: [4],
                color: AppColors.orange,
                strokeWidth: 1,
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  _uploadImages();
                },
                child: Container(
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width - 40,
                    height: 32,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "+",
                          style: AppTextStyle.orange.copyWith(
                            fontSize: 26,
                          ),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Text(
                          "Thêm ảnh/ Video",
                          style: AppTextStyle.orange,
                        ),
                      ],
                    )),
              ),
            ),
          ),
        ),
        SizedBox(
          height: 15,
        ),
      ],
    );
  }

  Widget getListMediaWidget() {
    List<dynamic> listMedia = _createPostsCubit.state.listMediaFile;
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 15),
          child: ImagesGroupItemPostMock(
            medias: _createPostsCubit.state.listMediaFile,
            thumbnail: "",
            spaceColor: Colors.white,
            spaceWidth: 4,
            isEdit: true,
            mediasPressed: (index) {
              _createPostsCubit.removeIndexFromMediaFile(index);
            },
          ),
        ),
        Visibility(
          visible: listMedia.length > 0,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: DottedBorder(
              options: RectDottedBorderOptions(
                dashPattern: [4],
                color: AppColors.orange,
                strokeWidth: 1,
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  _uploadImages();
                },
                child: Container(
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width - 40,
                    height: 32,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "+",
                          style: AppTextStyle.orange.copyWith(
                            fontSize: 26,
                          ),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Text(
                          "Thêm ảnh/ Video",
                          style: AppTextStyle.orange,
                        ),
                      ],
                    )),
              ),
            ),
          ),
        ),
        SizedBox(
          height: 15,
        ),
      ],
    );
  }

  Widget get _buildTextField {
    return NormalTextField(
      enabled: _createPostsCubit.state.createPostState != LoadStatus.SUCCESS,
      autoFocus: true,
      controller: controller,
      keyboardType: TextInputType.multiline,
      hintText: "Bạn muốn chia sẻ điều gì?",
      maxLength: null,
      hintStyle: AppTextStyle.greyS14,
      onChanged: (value) {
        _createPostsCubit.countNumberOfText(value);
      },
      inputFormatter: [
        LengthLimitingTextInputFormatter(1000),
      ],
    );
    // return BlocBuilder<CreatePostsCubit, CreatePostsState>(
    //   bloc: _createPostsCubit,
    //   buildWhen: (prev, current) =>
    //       prev.nameUser != current.nameUser ||
    //       prev.loadStatusContent != current.loadStatusContent ||
    //       prev.currentPostType != current.currentPostType,
    //   builder: (context, state) {
    //     if (state.loadStatusContent == LoadStatus.LOADING) {
    //       return const LoadingGroupContentWidget();
    //     } else {
    //       final hintText = state.currentPostType == PostType.NormalPost
    //           ? S.current
    //               .group_createPosts_hintTitleCreatePost(state.nameUser ?? "")
    //           : S.current.group_createPosts_hintTitleCreateSurvey + "...";
    //       return NormalTextField(
    //         controller: _contentController,
    //         keyboardType: TextInputType.multiline,
    //         hintText: hintText,
    //         maxLength:
    //             (state.currentPostType == PostType.NormalPost) ? null : 300,
    //         hintStyle: AppTextStyle.greyS14
    //             .copyWith(fontSize: 16, color: AppColors.hindTextA7A7A7),
    //         onChanged: (value) {
    //           if (widget.typeAction == ChangePostAction.Edit) {
    //             _createPostsCubit.onTextChange(value);
    //           }
    //         },
    //       );
    //     }
    //   },
    // );
  }
}
