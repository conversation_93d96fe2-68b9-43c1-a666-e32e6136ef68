import 'package:real_care/models/entities/amenities/utility_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'local_amenities.g.dart';

@JsonSerializable()
class LocalAmenitiesEntity {
  @J<PERSON><PERSON>ey()
  String? utilityTypeId;

  @J<PERSON><PERSON>ey()
  String? utilityType;

  @JsonKey()
  List<UtilityEntity>? utilites;

  LocalAmenitiesEntity({
    this.utilityTypeId,
    this.utilityType,
    this.utilites,
  });

  LocalAmenitiesEntity copyWith({
    String? utilityTypeId,
    String? utilityType,
    List<UtilityEntity>? utilites,
  }) {
    return LocalAmenitiesEntity(
      utilityTypeId: utilityTypeId ?? this.utilityTypeId,
      utilityType: utilityType ?? this.utilityType,
      utilites: utilites ?? this.utilites,
    );
  }

  factory LocalAmenitiesEntity.fromJson(Map<String, dynamic> json) => _$LocalAmenitiesEntityFromJson(json);
  Map<String, dynamic> toJson() => _$LocalAmenitiesEntityToJson(this);
}
