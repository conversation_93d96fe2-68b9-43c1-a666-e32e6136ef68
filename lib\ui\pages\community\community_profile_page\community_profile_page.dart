import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/commons/app_vectors.dart';
import 'package:real_care/models/enums/my_post_profile_type.dart';
import 'package:real_care/repositories/community_repository.dart';
import 'package:real_care/ui/components/app_cache_image.dart';
import 'package:real_care/ui/pages/community/community_create_post_page/community_create_post_page.dart';
import 'package:real_care/ui/pages/community/community_profile_page/profile_posts_page/profile_post_page.dart';
import 'package:real_care/ui/pages/community/community_profile_page/widgets/header_profile_post_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'community_profile_cubit.dart';

class CommunityProfilePage extends StatefulWidget {
  const CommunityProfilePage({
    Key? key,
  }) : super(key: key);

  @override
  _CommunityProfilePageState createState() => _CommunityProfilePageState();
}

class _CommunityProfilePageState extends State<CommunityProfilePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late CommunityProfileCubit _cubit;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: MyPostProfileType.values.length,
      vsync: this,
    );
    CommunityRepository groupRepository =
        RepositoryProvider.of<CommunityRepository>(context);
    _cubit = CommunityProfileCubit(repository: groupRepository);
    _cubit.getAllSavePost();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom + 60),
      child: Scaffold(
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<AppCubit, AppState>(
      bloc: BlocProvider.of<AppCubit>(context),
      buildWhen: (previous, current) =>
          previous.user != current.user ||
          previous.uploadAvatarStatus != current.uploadAvatarStatus,
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(
              height: 14,
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(width: 2, color: AppColors.blue009CFC),
                shape: BoxShape.circle,
              ),
              height: 80,
              width: 80,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50),
                child: AppCacheImage(
                  url: state.user?.images?.avatar ?? "",
                ),
              ),
            ),
            SizedBox(
              height: 6,
            ),
            Text(
              state.user?.name ?? '',
              style: AppTextStyle.blackS14Bold,
            ),
            SizedBox(
              height: 8,
            ),
            _buildTextFieldPost(),
            SizedBox(
              height: 14,
            ),
            Expanded(
              child: HeaderProfilePostWidget(
                cubit: _cubit,
                items: [
                  ProfilePostsPage(
                    cubit: _cubit,
                    type: MyPostProfileType.myPost,
                  ),
                  ProfilePostsPage(
                    cubit: _cubit,
                    type: MyPostProfileType.postSaved,
                  ),
                ],
                tabController: _tabController,
              ),
            )
          ],
        );
      },
    );
  }

  Widget _buildTextFieldPost() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      height: 34,
      child: TextField(
        readOnly: true,
        decoration: InputDecoration(
          contentPadding:
          EdgeInsets.only(left: 14, right: 14, bottom: 10),
          suffixIcon: SizedBox(
            height: 20,
            width: 20,
            child: MaterialButton(
              onPressed: null,
              child: SvgPicture.asset(
                AppVectors.icEdit,
                color: AppColors.textBlack,
              ),
            ),
          ),
          hintText: 'Bạn muốn đăng gì nào?',
          hintStyle: AppTextStyle.blackS12Regular.copyWith(
            color: AppColors.grayIntro,
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColors.gray),
            borderRadius: BorderRadius.circular(20),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColors.main),
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        onTap: () {
          _handleCreatePost();
        },
      ),
    );
  }

  _onRefreshGroup() async {
    if (_tabController.index == MyPostProfileType.myPost.index) {
      _cubit.getAllMyPost();
    } else {
      _cubit.getAllSavePost();
    }
  }

  void _handleCreatePost() async {
    final result = await Get.to(CommunityCreatePostPage());
    if (result == null) return;
    if (result is bool) {
      if (result) {
        _onRefreshGroup();
      }
    }
  }
}
