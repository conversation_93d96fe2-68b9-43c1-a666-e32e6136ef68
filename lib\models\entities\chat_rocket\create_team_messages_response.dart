import 'package:json_annotation/json_annotation.dart';

import 'team_entity.dart';

part 'create_team_messages_response.g.dart';

@JsonSerializable()
class CreateTeamMessagesResponse {
  @JsonKey()
  TeamEntity? team;
  @JsonKey()
  bool? success;

  CreateTeamMessagesResponse({
    this.team,
    this.success,
  });

  factory CreateTeamMessagesResponse.fromJson(Map<String, dynamic> json) => _$CreateTeamMessagesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CreateTeamMessagesResponseToJson(this);
}
