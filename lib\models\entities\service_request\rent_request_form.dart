import 'package:json_annotation/json_annotation.dart';
import 'custom_sr_entity.dart';

part 'rent_request_form.g.dart';

@JsonSerializable()
class RentRequestForm {
  @Json<PERSON>ey()
  String? projectId;
  @<PERSON><PERSON><PERSON>ey()
  String? repoType;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? title;
  @<PERSON>son<PERSON>ey()
  String? description;
  @<PERSON>son<PERSON>ey()
  CustomSREntity? customData;

  @Json<PERSON>ey()
  String? name;
  @<PERSON>son<PERSON><PERSON>()
  String? email;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? phone;
  @Json<PERSON>ey()
  String? customerId;

  RentRequestForm(
      {this.projectId,
      this.repoType,
      this.title,
      this.description,
      this.customData,
      this.name,
      this.email,
      this.phone,
      this.customerId});

  RentRequestForm copyWidth(
      {String? projectId,
      String? repoType,
      String? title,
      String? description,
      CustomSREntity? customData,
      String? name,
      String? email,
      String? phone,
      String? customerId}) {
    return new RentRequestForm(
        projectId: projectId ?? this.projectId,
        repoType: repoType ?? this.repoType,
        title: title ?? this.title,
        description: description ?? this.description,
        customData: customData ?? this.customData,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        customerId: customerId ?? this.customerId);
  }

  factory RentRequestForm.fromJson(Map<String, dynamic> json) =>
      _$RentRequestFormFromJson(json);
  
  Map<String, dynamic> toJson() => _$RentRequestFormToJson(this);
}
