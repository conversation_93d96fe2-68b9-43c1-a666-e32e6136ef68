import 'package:real_care/generated/l10n.dart';

enum GenderType {
  MALE,
  FEMALE,
}

extension GenderTypeExtension on GenderType? {
  static GenderType? fromAPICode(int? code) {
    if (code == 1) {
      return GenderType.MALE;
    } else if (code == 0) {
      return GenderType.FEMALE;
    }
    return null;
  }

  // static GenderType? fromAPICode(String? text) {
  //   if (text == "male" || text == "Nam") {
  //     return GenderType.MALE;
  //   } else if (text == "female" || text == "Nữ") {
  //     return GenderType.FEMALE;
  //   }
  //   return null;
  // }

  String get displayText {
    switch (this) {
      case GenderType.MALE:
        return S.current.gender_male;
      case GenderType.FEMALE:
        return S.current.gender_female;
      default:
        return "";
    }
  }

  String get toAPICode {
    switch (this) {
      case GenderType.MALE:
        return "male";
      case GenderType.FEMALE:
        return "female";
      default:
        return "";
    }
  }
}
