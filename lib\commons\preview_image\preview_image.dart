import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:real_care/ui/components/app_page_widget.dart';
import 'package:photo_view/photo_view.dart';
import 'package:url_launcher/url_launcher.dart';

class PreviewImagePage extends StatelessWidget {
  final String urlImage;
  const PreviewImagePage({super.key, required this.urlImage});

  @override
  Widget build(BuildContext context) {
    return AppPageWidget(
      title: "Ảnh",
      actionWidget: actionWidget(context),
      child: PhotoView(
        imageProvider: NetworkImage(urlImage),
      ),
    );
  }

  Widget actionWidget(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final uri = Uri.parse(urlImage);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          Flushbar(
            icon: Icon(Icons.error, size: 32, color: Colors.white),
            shouldIconPulse: false,
            message: "<PERSON><PERSON> x<PERSON>y ra lỗi khi tải tệp",
            duration: Duration(seconds: 3),
            flushbarPosition: FlushbarPosition.BOTTOM,
            margin: EdgeInsets.fromLTRB(8, 2, 8, 0),
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ).show(context);
          throw 'Đã xảy ra lỗi khi tải tệp $urlImage';
        }
      },
      child: Container(
        color: Colors.transparent,
        width: 50,
        child: Icon(
          CupertinoIcons.arrow_down_circle,
          color: Colors.white,
          size: 26,
        ),
      ),
    );
  }
}