import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/app_page_widget.dart';
import 'package:real_care/ui/pages/check_in/user/health_declaration/health_declaration_cubit.dart';
import 'package:real_care/ui/pages/check_in/user/health_declaration/health_declaration_page2.dart';
import 'package:real_care/ui/widgets/app_label_date_picker.dart';
import 'package:real_care/ui/widgets/input/app_email_input.dart';
import 'package:real_care/ui/widgets/input/app_fullname_input.dart';
import 'package:real_care/ui/widgets/input/app_identify_number_input.dart';
import 'package:real_care/ui/widgets/input/app_phone_input.dart';
import 'package:real_care/ui/widgets/picker/app_gender_picker.dart';
import 'package:real_care/ui/widgets/textfield/app_label_text_field.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HealthDeclarationPage extends StatefulWidget {
  const HealthDeclarationPage({super.key});

  @override
  State<HealthDeclarationPage> createState() => _HealthDeclarationPageState();
}

class _HealthDeclarationPageState extends State<HealthDeclarationPage> {
  final TextEditingController _nameController = TextEditingController();
  final DatePickerController _birthDateController = DatePickerController(dateTime: null);
  final GenderPickerController _genderController = GenderPickerController();
  final TextEditingController _identityNumberController = TextEditingController();
  final TextEditingController _companyController = TextEditingController();
  final TextEditingController _divisionController = TextEditingController();
  final TextEditingController _teamController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailTController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  late HealthDeclarationCubit _cubit;
  late AppCubit _appCubit;

  @override
  void initState() {
    _cubit = HealthDeclarationCubit();
    _appCubit = BlocProvider.of<AppCubit>(context);
    _setUpData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppPageWidget(
        title: 'Khai báo thông tin y tế',
        visibleFooter: false,
        safeAreaBottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 27),
          child: Column(
            children: [
              SizedBox(height: 22),
              Text(
                'KHAI BÁO Y TẾ',
                style: AppTextStyle.blackS14Bold,
              ),
              SizedBox(height: 2),
              Text(
                '(Phòng chống dịch Covid-19)',
                style: AppTextStyle.blackS12,
              ),
              SizedBox(height: 6),
              Text(
                'Khuyến cáo: Khai báo thông tin sai là vi phạm pháp luật\nViệt Nam và có thể xử lý hình sự',
                style: AppTextStyle.redS12.copyWith(height: 1.4),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: Text(
                  'Thông tin cá nhân',
                  style: AppTextStyle.tintS14Bold,
                  textAlign: TextAlign.left,
                ),
              ),
              SizedBox(height: 15),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      AppFullNameInput(
                        enabled: false,
                        labelStyle: AppTextStyle.greyS12,
                        textStyle: AppTextStyle.blackS14,
                        textEditingController: _nameController,
                        onChanged: (text) {
                          _cubit.updateUserName(text);
                        },
                      ),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: AppLabelDatePicker(
                              enabled: false,
                              labelText: S.of(context).profile_date_birth,
                              labelStyle: AppTextStyle.greyS12,
                              textStyle: AppTextStyle.blackS14,
                              controller: _birthDateController,
                              minTime: AppConfig.birthMinDate,
                              maxTime: AppConfig.birthMaxDate,
                              onChanged: (date) {
                                _cubit.updateBirthDay(date);
                              },
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.only(left: 40, right: 5),
                              child: AppGenderPicker(
                                enabled: false,
                                controller: _genderController,
                                onChanged: (value) {
                                  _cubit.updateGender(value!);
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      AppIdentifyNumberInput(
                        enabled: false,
                        labelText: S.of(context).profile_identities,
                        labelStyle: AppTextStyle.greyS12,
                        textStyle: AppTextStyle.blackS14,
                        textEditingController: _identityNumberController,
                        onChanged: (text) {
                          _cubit.updateIdentityNumber(text);
                        },
                      ),
                      AppLabelTextField(
                        enabled: false,
                        textEditingController: _companyController,
                        labelText: 'Công ty làm việc',
                        labelStyle: AppTextStyle.blackS12.copyWith(color: AppColors.grayIntro),
                        textStyle: AppTextStyle.blackS14,
                        onChanged: (value) {
                          _cubit.updateCompany(value);
                        },
                      ),
                      AppLabelTextField(
                        enabled: false,
                        textEditingController: _divisionController,
                        labelText: 'Khối',
                        labelStyle: AppTextStyle.blackS12.copyWith(color: AppColors.grayIntro),
                        textStyle: AppTextStyle.blackS14,
                        onChanged: (value) {
                          _cubit.updateBloc(value);
                        },
                      ),
                      AppLabelTextField(
                        enabled: false,
                        textEditingController: _teamController,
                        labelText: 'Phòng ban',
                        labelStyle: AppTextStyle.blackS12.copyWith(color: AppColors.grayIntro),
                        textStyle: AppTextStyle.blackS14,
                        onChanged: (value) {
                          _cubit.updateDepartment(value);
                        },
                      ),
                      AppPhoneInput(
                        enabled: false,
                        textEditingController: _phoneController,
                        labelStyle: AppTextStyle.greyS12,
                        textStyle: AppTextStyle.blackS14,
                        onChanged: (text) {
                          _cubit.updateUserPhone(text);
                        },
                      ),
                      AppEmailInput(
                        enabled: false,
                        textEditingController: _emailTController,
                        labelStyle: AppTextStyle.greyS12,
                        textStyle: AppTextStyle.blackS14,
                        onChanged: (text) {
                          _cubit.updateUserEmail(text);
                        },
                      ),
                      AppLabelTextField(
                        enabled: false,
                        textEditingController: _addressController,
                        labelText: S.of(context).ycgcDetail_address,
                        labelStyle: AppTextStyle.blackS12.copyWith(color: AppColors.grayIntro),
                        textStyle: AppTextStyle.blackS14,
                        maxLength: 256,
                        onChanged: (value) {
                          _cubit.updateAddress(value);
                        },
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 10),
              buildNextButton(),
              SizedBox(height: 26),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildNextButton() {
    return AppTintButton(
      onPressed: () async {
        FocusScope.of(context).requestFocus(FocusNode());
        _navigateToHealthDeclarationPage2();
      },
      title: S.of(context).button_continue,
    );
    // return BlocBuilder<HealthDeclarationCubit, HealthDeclarationState>(
    //   cubit: _cubit,
    //   builder: (context, state) {
    //     return AppTintButton(
    //       onPressed: () async {
    //         FocusScope.of(context).requestFocus(FocusNode());
    //         _navigateToHealthDeclarationPage2();
    //       },
    //       title: S
    //           .of(context)
    //           .button_continue,
    //       isEnable: state.isValidData,
    //     );
    //   },
    // );
  }

  void _navigateToHealthDeclarationPage2() async {
    final result = await Navigator.of(context).push(MaterialPageRoute(builder: (context) => HealthDeclarationPage2()));
    if (result != null && result == "toMyQRCode") {
      Navigator.of(context).pop("toMyQRCode");
    }
  }

  void _setUpData() {
    final userData = _appCubit.state.user;
    _nameController.text = userData?.name ?? '';
    // _birthDateController.date = userData?.birthday;
    _genderController.gender = GenderTypeExtension.fromAPICode(userData?.gender);
    // _identityNumberController.text = (userData?.identities ?? []).isNotEmpty ? userData!.identities![0].value ?? "" : "";
    // _companyController.text = userData?.company ?? '';
    // _divisionController.text = userData?.division ?? '';
    // _teamController.text = userData?.team ?? '';
    // _phoneController.text = userData?.phone ?? '';
    // _emailTController.text = userData?.email ?? '';
    // _addressController.text = userData?.address ?? '';  // [Fix]: Fix later
  }
}
