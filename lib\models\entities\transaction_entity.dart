import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transaction_entity.g.dart';

@JsonSerializable()
class TransactionEntity {
  @Json<PERSON>ey()
  String? id;
  @J<PERSON><PERSON>ey()
  dynamic amount;
  @J<PERSON><PERSON>ey()
  String? code;
  @Json<PERSON>ey()
  String? status;
  @JsonKey()
  String? type;
  @Json<PERSON>ey()
  String? receiptNum;
  @JsonKey()
  String? receiptDate;
  @Json<PERSON>ey()
  String? state;

  DateTime? get receiptDateTime {
    return DateUtils.fromString(receiptDate);
  }

  int? get amountValue {
    if (amount is int) return amount;
    if (amount is num) return (amount as num).toInt();
    return int.tryParse(amount);
  }

  TransactionEntity(
      {this.id,
      this.amount,
      this.code,
      this.status,
      this.type,
      this.receiptNum,
      this.receiptDate,
      this.state
    });

  factory TransactionEntity.fromJson(Map<String, dynamic> json) => _$TransactionEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TransactionEntityToJson(this);
}
