import 'package:json_annotation/json_annotation.dart';

part 'user_chat_status_entity.g.dart';

@JsonSerializable()
class UserChatStatusEntity {
  @Json<PERSON>ey(name: 'users')
  List<UserInfo>? users;

  @J<PERSON><PERSON><PERSON>(name: 'full')
  bool? full;

  @Json<PERSON>ey(name: 'success')
  bool? success;

  UserChatStatusEntity({
    this.users,
    this.full,
    this.success,
  });

  factory UserChatStatusEntity.fromJson(Map<String, dynamic> json) => _$UserChatStatusEntityFromJson(json);

  Map<String, dynamic> toJson() => _$UserChatStatusEntityToJson(this);
}

@JsonSerializable()
class UserInfo {
  @JsonKey(name: '_id')
  String? uId;

  @JsonKey(name: 'status')
  String? status;

  @JsonKey(name: 'name')
  String? name;

  @Json<PERSON>ey(name: 'utcOffset')
  int? utcOffset;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'username')
  String? username;

  @J<PERSON><PERSON><PERSON>(name: 'outside')
  bool? outside;

  UserInfo({
    this.uId,
    this.status,
    this.name,
    this.utcOffset,
    this.username,
    this.outside,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}
