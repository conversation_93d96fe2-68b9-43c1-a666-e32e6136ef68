import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/md_entity.dart';
import 'package:real_care/models/entities/chat_rocket/message_attachments_entity.dart';
import 'package:real_care/models/entities/chat_rocket/room_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'last_message_entity.g.dart';

@JsonSerializable()
class LastMessageEntity extends Equatable {
  @Json<PERSON><PERSON>(name: "_id")
  final String? id;

  @Json<PERSON>ey(name: 'rid')
  final  String? rid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'msg')
  final String? msg;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'ts')
  final String? ts;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'u')
  final UEntity? u;

  @Json<PERSON><PERSON>(name: 'urls')
  final List<LastMessageUrl>? urls;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'mentions')
  final List<dynamic>? mentions;

  @Json<PERSON>ey(name: 'channels')
  final List<dynamic>? channels;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'md')
  final List<MdEntity>? md;

  @J<PERSON><PERSON><PERSON>(name: 'sUpdatedAt')
  final String? sUpdatedAt;

  @J<PERSON><PERSON><PERSON>(name: 'attachments')
  final List<MessageAttachmentsEntity>? attachments;

  @JsonKey(name: 'file')
  final MessageFileEntity? file;

  @JsonKey(name: 'groupable')
  final bool? groupable;

  LastMessageEntity({
    this.id,
    this.rid,
    this.msg,
    this.ts,
    this.urls,
    this.mentions,
    this.channels,
    this.md,
    this.attachments,
    this.file,
    this.groupable,
    this.sUpdatedAt,
    this.u,
  });

  factory LastMessageEntity.fromJson(Map<String, dynamic> json) => _$LastMessageEntityFromJson(json);

  Map<String, dynamic> toJson() => _$LastMessageEntityToJson(this);

  @override
  List<Object?> get props => [
        id,
        rid,
        msg,
        ts,
        urls,
        mentions,
        channels,
        md,
      ];

  DateTime? get timeSend {
    try {
      return DateTime.parse(ts ?? DateTime.now().toString()).toLocal();
    } catch (e) {
      return null;
    }
  }
}

@JsonSerializable()
class MessageFileEntity extends Equatable {
  @JsonKey(name: "_id")
  final String? fId;

  @JsonKey(name: 'name')
  final String? name;

  @JsonKey(name: 'type')
  final String? type;

  MessageFileEntity({
    this.fId,
    this.name,
    this.type,
  });

  factory MessageFileEntity.fromJson(Map<String, dynamic> json) => _$MessageFileEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MessageFileEntityToJson(this);

  @override
  List<Object?> get props => [
        fId,
        name,
        type,
      ];
}

@JsonSerializable()
class LastMessageUrl extends Equatable {
  @JsonKey(name: "url")
  final String? url;

  @JsonKey(name: 'ignoreParse', includeFromJson: false, includeToJson: false)
  final bool? ignoreParse;

  LastMessageUrl({
    this.url,
    this.ignoreParse,
  });

  factory LastMessageUrl.fromJson(Map<String, dynamic> json) => _$LastMessageUrlFromJson(json);

  Map<String, dynamic> toJson() => _$LastMessageUrlToJson(this);

  @override
  List<Object?> get props => [
        url,
        ignoreParse,
      ];
}
