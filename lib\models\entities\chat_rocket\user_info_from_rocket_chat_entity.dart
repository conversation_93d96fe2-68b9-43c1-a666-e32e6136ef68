import 'package:json_annotation/json_annotation.dart';

part 'user_info_from_rocket_chat_entity.g.dart';

@JsonSerializable()
class UserInfoFromRocketChatEntity {
  @Json<PERSON>ey()
  String? teamId;

  @J<PERSON><PERSON>ey()
  String? personalAccessToken;

  @J<PERSON><PERSON>ey()
  String? userId;

  @J<PERSON><PERSON>ey()
  String? username;

  @Json<PERSON>ey()
  String? password;

  @JsonKey()
  String? authToken;

  UserInfoFromRocketChatEntity({
    this.teamId,
    this.personalAccessToken,
    this.userId,
    this.username,
    this.password,
    this.authToken,
  });

  factory UserInfoFromRocketChatEntity.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromRocketChatEntityFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoFromRocketChatEntityToJson(this);
}
