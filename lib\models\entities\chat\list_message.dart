import 'package:real_care/models/entities/chat/message_chat.dart';

class ListMessage {
  List<Message>? messages;
  int? count;
  int? offset;
  int? total;
  bool? success;

  ListMessage({this.messages, this.count, this.offset, this.total, this.success});

  ListMessage.fromJson(Map<String, dynamic> json) {
    if (json['messages'] != null) {
      messages = <Message>[];
      json['messages'].forEach((v) {
        messages!.add(Message.fromJson(v));
      });
    }
    count = json['count'];
    offset = json['offset'];
    total = json['total'];
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (messages != null) {
      data['messages'] = messages?.map((v) => v.toJson()).toList();
    }
    data['count'] = count;
    data['offset'] = offset;
    data['total'] = total;
    data['success'] = success;
    return data;
  }
}