import 'dart:async';

import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/blocs/dynamic_config/dynamic_config_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/commons/screen_size.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/main.dart';
import 'package:real_care/models/entities/check_in/building_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/entities/promotion_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/repositories/auth_repository.dart';
import 'package:real_care/repositories/news_repository.dart';
import 'package:real_care/repositories/project_repository.dart';
import 'package:real_care/repositories/promotion_repository.dart';
import 'package:real_care/repositories/property_repository.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/dialogs/project_dialog.dart';
import 'package:real_care/ui/pages/check_in/scan_qr/scan_qr_page.dart';
import 'package:real_care/ui/pages/check_in/user/history/check_in_history_screen.dart';
import 'package:real_care/ui/pages/check_in/user/my_qr_code/check_in_qr_code_screen.dart';
import 'package:real_care/ui/pages/home/<USER>';
import 'package:real_care/ui/pages/home/<USER>';
import 'package:real_care/ui/pages/home/<USER>/item_banner/item_banner_widget.dart';
import 'package:real_care/ui/pages/home/<USER>/item_menu/item_menu_widget.dart';
import 'package:real_care/ui/pages/home/<USER>/item_promotion/item_promotion_widget.dart';
import 'package:real_care/ui/pages/home/<USER>/project_sale/item_project_sale_widget.dart';
import 'package:real_care/ui/pages/home/<USER>/shimmer_list/banner/shimmer_list_banner.dart';
import 'package:real_care/ui/pages/home/<USER>/shimmer_list/promotion/shimmer_list_promotion.dart';
import 'package:real_care/ui/pages/home/<USER>/shimmer_list/shimmer_list_home.dart';
import 'package:real_care/ui/pages/profile/request_verify_account/request_verify_account_page.dart';
import 'package:real_care/ui/pages/project_detail/loans/loans_list/loans_list_page.dart';
import 'package:real_care/ui/pages/project_detail/project_detail_page.dart';
import 'package:real_care/ui/pages/projects/projects_page.dart';
import 'package:real_care/utils/utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:real_care/commons/app_images.dart';

import 'home_menu.dart';
import 'widgets/home_util_menu.dart';
import "widgets/hot_news.dart";

class HomePage extends StatefulWidget {
  HomePage({Key? key}) : super(key: key);

  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  List<HomeMenu> _listMenu = [];
  HomeCubit? _cubit;
  final controller = PageController();
  final _scrollController = ScrollController();
  AppCubit? _appCubit;
  DynamicConfigCubit? _dynamicConfigCubit;

  @override
  void initState() {
    final projectRepository = RepositoryProvider.of<ProjectRepository>(context);
    final promotionRepository =
        RepositoryProvider.of<PromotionRepository>(context);
    final authRepository = RepositoryProvider.of<AuthRepository>(context);
    final newsRepository = RepositoryProvider.of<NewsRepository>(context);
    final propertyRepository =
        RepositoryProvider.of<PropertyRepository>(context);

    _cubit = HomeCubit(
        projectRepository: projectRepository,
        promotionRepository: promotionRepository,
        authRepository: authRepository,
        newsRepository: newsRepository,
        propertyRepository: propertyRepository);
    _cubit!.fetchList();

    _appCubit = BlocProvider.of<AppCubit>(context);

    if (_appCubit?.state.isLoggedIn ?? false) {
      _appCubit!.getNotificationCountUnread();
      _cubit!.checkHasProperty();
      _cubit!.getListBuilding();
    }
    _initMenuItems();

    // DataFlow: Step 8
    // Fetch data
    _dynamicConfigCubit = BlocProvider.of<DynamicConfigCubit>(context);
    _dynamicConfigCubit?.getDynamicConfig(_appCubit?.state.isLoggedIn ?? false);

    super.initState();
  }

  void scrollToTop() {
    _scrollController.animateTo(0,
        duration: Duration(milliseconds: 300), curve: Curves.easeOut);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  _initMenuItems() {
    _listMenu = [];

    _listMenu.add(HomeMenu.requestManagement);

    if (_appCubit?.state.user != null) {
      _listMenu.add(HomeMenu.util);
    }

    _listMenu.add(HomeMenu.news);
    _listMenu.add(HomeMenu.promotion);
    _listMenu.add(HomeMenu.healthDeclare);
    _listMenu.add(HomeMenu.healthQRCode);
    _listMenu.add(HomeMenu.checkInBooking);

    // [Fix]: Fix later
    // if ((_appCubit?.state.user?.permissions?.length ?? 0) != 0) {
    //   if (_appCubit!.state.user!.permissions!
    //       .contains(AppConfig.permissionsSecurity)) {
    //     _listMenu.add(HomeMenu.scanQRCode);
    //   }
    // }
  }

  Widget _pageView(List<PromotionEntity> listBanner) {
    return PageView.builder(
      itemCount: listBanner.length,
      controller: controller,
      itemBuilder: (BuildContext context, int index) {
        return ItemBanner(
          imageAssets: listBanner[index].image?.bannerImage ?? "",
          onPressed: () {
            Application.router!.navigateTo(
              appNavigatorKey.currentContext!,
              Routes.promotionDetail,
              routeSettings: RouteSettings(
                arguments: listBanner[index].id,
              ),
            );
          },
        );
      },
    );
  }

  Widget _smoothPage(List<PromotionEntity> listBanner) {
    return Container(
      alignment: Alignment.bottomCenter,
      padding: EdgeInsets.only(bottom: 5),
      child: SmoothPageIndicator(
        controller: controller,
        count: listBanner.length,
        effect: ExpandingDotsEffect(
          dotWidth: 6,
          dotHeight: 6,
          expansionFactor: 2.2,
          dotColor: Colors.white,
          activeDotColor: AppColors.textBlack.withOpacity(0.5),
        ),
      ),
    );
  }

  Future<bool> onRefresh() async {
    if (_appCubit?.state.isLoggedIn ?? false) {
      _appCubit!.getNotificationCountUnread();
      _cubit!.checkHasProperty();
    }
    _dynamicConfigCubit?.getDynamicConfig(_appCubit?.state.isLoggedIn ?? false);
    _cubit!.fetchList(); //TODO
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: RefreshIndicator(
          onRefresh: () {
            return this.onRefresh();
          },
          child: Container(
            margin: EdgeInsets.only(top: 10),
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  BlocListener<AppCubit, AppState>(
                    bloc: _appCubit,
                    listenWhen: (prev, current) {
                      return prev.isLoggedIn != current.isLoggedIn;
                    },
                    listener: (context, state) {
                      _cubit!.fetchList();
                      if (_appCubit?.state.isLoggedIn ?? false) {
                        _appCubit!.getNotificationCountUnread();
                        _cubit!.checkHasProperty();
                        _cubit!.getListBuilding();
                      }

                      _dynamicConfigCubit?.getDynamicConfig(
                          _appCubit?.state.isLoggedIn ?? false);
                    },
                    child: Container(),
                  ),
                  _listMenuDisplay(),
                  SizedBox(
                    height: 5,
                  ),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(20))),
                    padding: EdgeInsets.only(top: 10),
                    child: Column(
                      children: [
                        _listBanner(),
                        SizedBox(height: 20),
                        BlocBuilder<AppCubit, AppState>(
                            bloc: _appCubit,
                            buildWhen: (prev, current) {
                              return prev.isLoggedIn != current.isLoggedIn;
                            },
                            builder: (context, state) {
                              return BlocBuilder<HomeCubit, HomeState>(
                                  bloc: _cubit,
                                  buildWhen: (prev, current) {
                                    return prev.hasProperty !=
                                        current.hasProperty;
                                  },
                                  builder: (hContext, hState) {
                                    if (state.isLoggedIn &&
                                        hState.hasProperty) {
                                      return HomeUtilMenu(cubit: _cubit);
                                    }

                                    return Center();
                                  });
                            }),
                        _listPromotionSection(),
                        Container(
                          height: 20,
                          color: Colors.transparent,
                        ),
                        _listProjectInprogress(),
                        Container(
                          height: 10,
                        ),
                        _listProjectComing(),
                        SizedBox(
                          height: 25,
                        ),
                        HotNews(cubit: _cubit, controller: controller),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        floatingActionButton: Container(
          constraints: BoxConstraints(
              minWidth: ScreenSize.of(context).width! * 0.2,
              minHeight: ScreenSize.of(context).width! * 0.2),
          child: FittedBox(
            child: FloatingActionButton(
              onPressed: () {},
              elevation: 0.0,
              child: Image.asset(
                AppImages.icChatbot,
              ),
              backgroundColor: Colors.transparent,
            ),
          ),
        ));
  }

  Widget _listPromotionSection() {
    return BlocBuilder<HomeCubit, HomeState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.loadStatusPromotion != current.loadStatusPromotion;
        },
        builder: (context, state) {
          if (state.loadStatusPromotion == LoadStatus.LOADING) {
            return Container(
              height: 80,
              padding: EdgeInsets.only(left: 12, right: 12),
              child: ShimmerListPromotion(
                width: MediaQuery.of(context).size.width / 2.2,
              ),
            );
          } else if (state.loadStatusPromotion == LoadStatus.FAILURE) {
            return Container(
              height: 120,
              child: _noDataResult(true),
            );
          } else {
            return (state.listPromotion?.length ?? 0) != 0
                ? Padding(
                    padding: EdgeInsets.only(left: 12),
                    child: Container(
                      height: 80,
                      alignment: Alignment.topLeft,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: state.listPromotion?.length ?? 0,
                        shrinkWrap: true,
                        primary: false,
                        itemBuilder: (context, index) {
                          PromotionEntity promotion =
                              state.listPromotion![index];
                          return ItemPromotionSection(
                            imageAssets: state
                                    .listPromotion![index].image?.bannerImage ??
                                "",
                            widthItem: MediaQuery.of(context).size.width / 2.2,
                            onPressed: () {
                              _openPromotionDetail(promotion);
                            },
                          );
                        },
                      ),
                    ),
                  )
                : Container(
                    height: 120,
                    child: _noDataResult(false),
                  );
          }
        });
  }

  Widget _listBanner() {
    return BlocBuilder<HomeCubit, HomeState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.loadStatusBanner != current.loadStatusBanner;
        },
        builder: (context, state) {
          if (state.loadStatusBanner == LoadStatus.LOADING) {
            return Container(
              height: 120,
              padding: EdgeInsets.symmetric(
                horizontal: 6,
              ),
              child: ShimmerListBanner(
                width: MediaQuery.of(context).size.width - 24,
              ),
            );
          } else if (state.loadStatusBanner == LoadStatus.FAILURE) {
            return Container(
              height: 120,
              child: _noDataResult(true),
            );
          }
          return (state.listBanner?.length ?? 0) != 0
              ? Container(
                  height: 120,
                  child: Stack(
                    children: [
                      _pageView(state.listBanner ?? []),
                      _smoothPage(state.listBanner ?? []),
                    ],
                  ),
                )
              : Container(
                  height: 120,
                  child: _noDataResult(false),
                );
        });
  }

  Widget _textShimmer(String textleft) {
    return Padding(
      padding: EdgeInsets.only(
        left: 10,
        right: 10,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            textleft,
            style: AppTextStyle.blackS16Bold,
          ),
        ],
      ),
    );
  }

  Widget _listProjectComing() {
    return BlocBuilder<HomeCubit, HomeState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.statusProjectsComing != current.statusProjectsComing;
        },
        builder: (context, state) {
          if (state.statusProjectsComing == LoadStatus.LOADING) {
            return Column(
              children: [
                _textShimmer(S.current.home_project_sale),
                SizedBox(
                  height: 5,
                ),
                Container(
                  height: 210,
                  color: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.only(left: 12),
                    child: ShimmerList(),
                  ),
                ),
              ],
            );
          } else if (state.statusProjectsComing == LoadStatus.FAILURE) {
            return Column(children: [
              _titleList(
                  S.current.home_project_handed_over, "HANDED_OVER", false),
              SizedBox(
                height: 5,
              ),
              Container(
                height: 100,
                child: _noDataResult(false),
              )
            ]);
          } else {
            return (state.listProjectsComing?.length ?? 0) != 0
                ? Column(
                    children: [
                      _titleList(
                          S.current.home_project_handed_over,
                          "HANDED_OVER",
                          (state.listProjectsComing?.length ?? 0) > 5
                              ? true
                              : false),
                      SizedBox(
                        height: 5,
                      ),
                      Container(
                        height: 210,
                        child: Padding(
                          padding: EdgeInsets.only(left: 10),
                          child: Container(
                            alignment: Alignment.topLeft,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount:
                                  (state.listProjectsComing?.length ?? 0) <= 5
                                      ? (state.listProjectsComing?.length ?? 0)
                                      : 5,
                              shrinkWrap: true,
                              primary: false,
                              itemBuilder: (context, index) {
                                final projectItem =
                                    state.listProjectsComing![index];
                                return ItemProjectWidget(
                                  projectEntity: projectItem,
                                  index: index,
                                  onPressed: () {
                                    _showDialog(project: projectItem);
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : Container(height: 210, child: _noDataResult(false));
          }
        });
  }

  Widget _listProjectInprogress() {
    return BlocBuilder<HomeCubit, HomeState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.statusProjectSale != current.statusProjectSale;
        },
        builder: (context, state) {
          if (state.statusProjectSale == LoadStatus.LOADING) {
            return Column(
              children: [
                _textShimmer(S.current.home_project_sale),
                SizedBox(
                  height: 5,
                ),
                Container(
                  height: 210,
                  color: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.only(left: 12),
                    child: ShimmerList(),
                  ),
                ),
              ],
            );
          } else if (state.statusProjectSale == LoadStatus.FAILURE) {
            return Column(children: [
              _titleList(S.current.home_project_sale, "INPROGRESS", false),
              SizedBox(
                height: 5,
              ),
              Container(
                height: 100,
                child: _noDataResult(false),
              )
            ]);
          } else {
            return (state.listProjectSale?.length ?? 0) != 0
                ? Column(
                    children: [
                      _titleList(
                          S.current.home_deploying_plan,
                          "INPROGRESS",
                          (state.listProjectSale?.length ?? 0) > 5
                              ? true
                              : false),
                      SizedBox(
                        height: 5,
                      ),
                      Container(
                        height: 210,
                        child: Padding(
                          padding: EdgeInsets.only(left: 10),
                          child: Container(
                            alignment: Alignment.topLeft,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount:
                                  (state.listProjectSale?.length ?? 0) <= 5
                                      ? (state.listProjectSale?.length ?? 0)
                                      : 5,
                              shrinkWrap: true,
                              primary: false,
                              itemBuilder: (context, index) {
                                return ItemProjectWidget(
                                  projectEntity: state.listProjectSale![index],
                                  index: index,
                                  onPressed: () {
                                    _showDialog(
                                        project: state.listProjectSale![index]);
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : Container(
                    height: 210,
                    child: _noDataResult(false),
                  );
          }
        });
  }

  Widget _titleList(String textleft, String status, bool isTextRight) {
    return Padding(
      padding: EdgeInsets.only(
        left: 10,
        right: 10,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            textleft,
            style: AppTextStyle.blackS16Bold,
          ),
          isTextRight
              ? GestureDetector(
                  onTap: () {
                    Application.router!.navigateTo(
                      context,
                      Routes.expandHome,
                      routeSettings: RouteSettings(
                        arguments: ExpandArgument(
                          type: ProjectsTypeExtension.getProjectsTypeExtension(
                              status),
                        ),
                      ),
                    );
                  },
                  child: Container(
                    color: Colors.transparent,
                    child: Text(
                      S.current.common_seeMore,
                      style: AppTextStyle.orangeS14,
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget _listMenuDisplay() {
    return BlocConsumer<AppCubit, AppState>(
        bloc: _appCubit,
        listenWhen: (prev, current) {
          return prev.isLoggedIn != current.isLoggedIn;
        },
        listener: (context, state) {
          _initMenuItems();
        },
        buildWhen: (prev, current) {
          return prev.isLoggedIn != current.isLoggedIn;
        },
        builder: (context, state) {
          final screenWidth = ScreenSize.of(context).width!;
          return Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              margin: EdgeInsets.all(12),
              shadowColor: Colors.grey,
              elevation: 10,
              child: Container(
                padding: EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
                child: GridView.count(
                  physics: NeverScrollableScrollPhysics(),
                  crossAxisCount: screenWidth > 350 ? 4 : 3,
                  shrinkWrap: true,
                  padding: EdgeInsets.all(0),
                  children: List.generate(_listMenu.length, (index) {
                    return ItemMenu(
                      menu: _listMenu[index],
                      index: index,
                      onPressed: () {
                        handleMenuPressed(_listMenu[index]);
                      },
                    );
                  }),
                ),
              ));
        });
  }

  void handleMenuPressed(HomeMenu menu) {
    switch (menu) {
      case HomeMenu.requestManagement:
        _openServiceManagementPage();
        break;
      case HomeMenu.news:
        Application.router?.navigateTo(context, Routes.newsMain);
        break;
      case HomeMenu.util:
        Application.router!.navigateTo(context, Routes.utilMain);
        break;
      case HomeMenu.promotion:
        _openPromotionPage();
        break;
      case HomeMenu.healthDeclare:
        _openCheckInHistoryScreen();
        break;
      case HomeMenu.healthQRCode:
        _openMyQRCodeScreen();
        break;
      case HomeMenu.scanQRCode:
        _scanQrPage();
        break;
      default:
        break;
    }
  }

  navigateQrPage(BuildingEntity model) async {
    PermissionStatus status = await Permission.camera.status;
    if (status.isDenied) {
      PermissionStatus newStatus = await Permission.camera.request();
      if (newStatus.isGranted) {
        await Navigator.push(
          appNavigatorKey.currentContext!,
          MaterialPageRoute(
            builder: (context) => ScanQRCodePage(
              buildingId: model.id,
              buildingName: model.buildingInfo?.name ?? '',
              address: model.buildingInfo?.address ?? '',
            ),
          ),
        );
      }
    } else {
      await Navigator.push(
        appNavigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => ScanQRCodePage(
            buildingId: model.id,
            buildingName: model.buildingInfo?.name ?? '',
            address: model.buildingInfo?.address ?? '',
          ),
        ),
      );
    }
  }

  _scanQrPage() async {
    if (_appCubit!.state.isLoggedIn) {
      if ((_cubit!.state.listBuilding?.length ?? 0) == 1) {
        navigateQrPage(_cubit!.state.listBuilding![0]);
      } else {
        // final result = await DialogUtils.showBottomBuildingPicker(
        //   context,
        //   title: "Chọn toà nhà làm việc",
        // );
        // if (result != null) {
        //   navigateQrPage(result);
        // }
      }
    } else {
      showSignIn();
    }
  }

  ///Navigate
  void showSignIn() async {
    Application.router!
        .navigateTo(appNavigatorKey.currentContext!, Routes.signIn);
  }

  Widget _noDataResult(bool isFailure) {
    return Center(
      child: Text(
        isFailure
            ? S.of(context).error_occurred
            : S.of(context).no_data_show, //TODO
        textAlign: TextAlign.center,
      ),
    );
  }

  void _showDialog({required ProjectEntity? project}) {
    showDialog(
      useRootNavigator: true,
      barrierDismissible: true,
      context: context,
      builder: (BuildContext context) => ProjectDialog(
        onDetailPressed: () {
          Navigator.of(context).pop();
          _openDetailProject(project: project);
        },
        onCallPressed: () {
          Utils.launchPhoneCall(phone: project?.careInvestor?.phone ?? "");
        },
        onBackPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _openDetailProject({required ProjectEntity? project}) {
    Application.router!.navigateTo(
      appNavigatorKey.currentContext!,
      Routes.detailProject,
      routeSettings: RouteSettings(
        arguments: ProjectDetailArgument(
          projectId: project?.id ?? "",
        ),
      ),
    );
  }

  void _openPromotionDetail(PromotionEntity promotion) {
    Application.router!.navigateTo(
      appNavigatorKey.currentContext!,
      Routes.promotionDetail,
      routeSettings: RouteSettings(
        arguments: promotion.id,
      ),
    );
  }

  void _openPromotionPage() {
    Application.router!.navigateTo(context, Routes.promotion);
  }

  void _openServiceManagementPage() {
    if (_appCubit!.state.isLoggedIn) {
      // if (_appCubit!.state.verifyAccountStatus !=
      //     VerifyAccountStatus.APPROVED) {
      //   _requestVerifyAccount();
      //   return;
      // } // [Fix]: Fix later
      Application.router?.navigateTo(context, Routes.requestManagement);
    } else {
      Application.router!.navigateTo(
          appNavigatorKey.currentContext!, Routes.advisoryCreate,
          routeSettings: RouteSettings(
              arguments: ProjectLoanDetailArgument(
                  projectId: "", projectName: "", projectImageUrl: "")));
    }
  }

  void _requestVerifyAccount() async {
    await Navigator.push(
      appNavigatorKey.currentContext!,
      MaterialPageRoute(
        builder: (context) => RequestVerifyAccountPage(),
      ),
    );
    _appCubit!.getProfile();
  }

  void _openCheckInHistoryScreen() async {
    if (_appCubit!.state.isLoggedIn) {
      //  if (_cubit.state.isRequireHealthDeclaration) {
      final result = await Navigator.push(
        appNavigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => CheckInHistoryScreen(),
        ),
      );
      if (result != null && result == "toMyQRCode") {
        _openMyQRCodeScreen();
      }
      // } else {
      //   DetailDialogSignOut.showCustomDialog(
      //     context,
      //     DialogCheckInPage(
      //       title: "Không thể khai báo y tế",
      //       iconDialog: AppImages.icErrorHealth,
      //       content: '********** ',
      //       contentLeft:
      //           'Anh/chị không nằm trong danh sách được đến làm việc tại toà nhà DXO. Xin vui lòng liên hệ hotline ',
      //       contentRight: 'để được hỗ trợ trong trường hợp khẩn cấp.',
      //       contentLeftStyle: AppTextStyle.blackS14.copyWith(fontWeight: FontWeight.normal),
      //       contentStyle: AppTextStyle.blackS14Bold,
      //       contentRightStyle: AppTextStyle.blackS14.copyWith(fontWeight: FontWeight.normal),
      //       callback: () {
      //         _cubit.changeRequireHealthDeclaration();
      //       },
      //       padding: 40,
      //     ),
      //     true,
      //   );
      // }
    } else {
      showSignIn();
    }
  }

  void _openMyQRCodeScreen() {
    if (_appCubit!.state.isLoggedIn) {
      Navigator.push(
        appNavigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => CheckInQRCodeScreen(),
        ),
      );
    } else {
      showSignIn();
    }
  }

}

class ExpandArgument {
  ProjectsType? type;

  ExpandArgument({
    this.type,
  });
}
