import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/commons/screen_size.dart';
import 'package:real_care/generated/l10n.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/enums/verify_account_status.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/utils/dialog_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequestVerifyAccountPage extends StatefulWidget {
  @override
  _RequestVerifyAccountPageState createState() => _RequestVerifyAccountPageState();
}

class _RequestVerifyAccountPageState extends State<RequestVerifyAccountPage> {
  late AppCubit _appCubit;

  @override
  void initState() {
    _appCubit = BlocProvider.of<AppCubit>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(child: _buildBodyWidget()),
    );
  }

  Widget _buildBodyWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          height: 56,
          alignment: Alignment.centerRight,
          child: GestureDetector(
            child: Container(
              height: 56,
              width: 56,
              child: Image.asset(AppImages.icCloseCircleShadow),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
        ),
        Container(
          width: double.infinity,
          height: 320,
          child: Image.asset(AppImages.icRequestVerifyAccount),
        ),
        BlocBuilder<AppCubit, AppState>(
          buildWhen: (prev, current) {
            return prev.user != current.user;
          },
          builder: (context, state) {
            return _buildMessageWidget();
          },
        ),
        _buildNextButton(),
      ],
    );
  }

  Widget _buildMessageWidget() {
    final name = _appCubit.state.user?.name ?? "";
    // [Fix]: Fix later
    // final verifyingAccount = _appCubit.state.user?.verifyAccountStatus == VerifyAccountStatus.WAITING;
    // if (verifyingAccount) {
    //   return Container(
    //     margin: EdgeInsets.symmetric(horizontal: 30),
    //     child: RichText(
    //       text: TextSpan(
    //         children: [
    //           TextSpan(text: "Chào ", style: AppTextStyle.blackS14.copyWith(height: 1.7)),
    //           TextSpan(text: "$name", style: AppTextStyle.blackS14Bold.copyWith(height: 1.7)),
    //           TextSpan(text: ",\nTài khoản của bạn đang được xác thực, xin vui lòng chờ\n", style: AppTextStyle.blackS14.copyWith(height: 1.7)),
    //           TextSpan(text: "Xác thực tài khoản ", style: AppTextStyle.blackS14Bold.copyWith(height: 1.7)),
    //           TextSpan(text: "nhằm:\n - Tăng độ bảo mật cho tài khoản\n - Bảo mật tối đa tài sản của bạn", style: AppTextStyle.blackS14.copyWith(height: 1.7)),
    //         ],
    //       ),
    //     ),
    //   );
    // }
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 30),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(text: "Chào ", style: AppTextStyle.blackS14.copyWith(height: 1.7)),
            TextSpan(text: "$name", style: AppTextStyle.blackS14Bold.copyWith(height: 1.7)),
            TextSpan(text: ",\nĐể xem danh sách tài sản của bạn, vui lòng thực hiện ", style: AppTextStyle.blackS14.copyWith(height: 1.7)),
            TextSpan(text: "xác thực tài khoản ", style: AppTextStyle.blackS14Bold.copyWith(height: 1.7)),
            TextSpan(text: "nhằm:\n - Tăng độ bảo mật cho tài khoản\n - Bảo mật tối đa tài sản của bạn", style: AppTextStyle.blackS14.copyWith(height: 1.7)),
          ],
        ),
      ),
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<AppCubit, AppState>(
      buildWhen: (prev, current) {
        return prev.user != current.user || prev.fetchUser != current.fetchUser;
      },
      builder: (context, state) {
        // [Fix]: Fix later
        // final showButton = _appCubit.state.user?.verifyAccountStatus == VerifyAccountStatus.INIT || _appCubit.state.user?.verifyAccountStatus == VerifyAccountStatus.REJECTED;
        // if (!showButton) {
        //   return Container();
        // }
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 30),
          padding: EdgeInsets.only(bottom: ScreenSize.of(context).height * 6 / 68),
          child: AppTintButton(
            title: "Xác thực tài khoản",
            onPressed: () {
              _handleVerifyAccount();
            },
            isLoading: state.fetchUser == LoadStatus.LOADING,
          ),
        );
      },
    );
  }

  void _handleVerifyAccount() {
    // [Fix]: Fix later
    // if (_appCubit.state.user!.identity?.isValidInfo != true) {
    //   DialogUtils.showErrorDialog(
    //     context,
    //     title: S.of(context).requestVerifyAccount_noCCCDWarning,
    //     okText: "Thêm thông tin",
    //     dismissible: true,
    //     onOkPressed: () {
    //       _openEditProfilePage();
    //     },
    //   );
    // } else {
    //   _openVerifyPage();
    // }
  }

  void _openVerifyPage() async {
    final result = await Application.router!.navigateTo(context, Routes.verifyProfile);
    if (result is bool) {
      if (result == true) {
        _appCubit.getProfile();
      }
    }
  }

  void _openEditProfilePage() {
    Application.router!.navigateTo(context, Routes.updateProfile);
  }
}
