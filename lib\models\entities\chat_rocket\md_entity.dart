import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'md_entity.g.dart';

@JsonSerializable()
class MdEntity extends Equatable {
  @JsonKey()
  final String? type;
  @JsonKey()
  final List<MdItemEntity>? value;

  MdEntity({
    this.type,
    this.value,
  });

  factory MdEntity.fromJson(Map<String, dynamic> json) => _$MdEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MdEntityToJson(this);

  @override
  List<Object> get props => [
        this.type!,
        this.value!,
      ];
}

@JsonSerializable()
class MdItemEntity extends Equatable {
  @JsonKey()
  final String? type;
  @JsonKey()
  final dynamic value;

  MdItemEntity({
    this.type,
    this.value,
  });

  factory MdItemEntity.fromJson(Map<String, dynamic> json) => _$MdItemEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MdItemEntityToJson(this);

  @override
  List<Object> get props => [
        this.type!,
        this.value,
      ];
}
