import 'package:real_care/models/entities/group/group_user_entity.dart';

class UserProfileFromRocketChat {
  String? id;
  String? createdDate;
  String? name;
  String? modifiedBy;
  String? phone;
  String? email;
  String? status;
  String? avatar;
  RocketChat? rocketChat;
  GroupUserEntity? employeeInfo;

  UserProfileFromRocketChat({
    this.id,
    this.createdDate,
    this.name,
    this.modifiedBy,
    this.phone,
    this.email,
    this.status,
    this.avatar,
    this.rocketChat,
    this.employeeInfo,
  });

  UserProfileFromRocketChat.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdDate = json['createdDate'];
    name = json['name'];
    modifiedBy = json['modifiedBy'];
    phone = json['phone'];
    email = json['email'];
    status = json['status'];
    avatar = json['avatar'];
    rocketChat = json['rocketChat'] != null ? RocketChat.fromJson(json['rocketChat']) : null;
    employeeInfo = json['employeeInfo'] != null ? GroupUserEntity.fromJson(json['employeeInfo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['createdDate'] = this.createdDate;
    data['modifiedBy'] = this.modifiedBy;
    data['phone'] = this.phone;
    data['email'] = this.email;
    data['status'] = this.status;
    data['avatar'] = this.avatar;
    if (this.rocketChat != null) {
      data['rocketChat'] = this.rocketChat?.toJson();
    }
    return data;
  }

  String get avatarUrl {
    if ((employeeInfo?.avatarThumbnail ?? '').isNotEmpty) {
      return employeeInfo!.avatarThumbnail;
    } else {
      return avatar ?? '';
    }
  }
}

class RocketChat {
  String? username;
  String? userId;
  String? personalAccessToken;

  RocketChat({
    this.username,
    this.userId,
    this.personalAccessToken,
  });

  RocketChat.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    userId = json['userId'];
    personalAccessToken = json['personalAccessToken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['username'] = this.username;
    data['userId'] = this.userId;
    data['personalAccessToken'] = this.personalAccessToken;
    return data;
  }
}
