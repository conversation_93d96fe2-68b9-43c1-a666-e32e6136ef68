import 'package:flutter/material.dart';
import 'package:real_care/commons/app_shadow.dart';

import 'app_colors.dart';

class AppBoxes {
  static final boxBorderTop = BoxDecoration(
    boxShadow: AppShadow.boxShadowHideBottom,
    color: Colors.white,
    borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20)),
  );

  static final boxBorderItemGrey = BoxDecoration(
    borderRadius: BorderRadius.circular(5),
    border: Border.all(
      width: 1,
      color: AppColors.greyDBDBDB,
    ),
  );

  static final boxBorderItemOrange = BoxDecoration(
    borderRadius: BorderRadius.circular(10),
    border: Border.all(
      width: 1,
      color: AppColors.orange,
    ),
  );
}