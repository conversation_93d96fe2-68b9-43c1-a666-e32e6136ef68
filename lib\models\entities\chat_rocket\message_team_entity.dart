import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/chat_rocket/attachments_entity.dart';
import 'package:json_annotation/json_annotation.dart';

import 'chat_home_entity.dart';

part 'message_team_entity.g.dart';

@JsonSerializable()
class MessageTeamEntity extends Equatable {
  @Json<PERSON><PERSON>(name: "_id")
  final String? id;
  @Json<PERSON>ey()
  final String? rid;
  @JsonKey()
  final String? ts;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String? msg;
  @Json<PERSON>ey()
  final FileEntity? file;
  @<PERSON><PERSON><PERSON><PERSON>()
  final List<AttachmentsEntity>? attachments;
  @<PERSON><PERSON><PERSON><PERSON>()
  final bool? groupable;
  @J<PERSON><PERSON>ey()
  final ChatHomeEntity? u;
  @Json<PERSON>ey()
  final List<dynamic>? urls;
  @JsonKey()
  final List<dynamic>? mentions;
  @Json<PERSON>ey()
  final List<dynamic>? channels;
  @Json<PERSON>ey(name: "_updatedAt")
  final String? updatedAt;

  MessageTeamEntity({
    this.id,
    this.rid,
    this.ts,
    this.msg,
    this.file,
    this.groupable,
    this.u,
    this.urls,
    this.mentions,
    this.channels,
    this.updatedAt,
    this.attachments,
  });

  factory MessageTeamEntity.fromJson(Map<String, dynamic> json) => _$MessageTeamEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MessageTeamEntityToJson(this);

  @override
  List<Object?> get props => [
        this.id,
        this.rid,
        this.ts,
        this.msg,
        this.file,
        this.groupable,
        this.u,
        this.urls,
        this.mentions,
        this.channels,
        this.updatedAt,
        this.attachments,
      ];
}

@JsonSerializable()
class FileEntity extends Equatable {
  @JsonKey(name: "_id")
  final  String? id;
  @JsonKey()
  final String? name;
  @JsonKey()
  final String? type;

  FileEntity({
    this.id,
    this.name,
    this.type,
  });

  factory FileEntity.fromJson(Map<String, dynamic> json) => _$FileEntityFromJson(json);

  Map<String, dynamic> toJson() => _$FileEntityToJson(this);

  @override
  List<Object?> get props => [
        this.id,
        this.name,
        this.type,
      ];
}
