import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:real_care/database/share_preferences_helper.dart';
import 'package:real_care/models/entities/amenities/utility_entity.dart';
import 'package:real_care/models/entities/resident_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/models/params/reservation_param.dart';
import 'package:real_care/repositories/property_repository.dart';
import 'package:real_care/repositories/user_repository.dart';
import 'package:real_care/repositories/utility_respository.dart';
import 'package:real_care/ui/pages/utility/bbq_reservation/reservation_state.dart';
import 'package:real_care/ui/widgets/app_snackbar.dart';
import 'package:real_care/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';

class ReservationCubit extends Cubit<ReservationState> {
  UserRepository? userRepository;
  UtilityRepository? utilityRepository;
  PropertyRepository? propertyRepository;
  final showMessageController = PublishSubject<SnackBarMessage>();

  @override
  Future<void> close() {
    showMessageController.close();
    return super.close();
  }

  ReservationCubit(
      {this.userRepository, this.utilityRepository, this.propertyRepository})
      : super(ReservationState());

  void updateStep(int step) {
    emit(state.copyWith(currentStep: step));
  }

  void updateIdentityName(String name) {
    emit(
      state.copyWith(
        nameUser: name,
        param: state.param!.copyWith(name: name),
      ),
    );
  }

  void updateIdentityNumber(String number) {
    emit(
      state.copyWith(
        numberUser: number,
        param: state.param!.copyWith(phone: number),
      ),
    );
  }

  void updateIdentityEmail(String email) {
    emit(
      state.copyWith(
        emailUser: email,
        param: state.param!.copyWith(email: email),
      ),
    );
  }

  void updateApartmentCode(String code) {
    emit(
      state.copyWith(
        apartmentCodeUser: code,
        param: state.param!.copyWith(apartmentCode: code),
      ),
    );
  }

  void updateDateSelected(DateTime dateSelected) {
    String dateStringSubmit =
        "${DateFormat('EEE, dd MMM yyyy HH:mm:ss').format(dateSelected)} GMT";
    emit(
      state.copyWith(
        dateSelected: dateSelected,
        param: state.param!.copyWith(bookingDate: dateStringSubmit),
      ),
    );
  }

  void updateUtilityId(String utilityId) {
    emit(
      state.copyWith(
        utilityId: utilityId,
      ),
    );
  }

  void updateProjectId(String projectId) {
    emit(
      state.copyWith(
        projectId: projectId,
      ),
    );
  }

  void updateAreaId(String areaId) {
    emit(
      state.copyWith(
        areaId: areaId,
        param: state.param!.copyWith(areaId: areaId),
      ),
    );
  }

  void updateTime(String time) {
    emit(
      state.copyWith(
        time: time,
        param: state.param!.copyWith(time: time),
      ),
    );
  }

  void updateAreaCode(String areaCode) {
    emit(
      state.copyWith(
        areaCode: areaCode,
      ),
    );
  }

  void updateSeatId(String seatId) {
    emit(
      state.copyWith(
        seatId: seatId,
        param: state.param!.copyWith(seatId: seatId),
      ),
    );
  }

  void getProfile() async {
    emit(state.copyWith(
      param: new ReservationParam(),
    ));

    try {
      final userRes = await userRepository!.getProfile();
      if (userRes.id != null && userRes.id!.isNotEmpty) {
        emit(
          state.copyWith(
            userEntity: userRes,
            nameUser: userRes.name ?? "",
            numberUser: userRes.phone ?? "",
            emailUser: userRes.email ?? "",
            // apartmentCodeUser: userRes.buildingId ?? "", // [Fix]: Fix later
            param: state.param!.copyWith(
              name: userRes.name ?? "",
              phone: userRes.phone ?? "",
              email: userRes.email ?? "",
              utilityId: state.utilityId ?? "",
              projectId: state.projectId,
            ),
          ),
        );
      }
    } catch (error) {
      logger.e(error);
    }
  }

  void getUtilityDetail(String? utilityId, String? dateSelected) async {
    emit(state.copyWith(loadStatus: LoadStatus.LOADING));
    try {
      UtilityEntity? service = await utilityRepository!
          .getUtilityDetail(utilityId: utilityId, bookingDate: dateSelected);
      if (service != null) {
        emit(state.copyWith(
          loadStatus: LoadStatus.SUCCESS,
          utilityDetail: service,
          areas: service.areas,
        ));
      } else {
        emit(state.copyWith(loadStatus: LoadStatus.FAILURE));
      }
    } catch (e) {
      print(e);
      emit(state.copyWith(loadStatus: LoadStatus.FAILURE));
    }
  }

  void getAreaDetail(String? utilityId, String? dateSelected) async {
    emit(state.copyWith(loadStatus: LoadStatus.LOADING));
    AreasEntity area;
    try {
      UtilityEntity? service = await utilityRepository!
          .getUtilityDetail(utilityId: utilityId, bookingDate: dateSelected);
      area = service.areas!.where((e) => e.id == state.areaId).toList().first;
      if (service != null) {
        emit(
          state.copyWith(
            loadStatus: LoadStatus.SUCCESS,
            utilityDetail: service,
            areas: service.areas,
            seats: area.seats,
            areaName: area.name,
            diagram: area.diagram ?? "",
          ),
        );
      } else {
        emit(state.copyWith(loadStatus: LoadStatus.FAILURE));
      }
    } catch (e) {
      print(e);
      emit(state.copyWith(loadStatus: LoadStatus.FAILURE));
    }
  }

  void createBooking() async {
    emit(state.copyWith(requestCreate: LoadStatus.LOADING));
    try {
      final result = await utilityRepository!.createBooking(state.param);
      emit(state.copyWith(requestCreate: LoadStatus.SUCCESS));
    } catch (e) {
      emit(state.copyWith(requestCreate: LoadStatus.FAILURE));
      if (e is DioException) {
        if (e.response!.statusCode == 400) {
          showMessageController.sink.add(SnackBarMessage(
            message: "Đã vượt qua giới hạn đặt chỗ",
            type: SnackBarType.ERROR,
          ));
          return;
        } else {
          showMessageController.sink.add(SnackBarMessage(
            message: "Đã xảy ra lỗi khi đặt chỗ",
            type: SnackBarType.ERROR,
          ));
          return;
        }
      } else {
        showMessageController.sink.add(SnackBarMessage(
          message: "Đã xảy ra lỗi khi đặt chỗ",
          type: SnackBarType.ERROR,
        ));
        return;
      }
    }
  }

  void getApartmentCode() async {
    final lastApartmentId =
        await SharedPreferencesHelper.getLastSelectedApartment();
    emit(state.copyWith(apartmentCodeLoading: LoadStatus.LOADING));

    try {
      ResidentEntity apartmentEntity =
          await propertyRepository!.getApartmentInfo(lastApartmentId);

      emit(
        state.copyWith(
            param: state.param!.copyWith(
              apartmentCode: apartmentEntity.resident?.propertyInfo?.code,
            ),
            apartmentCodeLoading: LoadStatus.SUCCESS,
            apartmentCodeUser:
                apartmentEntity.resident?.propertyInfo?.code ?? ""),
      );
    } catch (e) {
      emit(state.copyWith(apartmentCodeLoading: LoadStatus.FAILURE));
    }
  }

  void getBookingDetail({required String idBooking}) async {
    emit(state.copyWith(getBookingDetailStatus: LoadStatus.LOADING));
    try {
      final detailBookingData = await utilityRepository!
          .getBookingDetailUtility(utilityId: idBooking);
      emit(state.copyWith(
        getBookingDetailStatus: LoadStatus.SUCCESS,
        bookingDetail: detailBookingData,
      ));
    } catch (e, s) {
      logger.e(e);
      logger.e(s);
      emit(state.copyWith(getBookingDetailStatus: LoadStatus.FAILURE));
    }
  }
}
