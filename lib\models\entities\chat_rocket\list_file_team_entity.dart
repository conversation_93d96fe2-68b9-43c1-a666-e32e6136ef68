import 'package:real_care/models/entities/chat_rocket/file_of_team_entity.dart';

class ListFileTeam {
  List<FileOfTeamEntity>? files;
  int? count;
  int? offset;
  int? total;
  bool? success;

  ListFileTeam({this.files, this.count, this.offset, this.total, this.success});

  ListFileTeam.fromJson(Map<String, dynamic> json) {
    if (json['files'] != null) {
      files = <FileOfTeamEntity>[];
      json['files'].forEach((v) {
        files?.add(FileOfTeamEntity.fromJson(v));
      });
    }
    count = json['count'];
    offset = json['offset'];
    total = json['total'];
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    if (this.files != null) {
      data['files'] = this.files?.map((v) => v.toJson()).toList();
    }
    data['count'] = this.count;
    data['offset'] = this.offset;
    data['total'] = this.total;
    data['success'] = this.success;
    return data;
  }
}