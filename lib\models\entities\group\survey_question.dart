import 'package:flutter/cupertino.dart';
import 'package:real_care/models/entities/group/survey_answer.dart';

class SurveyQuestion {
  GlobalKey? key;
  TextEditingController? questionController;
  TextEditingController? answerController;
  String? id;
  List<SurveyAnswer?>? answers;
  bool? isAddOption;
  bool? isMultipleSelection;
  String? createdDate;
  String? name;

  SurveyQuestion({
    this.questionController,
    this.answers,
    this.id,
    this.name,
    this.key,
    this.isAddOption,
    this.createdDate,
    this.isMultipleSelection,
  }) {
    if (this.answers == null) {
      this.answers = [];
    }
    if (this.questionController == null) {
      questionController = TextEditingController(text: name ?? '');
    }
    if (this.answerController == null) {
      answerController = TextEditingController();
    }
    if (this.key == null) {
      key = GlobalKey();
    }
    if (this.isAddOption == null) {
      this.isAddOption = false;
    }
    if (this.isMultipleSelection == null) {
      this.isMultipleSelection = false;
    }
  }

  SurveyQuestion.fromJson(Map<String, dynamic> json) {
    isAddOption = json['isAddOption'];
    isMultipleSelection = json['isMultipleSelection'];
    createdDate = json['createdDate'];
    id = json['id'];
    name = json['name'];
    questionController = TextEditingController();
    answerController = TextEditingController();
    if (json['answers'] != null) {
      answers = [];
      json['answers'].forEach((v) {
        answers!.add(SurveyAnswer.fromJson(v));
      });
    }
  }

  SurveyQuestion.fromWebSocketJson(Map<String, dynamic> json) {
    isAddOption = json['isAddOption'];
    isMultipleSelection = json['isMultipleSelection'];
    createdDate = json['createdDate'];
    id = json['id'];
    name = json['name'];
    answerController = TextEditingController();
    if (json['answers'] != null) {
      answers = [];
      json['answers'].forEach((v) {
        answers!.add(SurveyAnswer.fromWebSocketJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['isAddOption'] = this.isAddOption;
    data['isMultipleSelection'] = this.isMultipleSelection;
    data['name'] = questionController != null ? questionController!.text.trim() : this.name;
    if (this.answers != null) {
      data['answers'] = this.answers!.map((v) => v!.toJson()).toList();
    }
    return data;
  }
}
