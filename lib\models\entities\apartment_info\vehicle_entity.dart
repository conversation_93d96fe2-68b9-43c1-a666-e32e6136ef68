import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:json_annotation/json_annotation.dart';

part 'vehicle_entity.g.dart';

@JsonSerializable()
class VehicleEntity extends Equatable {
  @Json<PERSON>ey(name: '_id')
  String? id_;
  @Json<PERSON>ey()
  String? id;
  @JsonKey()
  String? status;
  @Json<PERSON>ey()
  String? name;
  @Json<PERSON>ey()
  String? reason;
  @JsonKey()
  String? type;
  @Json<PERSON>ey()
  String? labled;
  @JsonKey()
  String? model;
  @Json<PERSON>ey()
  String? color;
  @JsonKey()
  String? licensePlate;
  @JsonKey()
  DateTime? startDate;
  @JsonKey()
  DateTime? expiredDate;
  @JsonKey()
  FileEntity? frontImage;
  @JsonKey()
  FileEntity? backImage;
  @JsonKey()
  String? methodRegister;
  @J<PERSON><PERSON><PERSON>()
  String? cardNumber;
  @Json<PERSON>ey()
  int? seat;

  VehicleEntity({
    this.id_,
    this.id,
    this.type,
    this.status,
    this.name,
    this.backImage,
    this.color,
    this.startDate,
    this.expiredDate,
    this.frontImage,
    this.labled,
    this.licensePlate,
    this.methodRegister,
    this.model,
    this.reason,
    this.cardNumber,
    this.seat,
  });

  factory VehicleEntity.fromJson(Map<String, dynamic> json) => _$VehicleEntityFromJson(json);
  Map<String, dynamic> toJson() => _$VehicleEntityToJson(this);

  VehicleEntity copyWith({
    String? id_,
    String? id,
    String? type,
    String? status,
    String? name,
    FileEntity? backImage,
    String? color,
    DateTime? startDate,
    DateTime? expiredDate,
    FileEntity? frontImage,
    String? labled,
    String? licensePlate,
    String? methodRegister,
    String? model,
    String? reason,
    String? cardNumber,
    int? seat,
  }) {
    return VehicleEntity(
      id_: id_ ?? this.id_,
      id: id ?? this.id,
      type: type ?? this.type,
      status: status ?? this.status,
      name: name ?? this.name,
      backImage: backImage ?? this.backImage,
      color: color ?? this.color,
      startDate: startDate ?? this.startDate,
      expiredDate: expiredDate ?? this.expiredDate,
      frontImage: frontImage ?? this.frontImage,
      labled: labled ?? this.labled,
      licensePlate: licensePlate ?? this.licensePlate,
      methodRegister: methodRegister ?? this.methodRegister,
      model: model ?? this.model,
      reason: reason ?? this.reason,
      cardNumber: cardNumber ?? this.cardNumber,
      seat: seat ?? this.seat,
    );
  }

  String? get getStartDate {
    return DateUtils.toDateAPIString(startDate);
  }

  String? get getExpiredDate {
    return DateUtils.toDateAPIString(expiredDate);
  }

  @override
  List<Object?> get props =>
  [
    id_,
    id,
    type,
    status,
    name,
    backImage,
    color,
    startDate,
    expiredDate,
    frontImage,
    labled,
    licensePlate,
    methodRegister,
    model,
    reason,
    cardNumber,
    seat,
  ];
}