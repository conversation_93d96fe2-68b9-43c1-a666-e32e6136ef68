import 'package:json_annotation/json_annotation.dart';

part 'text_entity.g.dart';

@JsonSerializable()
class TextEntity {
  TextEntity({
    this.id,
    this.name,
    this.level,
    this.info,
    this.personalInfo,
    this.managerId,
    this.parentName,
    this.text,
    this.staffIds,
    this.customerIds,
    this.config,
    this.description,
    this.status,
    this.personalStaffIds,
    this.canAssignStaff,
    this.location,
    this.active,
    this.createdBy,
    this.images,
    this.createdDate,
    this.modifiedBy,
    this.modifiedDate,
  });

  @JsonKey()
  String? id;
  @JsonKey()
  String? name;
  @JsonKey()
  String? level;
  @JsonKey()
  String? info;
  @JsonKey()
  String? personalInfo;
  @JsonKey()
  String? managerId;
  @JsonKey()
  String? parentName;
  @JsonKey()
  String? text;
  @JsonKey()
  String? staffIds;
  @JsonKey()
  String? customerIds;
  @JsonKey()
  String? config;
  @JsonKey()
  String? description;
  @JsonKey()
  String? status;
  @JsonKey()
  String? personalStaffIds;
  @JsonKey()
  String? canAssignStaff;
  @JsonKey()
  String? location;
  @JsonKey()
  String? active;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  String? images;
  @JsonKey()
  String? createdDate;
  @JsonKey()
  String? modifiedBy;
  @JsonKey()
  String? modifiedDate;

  factory TextEntity.fromJson(Map<String, dynamic> json) => _$TextEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TextEntityToJson(this);
}
