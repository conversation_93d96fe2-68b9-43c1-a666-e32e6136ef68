import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:real_care/blocs/app_cubit.dart';
import 'package:real_care/commons/app_colors.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/global/global_data.dart';
import 'package:real_care/main.dart';
import 'package:real_care/models/entities/check_in/health_declaration_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/check_in_repository.dart';
import 'package:real_care/router/application.dart';
import 'package:real_care/router/routers.dart';
import 'package:real_care/ui/components/app_button.dart';
import 'package:real_care/ui/components/app_cache_image.dart';
import 'package:real_care/ui/components/app_page_widget.dart';
import 'package:real_care/ui/pages/check_in/building_picker/shimmer_list_building.dart';
import 'package:real_care/ui/pages/check_in/detail_heath_declaration/detail_heath_declacation_page.dart';
import 'package:real_care/ui/pages/check_in/user/health_declaration/health_declaration_page.dart';
import 'package:real_care/ui/pages/check_in/user/history/check_in_history_cubit.dart';
import 'package:real_care/ui/pages/check_in/user/history/check_in_history_state.dart';
import 'package:real_care/ui/widgets/empty_list_widget.dart';
import 'package:real_care/utils/utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class CheckInHistoryScreen extends StatefulWidget {
  const CheckInHistoryScreen({super.key});

  @override
  State<CheckInHistoryScreen> createState() => _CheckInHistoryScreenState();
}

class _CheckInHistoryScreenState extends State<CheckInHistoryScreen> {
  CheckInHistoryCubit? _cubit;
  late AppCubit _appCubit;

  @override
  void initState() {
    final repository = RepositoryProvider.of<CheckInRepository>(context);
    _cubit = CheckInHistoryCubit(
      repository: repository,
    );
    _appCubit = BlocProvider.of<AppCubit>(context);
    _cubit!.initData(_appCubit.state.user?.id ?? "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppPageWidget(
        title: "Khai báo y tế",
        visibleFooter: false,
        backgroundColor: AppColors.greyE6E6E6,
        child: _buildBodyWidget(),
      ),
    );
  }

  Widget _buildBodyWidget() {
    return BlocBuilder<CheckInHistoryCubit, CheckInHistoryState>(
        bloc: _cubit,
        buildWhen: (prev, current) {
          return prev.loadStatus != current.loadStatus ||
              prev.statusRequireHealthDeclaration !=
                  current.statusRequireHealthDeclaration;
        },
        builder: (context, state) {
          return SizedBox(
            width: double.infinity,
            child: SingleChildScrollView(
              physics: NeverScrollableScrollPhysics(),
              child: Column(
                children: [
                  Container(
                    color: AppColors.background,
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 35),
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 30, bottom: 15),
                          child: state.loadStatus == LoadStatus.LOADING
                              ? _buildShimmer(
                                  circular: 80, width: 132, height: 132)
                              : AppCircleAvatar(
                                  url: _appCubit.state.user?.images?.avatar ?? "",
                                  size: 132,
                                ),
                        ),
                        state.loadStatus == LoadStatus.LOADING
                            ? _buildShimmer(
                                circular: 10, width: 192, height: 17)
                            : Text(_appCubit.state.user?.name ?? "",
                                style: AppTextStyle.blackS16Bold),
                        Visibility(
                          visible: _cubit!.state.numberAge(_appCubit.state.user?.dob ?? null) != 0 ,
                          child: Column(
                            children: [
                              SizedBox(height: 8),
                              state.loadStatus == LoadStatus.LOADING
                                  ? _buildShimmer(
                                      circular: 10, width: 100, height: 17)
                                  : Text(
                                    '${_cubit!.state.numberAge(_appCubit.state.user?.dob ?? null)} Tuổi',
                                      style: AppTextStyle.blackS14Regular),
                            ],
                          ),
                        ),
                        // Visibility(
                        //   visible: state.loadStatus == LoadStatus.LOADING ||
                        //       _cubit!.state
                        //           .teamDivisionCompany(
                        //               team: _appCubit.state.user?.team,
                        //               division: _appCubit.state.user?.division,
                        //               company: _appCubit.state.user?.company)
                        //           .isNotEmpty,
                        //   child: Column(
                        //     children: [
                        //       SizedBox(height: 6),
                        //       state.loadStatus == LoadStatus.LOADING
                        //           ? _buildShimmer(
                        //               circular: 10, width: 210, height: 17)
                        //           : Text(
                        //               _cubit!.state.teamDivisionCompany(
                        //                   team: _appCubit.state.user?.team,
                        //                   division:
                        //                       _appCubit.state.user?.division,
                        //                   company:
                        //                       _appCubit.state.user?.company),
                        //               style: AppTextStyle.blackS14Regular,
                        //               textAlign: TextAlign.center,
                        //             ),
                        //     ],
                        //   ),
                        // ),  // [Fix]: Fix later
                        SizedBox(height: 12),
                        state.loadStatus == LoadStatus.LOADING
                            ? _buildShimmer(
                                circular: 20, width: 214, height: 36)
                            : state.statusRequireHealthDeclaration ==
                                    LoadStatus.SUCCESS
                                ? (state.isRequireHealthDeclaration)
                                    ? SizedBox(
                                        width: 214,
                                        child: AppTintButton(
                                          title: 'Khai báo y tế',
                                          onPressed: () =>
                                              _navigateToHealthDeclarationPage(),
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Text(
                                            "Anh/ Chị không nằm trong danh sách được đến làm việc tại toà nhà hôm nay",
                                            style: AppTextStyle.redS14,
                                            textAlign: TextAlign.center,
                                          ),
                                          SizedBox(height: 8),
                                          RichText(
                                            text: TextSpan(
                                              text:
                                                  'Xin vui lòng liên hệ hotline ',
                                              style: AppTextStyle.blackS14,
                                              children: <TextSpan>[
                                                TextSpan(
                                                  text: GlobalData.instance.config?.hotline ?? "",
                                                  style: AppTextStyle.redS14,
                                                  recognizer:
                                                      TapGestureRecognizer()
                                                        ..onTap = () {
                                                          Utils.launchPhoneCall(
                                                              phone: GlobalData.instance.config?.hotline ?? "");
                                                        },
                                                ),
                                                TextSpan(
                                                  text:
                                                      ' để được hỗ trợ trong trường hợp khẩn cấp.',
                                                  style: AppTextStyle.blackS14,
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      )
                                : Container(),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                  if (GlobalData.instance.config?.isShowCovidPolicy ?? false)
                    GestureDetector(
                      child: Container(
                        color: AppColors.background,
                        padding: EdgeInsets.only(left: 12, bottom: 8),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'Quy định về khai báo y tế',
                          style: AppTextStyle.tintS14Bold.copyWith(
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                      onTap: () {
                        Application.router!
                            .navigateTo(context, Routes.healthDeclarePolicy);
                      },
                    ),
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.only(top: 19, bottom: 12, left: 14),
                    child: Text('Lịch sử khai báo',
                        style: AppTextStyle.blackS16Bold,
                        textAlign: TextAlign.left),
                  ),
                  _buildListCheckInHistory(state.loadStatus)
                ],
              ),
            ),
          );
        });
  }

  Future<bool> onRefresh() async {
    _cubit!.initData(_appCubit.state.user?.id ?? "");
    return true;
  }

  Widget _buildListCheckInHistory(LoadStatus? loadStatus) {
    if (loadStatus == LoadStatus.LOADING) {
      return SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: ShimmerList());
    } else {
      if (loadStatus == LoadStatus.FAILURE) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: EmptyListWidget(
            text: "Đã xảy ra lỗi",
            onRefresh: onRefresh,
            height: MediaQuery.of(context).size.height * 0.40,
          ),
        );
      } else {
        return (_cubit!.state.listHistoryHealthDeclaration?.length ?? 0) == 0
            ? SizedBox(
                height: MediaQuery.of(context).size.height * 0.5,
                child: EmptyListWidget(
                  onRefresh: onRefresh,
                  height: MediaQuery.of(context).size.height * 0.40,
                ),
              )
            : SizedBox(
                height: MediaQuery.of(context).size.height * 0.40,
                child: RefreshIndicator(
                  onRefresh: onRefresh,
                  child: ListView.separated(
                    itemCount: _cubit!.state.listHistoryHealthDeclaration!.length,
                    shrinkWrap: true,
                    physics: AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: 13),
                    itemBuilder: (context, index) {
                      return _itemCheckInHistory(
                          _cubit!.state.listHistoryHealthDeclaration![index]);
                    },
                    separatorBuilder: (context, index) => SizedBox(height: 8),
                  ),
                ),
              );
      }
    }
  }

  Widget _itemCheckInHistory(HealthDeclarationEntity model) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 17),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.all(
          Radius.circular(5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                    text: _cubit!.state.titleDate(model.createdDate),
                    style: AppTextStyle.blackS12
                        .copyWith(color: AppColors.grayIntro)),
                TextSpan(
                  text: _cubit!.state.dateHealthDeclaration(model.createdDate),
                  style: AppTextStyle.tintS12Regular,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 6),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Khai báo y tế', style: AppTextStyle.blackS14Regular),
                GestureDetector(
                  onTap: () {
                    showDialog(
                      context: context,
                      useRootNavigator: true,
                      barrierDismissible: true,
                      useSafeArea: false,
                      builder: (context) =>
                          DetailHeathDeclarationPage(detailId: model.id ?? ''),
                    );
                  },
                  child: Row(
                    children: [
                      Text('Xem chi tiết',
                          style: AppTextStyle.tintS12
                              .copyWith(fontStyle: FontStyle.italic)),
                      SizedBox(width: 5),
                      Image.asset(AppImages.icArrowRightBlack),
                    ],
                  ),
                )
              ],
            ),
          ),
          Row(
            children: [
              Image.asset(
                  model.isValid!
                      ? AppImages.icTickOnline
                      : AppImages.icCloseRedMini,
                  width: 14,
                  height: 14),
              SizedBox(width: 5),
              Text(_cubit!.state.statusHealthDeclaration(model.isValid),
                  style: AppTextStyle.blackS12Regular.copyWith(
                    color:
                        model.isValid! ? AppColors.greenRequest : AppColors.red,
                  )),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildShimmer({required double circular, double? height, double? width}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[400]!,
      highlightColor: Colors.grey.shade100,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(circular),
          color: Colors.white,
        ),
      ),
    );
  }

  void _navigateToHealthDeclarationPage() async {
    final result = await Navigator.push(
      appNavigatorKey.currentContext!,
      MaterialPageRoute(
        builder: (context) => HealthDeclarationPage(),
      ),
    );
    if (result != null && result == "toMyQRCode") {
      Navigator.of(context).pop("toMyQRCode");
    }
  }
}
