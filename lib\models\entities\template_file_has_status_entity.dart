import 'package:json_annotation/json_annotation.dart';

import 'index.dart';

part 'template_file_has_status_entity.g.dart';

@JsonSerializable()
class TemplateFileHasStatus {
  @JsonKey()
  FileEntity? file;
  @JsonKey()
  List<String>? status;
  @JsonKey()
  dynamic stage;

  TemplateFileHasStatus({this.file, this.status, this.stage});

  factory TemplateFileHasStatus.fromJson(Map<String, dynamic> json) => _$TemplateFileHasStatusFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TemplateFileHasStatusToJson(this);
}
