import 'package:real_care/models/entities/check_in/building_info.dart';
import 'package:json_annotation/json_annotation.dart';

part 'building_entity.g.dart';

@JsonSerializable()
class BuildingEntity {
  @JsonKey(name: '_id')
  String? id;
  @JsonKey()
  BuildingInfo? buildingInfo;

  BuildingEntity({this.id, this.buildingInfo});

  factory BuildingEntity.fromJson(Map<String, dynamic> json) => _$BuildingEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$BuildingEntityToJson(this);
}
