import 'env_config.dart';

class AppConfig {
  static const String appName = 'FPT RC';
  static const String accessSystem = ''; // for CarePlus
  static Environment env = Environment.prod;

  ///API Env
  static const String clientId = "real-care-app";
  static const String clientSecret = "snGYx2V1mSAm5yoXT3A8dT9Ybzg0xVrQ";
  static const String redirectUri = "http://real-care-app.datxanh.com.vn/";
  static const String grantType = "authorization_code";
  static String get envName => env.envName;
  static String get webUrl => env.webUrl;
  static const String baseUrl = "https://uat-api-crm.datxanh.com.vn";
  static const String baseUrlSSO = "https://uat-api-sso.datxanh.com.vn";

  ///API header
  static const String apiHeaderXClientCodeKey = 'x-client-code';
  static const String apiHeaderXClientCode = 'REAL_AGENT';
  static const String apiHeaderAppNameKey = 'app-name';
  static const String apiHeaderAppName = 'REAL_CARE';

  ///Paging
  static const pageSize = 20;
  static const pageSizeMax = 1000;

  ///Local
  static const appLocal = 'vi_VN';

  ///DateFormat
  static const dateAPIFormat = 'dd/MM/yyyy';
  static const dateAPIFormatStrikethrough = 'dd-MM-yyyy';
  static const dateDisplayFormat = 'dd/MM/yyyy';
  static const dateTimeAPIFormat = "MM/dd/yyyy'T'hh:mm:ss.SSSZ"; //Use DateTime.parse(date) instead of ...
  static const dateTimeDisplayFormat = 'dd/MM/yyyy HH:mm';
  static const dateTimeDisplayFormatCheckIn = 'HH:mm dd/MM/yyyy';
  static const bbqReservationDateFormat = 'EEE, dd MMM yyyy HH:mm:ss';

  ///Date range
  static final identityMinDate = DateTime(1900, 1, 1);
  static final identityMaxDate = DateTime.now();
  static final birthMinDate = DateTime(1900, 1, 1);
  static final birthMaxDate = DateTime.now();

  ///Font
  static const fontFamily = 'Arimo';

  ///Max file
  static const maxAttachFile = 5;
  static const maxImageFileSize = 5242880;
  static const maxVideoFileSize = 52428800;
  static const maxDocumentFileSize = 10485760;

  ///Share link
  static const shareLink = "https://fpt.vn.link/zXbp";

  ///permissions
  static final permissionsSecurity = 'care.security.qrcode';

  ///Sever target
  static const String appSeverTarget = 'Care Plus';

  //Page size
  static const pageSizeDefault = 6;

}
