import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'member_entity.g.dart';

@JsonSerializable()
class MemberEntity {
  @JsonKey()
  String? id;
  @Json<PERSON>ey()
  String? name;
  @<PERSON><PERSON><PERSON>ey()
  String? status;
  @JsonKey()
  String? groupStatus;
  @JsonKey()
  String? avatar;
  @Json<PERSON>ey()
  PosEntity? pos;

  MemberEntity({
    this.id,
    this.name,
    this.status,
    this.groupStatus,
    this.avatar,
    this.pos,
  });

  String get identifiedStatus {
    if (status == 'IDENTIFIED') {
      return 'Đã xác thực';
    }
    return 'Chưa xác thực';
  }

  factory MemberEntity.fromJson(Map<String, dynamic> json) => _$MemberEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MemberEntityToJson(this);
}
