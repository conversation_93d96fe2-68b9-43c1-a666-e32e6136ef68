import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transaction_history_entity.g.dart';

@JsonSerializable()
class TransactionHistoryEntity extends Equatable {
  @Json<PERSON>ey()
  bool? isToContract;
  @Json<PERSON>ey()
  String? type;
  @JsonKey()
  String? name;
  @Json<PERSON>ey()
  dynamic value;
  @JsonKey()
  Object? expiredDays;
  @<PERSON>son<PERSON><PERSON>()
  dynamic totalAmount;
  @Json<PERSON>ey()
  dynamic totalTransfered;
  @Json<PERSON>ey()
  List<TransactionEntity>? receipts;
  @J<PERSON><PERSON>ey()
  String? paymentDueDate;
  @JsonKey()
  String? descriptionProgress;

  int? get totalAmountValue {
    if (totalAmount is int) return totalAmount;
    if (totalAmount is num) return (totalAmount as num).toInt();
    return int.tryParse(totalAmount ?? '0');
  }

  int? get totalTransferedValue {
    if (totalTransfered is int) return totalTransfered;
    if (totalTransfered is num) return (totalTransfered as num).toInt();
    return int.tryParse(totalTransfered);
  }

  double? get percentValue2 {
    if (type == 'percent') {
      if (value is double) return value;
      if (value is num) return (value as num).toDouble();
      return double.tryParse(value);
    } else {
      return null;
    }
  }

  int? get currencyValue2 {
    if (type == 'currency') {
      if (value is int) return value;
      if (value is num) return (value as num).toInt();
      return int.tryParse(value);
    } else {
      return null;
    }
  }

  TransactionHistoryEntity({
    this.isToContract,
    this.type,
    this.name,
    this.value,
    this.expiredDays,
    this.totalAmount,
    this.totalTransfered,
    this.receipts,
    this.paymentDueDate,
  });

  factory TransactionHistoryEntity.fromJson(Map<String, dynamic> json) =>
      _$TransactionHistoryEntityFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TransactionHistoryEntityToJson(this);

  @override
  List<Object?> get props => [
        this.isToContract,
        this.type,
        this.name,
        this.value,
        this.expiredDays,
        this.totalAmount,
        this.totalTransfered,
        this.receipts,
        this.paymentDueDate,
      ];
}
