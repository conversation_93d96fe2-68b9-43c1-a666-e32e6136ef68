import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/user_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'check_in_user_entity.g.dart';

@JsonSerializable()
class CheckInUserEntity extends Equatable {
  @JsonKey()
  UserEntity? userInfo;
  @JsonKey()
  LastHealthDeclarationEntity? lastHealthDeclaration;
  
  factory CheckInUserEntity.fromJson(Map<String, dynamic> json) => _$CheckInUserEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$CheckInUserEntityToJson(this);

  CheckInUserEntity({
    this.userInfo,
    this.lastHealthDeclaration,
  });

  @override
  List<Object?> get props => [
        this.userInfo,
        this.lastHealthDeclaration,
      ];
}

@JsonSerializable()
class LastHealthDeclarationEntity extends Equatable {
  @J<PERSON><PERSON>ey(name: '_id')
  String? id;
  @J<PERSON><PERSON>ey()
  String? description;
  @JsonKey()
  bool? isValid;
  @JsonKey()
  List<dynamic>? data;
  @JsonKey()
  String? name;
  @JsonKey()
  String? image;
  @JsonKey()
  String? modifiedBy;
  @JsonKey()
  String? createdBy;
  @JsonKey()
  String? userId;
  @JsonKey()
  String? createdDate;
  @JsonKey()
  String? modifiedDate;

  factory LastHealthDeclarationEntity.fromJson(Map<String, dynamic> json) =>
      _$LastHealthDeclarationEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$LastHealthDeclarationEntityToJson(this);

  LastHealthDeclarationEntity({
    this.id,
    this.description,
    this.isValid,
    this.data,
    this.name,
    this.image,
    this.modifiedBy,
    this.createdBy,
    this.userId,
    this.createdDate,
    this.modifiedDate,
  });

  @override
  List<Object?> get props => [
        this.id,
        this.description,
        this.isValid,
        this.data,
        this.name,
        this.image,
        this.modifiedBy,
        this.createdBy,
        this.userId,
        this.createdDate,
        this.modifiedDate,
      ];
}
