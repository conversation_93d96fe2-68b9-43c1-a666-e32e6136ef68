import 'package:json_annotation/json_annotation.dart';

part 'custom_sr_rent_entity.g.dart';

@JsonSerializable()
class CustomSRRentEntity {
  @Json<PERSON>ey()
  String? propertyId;

  @Json<PERSON>ey()
  String? price;

  @Json<PERSON>ey(includeFromJson: false, includeToJson: false)
  String? formatPrice;

  @Json<PERSON>ey()
  String? unit;

  @Json<PERSON>ey()
  String? type;

  @Json<PERSON><PERSON>()
  String? period;

  @Json<PERSON>ey()
  String? deposit;

  @<PERSON><PERSON><PERSON><PERSON>()
  String? note;

  @JsonKey()
  String? province;

  @JsonKey()
  String? district;

  @JsonKey()
  String? ward;

  @Json<PERSON>ey()
  String? address;

  @J<PERSON><PERSON><PERSON>()
  String? customerCode;

  @Json<PERSON>ey()
  List<dynamic>? images;

  @J<PERSON><PERSON><PERSON>()
  List<dynamic>? files;

  CustomSRRentEntity(
      {this.propertyId,
      this.price,
      this.unit,
      this.type,
      this.period,
      this.deposit,
      this.note,
      this.province,
      this.district,
      this.ward,
      this.address,
      this.formatPrice,
      this.customerCode,
      this.images,
      this.files});

  CustomSRRentEntity copyWith({
    String? propertyId,
    String? price,
    String? unit,
    String? type,
    String? period,
    String? deposit,
    String? note,
    String? province,
    String? district,
    String? formatPrice,
    String? ward,
    String? address,
    String? customerCode,
    List<dynamic>? images,
    List<dynamic>? files,
  }) {
    return new CustomSRRentEntity(
        propertyId: propertyId ?? this.propertyId,
        price: price ?? this.price,
        formatPrice: formatPrice ?? this.formatPrice,
        unit: unit ?? this.unit,
        type: type ?? this.type,
        period: period ?? this.period,
        deposit: deposit ?? this.deposit,
        note: note ?? this.note,
        province: province ?? this.province,
        district: district ?? this.district,
        ward: ward ?? this.ward,
        address: address ?? this.address,
        customerCode: customerCode ?? this.customerCode,
        images: images ?? this.images,
        files: files ?? this.files);
  }

  factory CustomSRRentEntity.fromJson(Map<String, dynamic> json) =>
      _$CustomSRRentEntityFromJson(json);

  Map<String, dynamic> toJson() => _$CustomSRRentEntityToJson(this);
}
