import 'package:json_annotation/json_annotation.dart';

part 'hotline_entity.g.dart';

@JsonSerializable()
class HotlineEntity {
  @Json<PERSON>ey()
  String? id;
  @J<PERSON><PERSON>ey()
  String? phone;
  @<PERSON><PERSON><PERSON>ey()
  String? pressNumber;
  @<PERSON><PERSON><PERSON><PERSON>()
  String? description;
  @J<PERSON><PERSON>ey()
  bool? active;

  HotlineEntity({
    this.id,
    this.phone,
    this.pressNumber,
    this.description,
    this.active
  });

  factory HotlineEntity.fromJson(Map<String, dynamic> json) => _$HotlineEntityFromJson(json);
  Map<String, dynamic> toJson() => _$HotlineEntityToJson(this);

  HotlineEntity copyWith({
    String? id,
    String? phone,
    String? pressNumber,
    String? description,
    bool? active,
  }) {
    return HotlineEntity(
      id: id ?? this.id,
      phone: phone ?? this.phone,
      pressNumber: pressNumber ?? this.pressNumber,
      description: description ?? this.description,
      active: active ?? this.active,
    );
  }

  List<Object?> get props =>
  [
    id,
    active,
    description,
    phone,
    pressNumber,
  ];
}