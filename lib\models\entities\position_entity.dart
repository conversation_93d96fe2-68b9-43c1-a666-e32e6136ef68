class PositionEntity {
  String? id;
  String? code;
  String? nameVN;

  PositionEntity({
    this.id,
    this.code,
    this.nameVN
  });

  factory PositionEntity.fromJson(Map<String, dynamic> json) {
    return PositionEntity(
      id: json["id"],
      code: json["code"],
      nameVN: json["nameVN"]
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "code": code,
    "nameVN": nameVN
  };
}
