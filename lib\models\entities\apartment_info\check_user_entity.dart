import 'package:json_annotation/json_annotation.dart';

part 'check_user_entity.g.dart';

@JsonSerializable()
class CheckUserEntity {
  String? phone;
  String? email;
  List<String>? system;

  CheckUserEntity({
    this.phone,
    this.email,
    this.system,
  });

  CheckUserEntity copyWith({
    String? phone,
    String? email,
    List<String>? system,
  }) {
    return CheckUserEntity(
      phone: phone ?? this.phone,
      email: email ?? this.email,
      system: system ?? this.system,
    );
  }

  factory CheckUserEntity.fromJson(Map<String, dynamic> json) => _$CheckUserEntityFromJson(json);
  Map<String, dynamic> toJson() => _$CheckUserEntityToJson(this);
}

@JsonSerializable()
class CheckUserResponse {
  bool? isExistEmail;
  bool? isExistPhone;

  factory CheckUserResponse.fromJson(Map<String, dynamic> json) => _$CheckUserResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CheckUserResponseToJson(this);

  CheckUserResponse({
    this.isExistEmail,
    this.isExistPhone,
  });

  CheckUserResponse copyWith({
    bool? isExistEmail,
    bool? isExistPhone,
  }) {
    return CheckUserResponse(
      isExistEmail: isExistEmail ?? this.isExistEmail,
      isExistPhone: isExistPhone ?? this.isExistPhone,
    );
  }
}