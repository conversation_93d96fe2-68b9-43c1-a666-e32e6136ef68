import 'package:equatable/equatable.dart';
import 'package:real_care/models/entities/customer_entity.dart';
import 'package:real_care/models/entities/index.dart';
import 'package:real_care/models/enums/ycgc_status.dart';
import 'package:json_annotation/json_annotation.dart';
part 'YCGC_entity.g.dart';

@JsonSerializable()
class YCGCEntity extends Equatable {
  @JsonKey()
  String? status;
  @JsonKey()
  List<String>? surveys;
  @Json<PERSON>ey()
  String? id;
  @JsonKey()
  CustomerEntity? customer;
  @Json<PERSON>ey()
  String? bookingTicketCode;
  @Json<PERSON>ey()
  dynamic note;
  @Json<PERSON>ey()
  ProjectEntity? project;
  @JsonKey()
  String? originalStatus;
  @JsonKey()
  int? amountRegistration;
  @JsonKey()
  String? unitCode;

  YCGCStatus get getYCGCStatus {
    return YCGCStatusExtension.fromStatus(status);
  }

  YCGCEntity({
    this.status,
    this.surveys,
    this.id,
    this.customer,
    this.bookingTicketCode,
    this.note,
    this.project,
    this.originalStatus,
    this.amountRegistration,
    this.unitCode,
  });

  factory YCGCEntity.fromJson(Map<String, dynamic> json) =>
      _$YCGCEntityFromJson(json);
  Map<String, dynamic> toJson() => _$YCGCEntityToJson(this);

  @override
  List<Object?> get props => [
        this.status,
        this.surveys,
        this.id,
        this.customer,
        this.bookingTicketCode,
        this.note,
        this.project,
        this.originalStatus,
        this.amountRegistration,
        this.unitCode,
      ];
}
