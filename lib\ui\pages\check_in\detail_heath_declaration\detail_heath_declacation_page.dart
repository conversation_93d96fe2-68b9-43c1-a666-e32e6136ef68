import 'package:flutter/material.dart';
import 'package:real_care/commons/app_images.dart';
import 'package:real_care/commons/app_text_styles.dart';
import 'package:real_care/models/enums/gender_type.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/repositories/check_in_repository.dart';
import 'package:real_care/ui/pages/check_in/detail_heath_declaration/detail_heath_declaration_cubit.dart';
import 'package:real_care/ui/pages/check_in/user/health_declaration/health_declaration_information/health_declaration_infor_widget.dart';
import 'package:real_care/ui/pages/check_in/user/health_declaration/widget/loading_template_widget.dart';
import 'package:real_care/ui/widgets/app_shimmer.dart';
import 'package:real_care/ui/widgets/error_list_widget.dart';
import 'package:real_care/utils/date_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DetailHeathDeclarationPage extends StatefulWidget {
  final String? detailId;

  const DetailHeathDeclarationPage({super.key, this.detailId});

  @override
  State<DetailHeathDeclarationPage> createState() => _DetailHeathDeclarationPageState();
}

class _DetailHeathDeclarationPageState extends State<DetailHeathDeclarationPage> {
  DetailHeathDeclarationCubit? _cubit;

  @override
  void initState() {
    _cubit = DetailHeathDeclarationCubit(repository: RepositoryProvider.of<CheckInRepository>(context));
    _cubit!.getDetailHeathDeclaration(widget.detailId ?? '');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
          ),
          margin: EdgeInsets.symmetric(vertical: 28, horizontal: 12),
          child: Stack(
            children: [
              Container(
                height: double.infinity,
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    SizedBox(height: 24),
                    Text(
                      'KHAI BÁO Y TẾ',
                      style: AppTextStyle.blackS14Bold,
                    ),
                    SizedBox(height: 2),
                    Text(
                      '(Phòng chống dịch Covid-19)',
                      style: AppTextStyle.blackS12,
                    ),
                    SizedBox(height: 10),
                    Expanded(
                      child: BlocBuilder<DetailHeathDeclarationCubit,
                          DetailHeathDeclarationState>(
                        bloc: _cubit,
                        buildWhen: (previous, current) =>
                            previous.getDetailStatus != current.getDetailStatus,
                        builder: (context, state) {
                          switch (state.getDetailStatus) {
                            case LoadStatus.LOADING:
                              return LoadingDetailDeclarationWidget();
                            case LoadStatus.FAILURE:
                              return ErrorListWidget(onRefresh: _onRefresh);
                            default:
                              break;
                          }
                          return _buildBodyWidget;
                        },
                      ),
                    ),
                    SizedBox(height: 20),
                  ],
                ),
              ),
              Positioned(
                right: 0,
                child: GestureDetector(
                  onTap: () {
                    Navigator.pop(context, '');
                  },
                  child: SizedBox(
                    width: 40,
                    height: 40,
                    child: Image.asset(AppImages.icCloseGrey),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget get _buildBodyWidget {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 10),
          Text('Thông tin cá nhân', style: AppTextStyle.tintS14Bold),
          SizedBox(height: 10),
          _informationWidget(
              title: 'Họ và tên',
              information: _cubit!.state.data?.userData?.name ?? ""),
          SizedBox(height: 10),
          _informationWidget(
              title: 'Ngày sinh',
              information:
                  _cubit!.state.data?.userData?.dob?.toDateString() ?? ""),
          SizedBox(height: 10),
          _informationWidget(
              title: 'Giới tính',
              information:
                  _cubit!.state.data?.userData?.genderType?.displayText ?? ''),
          SizedBox(height: 10),
          // _informationWidget(
          //     title: 'Số CMND/ CCCD',
          //     information:
          //         (_cubit!.state.data?.userData?.identities?.length ?? 0) != 0
          //             ? (_cubit!.state.data?.userData?.identities![0].value ??
          //                 "")
          //             : ""),
          // SizedBox(height: 10),
          // _informationWidget(
          //     title: 'Công ty làm việc',
          //     information: _cubit!.state.data?.userData?.company ?? ""),
          // SizedBox(height: 10),
          // _informationWidget(
          //     title: 'Khối',
          //     information: _cubit!.state.data?.userData?.division ?? ""),
          // SizedBox(height: 10),
          // _informationWidget(
          //     title: 'Phòng ban',
          //     information: _cubit!.state.data?.userData?.team ?? ""), // [Fix]: Fix later
          SizedBox(height: 10),
          _informationWidget(
              title: 'Điện thoại',
              information: _cubit!.state.data?.userData?.phone ?? ""),
          SizedBox(height: 10),
          _informationWidget(
              title: 'Địa chỉ Email',
              information: _cubit!.state.data?.userData?.email ?? ""),
          SizedBox(height: 10),
          // _informationWidget(
          //     title: 'Địa chỉ liên hệ',
          //     information: _cubit!.state.data?.userData?.address ?? ""), // [Fix]: Fix later
          SizedBox(height: 20),
          Text('Thông tin khai báo', style: AppTextStyle.tintS14Bold),
          SizedBox(height: 10),
          IgnorePointer(
            child: HealthDeclarationInforWidget(
              listTemplate: _cubit!.state.dataModel?.templateModels ?? [],
              viewOnly: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _informationWidget(
      {required String title, required String information}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
            flex: 3, child: Text(title, style: AppTextStyle.blackS14)),
        Expanded(
            flex: 4,
            child: Text(information,
                style: AppTextStyle.blackS14Bold, textAlign: TextAlign.right)),
      ],
    );
  }

  Future<void> _onRefresh() async {
    _cubit!.getDetailHeathDeclaration(widget.detailId ?? '');
  }
}

class LoadingDetailDeclarationWidget extends StatelessWidget {
  const LoadingDetailDeclarationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 10),
          Text('Thông tin cá nhân', style: AppTextStyle.tintS14Bold),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Họ và tên'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Ngày sinh'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Giới tính'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Số CMND/ CCCD'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Công ty làm việc'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Khối'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Phòng ban'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Điện thoại'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Địa chỉ Email'),
          SizedBox(height: 10),
          _loadingInformationWidget(title: 'Địa chỉ liên hệ'),
          SizedBox(height: 20),
          Text('Thông tin khai báo', style: AppTextStyle.tintS14Bold),
          SizedBox(height: 10),
          LoadingTemplateWidget(isPadding: false),
        ],
      ),
    );
  }

  Widget _loadingInformationWidget({required String title}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
            flex: 3, child: Text(title, style: AppTextStyle.blackS14)),
        Expanded(
            flex: 4, child: AppShimmer(height: 16, width: 60, cornerRadius: 4)),
      ],
    );
  }
}
