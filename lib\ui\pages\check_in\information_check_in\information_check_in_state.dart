import 'package:equatable/equatable.dart';
import 'package:real_care/configs/app_config.dart';
import 'package:real_care/models/entities/check_in/check_in_user_entity.dart';
import 'package:real_care/models/enums/load_status.dart';
import 'package:real_care/utils/date_utils.dart';

class InformationCheckInState extends Equatable {
  LoadStatus? loadStatus;
  CheckInUserEntity? user;
  LoadStatus? loadStatusDeny;
  LoadStatus? loadStatusApprove;
  String reason;

  InformationCheckInState({
    this.loadStatus,
    this.user,
    this.loadStatusDeny,
    this.loadStatusApprove,
    this.reason = "",
  });

  InformationCheckInState copyWith({
    LoadStatus? loadStatus,
    CheckInUserEntity? user,
    LoadStatus? loadStatusDeny,
    LoadStatus? loadStatusApprove,
    String? reason,
  }) {
    return InformationCheckInState(
      loadStatus: loadStatus ?? this.loadStatus,
      user: user ?? this.user,
      loadStatusDeny: loadStatusDeny ?? this.loadStatusDeny,
      loadStatusApprove: loadStatusApprove ?? this.loadStatusApprove,
      reason: reason ?? this.reason,
    );
  }

  String statusHealthDeclaration(bool isValid) {
    if (isValid) {
      return 'Hợp lệ';
    } else {
      return 'Không hợp lệ';
    }
  }

  int numberAge() {
    // [Fix]: Fix later
    // try {
    //   return DateTime.now().year - user!.userInfo!.birthday!.year;
    // } catch (e) {
      return 0;
    // }
  }

  String dateHealthDeclaration({bool isValue = false}) {
    try {
      DateTime date = isValue
          ? DateTime.now()
          : DateTimeExtension.fromString(user!.lastHealthDeclaration!.createdDate,
              format: AppConfig.dateTimeDisplayFormatCheckIn)!;
      return date.toDateString(format: AppConfig.dateTimeDisplayFormatCheckIn).replaceAll(' ', ', ');
    } catch (e) {
      return '';
    }
  }

  String titleDate() {
    try {
      DateTime dateHealthDeclaration = DateTimeExtension.fromString(user!.lastHealthDeclaration!.createdDate)!;
      DateTime currentDate = DateTime.now();
      if (currentDate.day == dateHealthDeclaration.day &&
          currentDate.month == dateHealthDeclaration.month &&
          currentDate.year == dateHealthDeclaration.year) {
        return 'Hôm nay, ';
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  }

  String teamDivisionCompany({String? team, String? division, String? company}) {
    try {
      String? teamState = "";
      String divisionState = "";
      String companyState = "";
      if ((team ?? "").isNotEmpty) {
        teamState = team;
      }
      if ((division ?? "").isNotEmpty) {
        divisionState = ' - $division';
      }
      if ((company ?? "").isNotEmpty) {
        companyState = ' - $company';
      }
      return teamState! + divisionState + companyState;
    } catch (e) {
      return "";
    }
  }


  @override
  List<Object?> get props => [
    loadStatus,
    user,
    loadStatusDeny,
    loadStatusApprove,
    reason,
  ];
}
