import 'index.dart';

import 'package:json_annotation/json_annotation.dart';
//
part 'template_file_has_type.g.dart';

@JsonSerializable()
class TemplateFileHasType {
  @JsonKey()
  FileEntity? file;
  @JsonKey()
  String? type;

  TemplateFileHasType({this.file, this.type});

  factory TemplateFileHasType.fromJson(Map<String, dynamic> json)  => _$TemplateFileHasTypeFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TemplateFileHasTypeToJson(this);
}
