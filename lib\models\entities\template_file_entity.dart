import 'package:json_annotation/json_annotation.dart';

part 'template_file_entity.g.dart';

@JsonSerializable()
class TemplateFiles {
  @Json<PERSON>ey()
  String? name;
  @JsonKey()
  String? url;
  @Json<PERSON>ey()
  String? absoluteUrl;
  @J<PERSON><PERSON>ey()
  String? uploadName;

  TemplateFiles({this.name, this.url, this.absoluteUrl, this.uploadName});
  //
  factory TemplateFiles.fromJson(Map<String, dynamic> json) => _$TemplateFilesFromJson(json);
  //
  Map<String, dynamic> toJson() => _$TemplateFilesToJson(this);
}
