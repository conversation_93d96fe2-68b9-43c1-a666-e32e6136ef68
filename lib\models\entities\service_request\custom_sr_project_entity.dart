import 'package:json_annotation/json_annotation.dart';

part 'custom_sr_project_entity.g.dart';

@JsonSerializable()
class CustomSRProjectEntity {
  @Json<PERSON>ey()
  String? requestType;

  @<PERSON><PERSON><PERSON><PERSON>()
  String? name;

  @<PERSON><PERSON><PERSON>ey()
  String? value;

  @J<PERSON><PERSON>ey()
  String? type;

  @J<PERSON><PERSON>ey()
  List<String>? images;

  @Json<PERSON>ey()
  List<String>? files;

  CustomSRProjectEntity(
      {this.requestType,
      this.name,
      this.value,
      this.type,
      this.images,
      this.files});

  factory CustomSRProjectEntity.fromJson(Map<String, dynamic> json) => _$CustomSRProjectEntityFromJson(json);
  
  Map<String, dynamic> toJson() => _$CustomSRProjectEntityToJson(this);
}
